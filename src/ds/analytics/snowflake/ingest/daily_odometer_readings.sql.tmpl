copy into daily_odometer_readings
(
    -- ingestion time parameters
    filename,
    last_modified_at,
    row_number,
    pipeline_id,

    -- file data
    value,

    -- logical key for a data value (not necessarily UNIQUE)
    connection_id,
    vin,
    version,
    git_sha,
    day,

    -- data attributes
    odometer_reading_min,
    odometer_reading_max
)
from (
    select
        metadata$filename as filename,
        metadata$file_last_modified as last_modified_at,
        metadata$file_row_number as row_number,

        SPLIT_PART(metadata$filename, '/', 2) as pipeline_id,

        ($1::variant) as value,

        ($1:connection_id::varchar) as connection_id,
        ($1:vin::varchar) as vin,
        ($1:version::varchar) as version,
        ($1:git_sha::varchar) as git_sha,
        ($1:day::Date) as day,
        ($1:odometer_reading_min::double) as odometer_reading_min,
        ($1:odometer_reading_max::double) as odometer_reading_max
    from
        @analytics_prod.ds.s3_datascience_ro_stage/metaflow_analytics_data/{{.PipelineId}}/DailyOdometerReadings/
    )
    file_format = (type = 'parquet', compression = 'SNAPPY')
    force = {{.Force}},
    load_uncertain_files = {{.LoadUncertainFiles}}
;
