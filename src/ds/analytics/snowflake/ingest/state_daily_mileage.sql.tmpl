copy into state_daily_mileage
(
    -- ingestion time parameters
    filename,
    last_modified_at,
    row_number,
    pipeline_id,

    -- logical key for a data value (not necessarily UNIQUE)
    connection_id,
    vin,
    version,
    git_sha,
    state_code,
    day,

    -- data attributes
    model_output_distance,
    gps_distance,
    odometer_distance
)
from (
    select
        metadata$filename as filename,
        metadata$file_last_modified as last_modified_at,
        metadata$file_row_number as row_number,

        SPLIT_PART(metadata$filename, '/', 2) as pipeline_id,

        ($1:connection_id::varchar) as connection_id,
        ($1:vin::varchar) as vin,
        ($1:version::varchar) as version,
        ($1:git_sha::varchar) as git_sha,
        ($1:state_code::varchar) as state_code,
        ($1:day::Date) as day,
        ($1:model_output_distance::double) as model_output_distance,
        ($1:gps_distance::double) as gps_distance,
        ($1:odometer_distance::double) as odometer_distance
    from
        @analytics_prod.ds.s3_datascience_ro_stage/metaflow_analytics_data/{{.PipelineId}}/StateDailyMileage/
    )
    file_format = (type = 'parquet', compression = 'SNAPPY')
    force = {{.Force}},
    load_uncertain_files = {{.LoadUncertainFiles}}
;
