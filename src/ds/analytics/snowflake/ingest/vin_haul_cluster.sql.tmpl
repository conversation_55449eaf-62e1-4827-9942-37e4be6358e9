copy into vin_haul_cluster
(
    -- ingestion time parameters
    filename,
    last_modified_at,
    row_number,
    pipeline_id,

    -- logical key for a data value (not necessarily UNIQUE)
    connection_id,
    vin,
    version,
    git_sha,

    -- data attributes
    start_date,
    end_date,
    haul_cluster,
    haul_cluster_label,
    is_short_haul,
    reason,
    model_type,
    features,
    metadata
)
from (
    select
        metadata$filename as filename,
        metadata$file_last_modified as last_modified_at,
        metadata$file_row_number as row_number,

        SPLIT_PART(metadata$filename, '/', 2) as pipeline_id,

        ($1:connection_id::varchar) as connection_id,
        ($1:vin::varchar) as vin,
        ($1:version::varchar) as version,
        ($1:git_sha::varchar) as git_sha,

        to_timestamp_ntz($1:start_date) as start_date,
        to_timestamp_ntz($1:end_date) as end_date,
        ($1:haul_cluster::numeric) as haul_cluster,
        ($1:haul_cluster_label::varchar) as haul_cluster_label,
        ($1:is_short_haul::boolean) as is_short_haul,
        ($1:reason::varchar) as reason,
        ($1:model_type::varchar) as model_type,
        ($1:features::variant) as features,
        ($1:metadata::variant) as metadata
from
        @analytics_prod.ds.s3_datascience_ro_stage/metaflow_analytics_data/{{.PipelineId}}/VinHaulCluster/
    )
    file_format = (type = 'parquet', compression = 'SNAPPY')
    force = {{.Force}},
    load_uncertain_files = {{.LoadUncertainFiles}}
;
