from typing import Any, Dict, List, Type, TypeVar

import attr

T = TypeVar("T", bound="TelematicsConnectionInfo")


@attr.s(auto_attribs=True)
class TelematicsConnectionInfo:
    """
    Attributes:
        tsp (str):  Example: Samsara.
        data_provider (str):  Example: Native.
    """

    tsp: str
    data_provider: str
    additional_properties: Dict[str, Any] = attr.ib(init=False, factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        tsp = self.tsp
        data_provider = self.data_provider

        field_dict: Dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "tsp": tsp,
                "dataProvider": data_provider,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: Type[T], src_dict: Dict[str, Any]) -> T:
        d = src_dict.copy()
        tsp = d.pop("tsp")

        data_provider = d.pop("dataProvider")

        telematics_connection_info = cls(
            tsp=tsp,
            data_provider=data_provider,
        )

        telematics_connection_info.additional_properties = d
        return telematics_connection_info

    @property
    def additional_keys(self) -> List[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
