import copy

import numpy as np
import pandas as pd
import pickle as pkl
import seaborn as sns
import matplotlib.pyplot as plt

from metaflow import Run
from metaflow import card
from metaflow import retry
from metaflow import timeout
from metaflow import current
from metaflow import JSONType
from metaflow import namespace
from metaflow import resources
from metaflow import Parameter
from metaflow.cards import Image
from metaflow.cards import Table
from metaflow.cards import Markdown

from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split

# import nirvana's meta flow base class
# this import should be above all other nirvana imports

from base_flow import NirvanaMetaflow, step, MemoryRequirements

from metaflow_utils.helpers import metaflow_cross_environment_s3

from telematics_utils.config import FilesConfig

# Import modules for data processing
from model_training.risk_score_v1.config import NirvanaRiskScoreConfig
from model_training.risk_score_v1.preprocessing.setup import get_fleet_meta_data
from model_training.risk_score_v1.helpers.milliman_scores import join_milliman_scores

from model_training.risk_score_v1.helpers.feature_transformation.transformation import (
    transform_all_features,
    TransformationPipeline,
)

# Import modules for feature engineering
from model_training.risk_score_v1.features.distance import HaltConfig
from model_training.risk_score_v1.features.distance import DistanceConfig
from model_training.risk_score_v1.features.speed import SpeedFeaturesConfig
from model_training.risk_score_v1.features.timezone_config import TimeFeaturesConfig
from model_training.risk_score_v1.features.hard_braking import HardBrakingFeaturesConfig

# Import modules for training models
from model_training.risk_score_v1.model.tree import xgb_model_bayes
from model_training.risk_score_v1.model.tree import xgb_model_grid
from model_training.risk_score_v1.model.tree import decision_tree_model_bayes
from model_training.risk_score_v1.model.tree import random_forest_model_bayes
from model_training.risk_score_v1.model.tree import hist_gradient_boosting_model_bayes
from model_training.risk_score_v1.model.tree import hist_gradient_boosting_model_grid

from model_training.risk_score_v1.model.linear import poisson_model
from model_training.risk_score_v1.model.linear import linear_regression
from model_training.risk_score_v1.model.linear import fixed_poisson_model

from utils.scoring import PercentileRanker

from model_training.risk_score_v1.visualisation.decile_frequency import (
    decile_frequency_plot,
)
from model_training.risk_score_v1.visualisation.cumulative_profile import (
    plot_cumulative_profile,
)

import telemetry.api.logging as logging

log = logging.getLogger("metaflow")

from dotenv import load_dotenv

load_dotenv()


class NirvanaRiskScoreModelTraining(NirvanaMetaflow):
    """
    This class contains the meta flow for running risk score model experiments
    """
    data_prep_run_id = Parameter(
        "data_prep_run_id",
        help="run id of the data preparation step",
        required=True,
        type=str,
    )

    # Loading optional parameters

    data_prep_namespace = Parameter(
        "data_prep_namespace",
        help="run id of the data preparation step",
        required=False,
        type=str,
    )

    data_prep_env = Parameter(
        "data_prep_env",
        help="metaflow environment in which data prep flow was run. " +
             "This is required when cross_env_data_read is set true",
        required=False,
        type=str,
    )

    cross_env_data_read = Parameter(
        "cross_env_data_read",
        help="run id of the data preparation step",
        required=False,
        type=bool,
        default=False,
    )

    model_config_overrides = Parameter(
        "model_config_overrides",
        # "Modeling Config to control all modeling parameters",
        type=JSONType,
        help="Miscellaneous optional overrides for the metaflow pipeline",
        required=False,
        default={},
    )

    @retry(times=3)
    @timeout(minutes=60)
    @resources(memory=MemoryRequirements.gb(32), cpu=4)
    @card(type="blank")
    @step
    def start(self):
        super().start()

        self.files_config = FilesConfig()
        self.model_config = NirvanaRiskScoreConfig(**dict(self.model_config_overrides))

        self.fleet_meta_data = get_fleet_meta_data(config=self.model_config)

        connection_handle_id_col = self.model_config.FLEET_META_DATA.get(
            "connection_handle"
        )
        self.connection_handle_list = self.fleet_meta_data[
            connection_handle_id_col
        ].unique()

        # Get feature data
        if self.cross_env_data_read is False:
            # Get data preparation flow from parameters
            namespace(self.data_prep_namespace)
            data_preparation_run = Run(self.data_prep_run_id)
            self.feature_dataframe = data_preparation_run.data.feature_dataframe
            self.fleet_harsh_braking_thresh = data_preparation_run.data.fleet_harsh_braking_thresh
            self.milliman_scores_df = data_preparation_run.data.milliman_scores_df
        else:
            if self.data_prep_env is None:
                raise ValueError("data_prep_env parameter is required when cross_env_data_read is True")
            else:
                with metaflow_cross_environment_s3(env=self.data_prep_env, run_id=self.data_prep_run_id) as s3:
                    feature_dataframe_obj = s3.get('feature_dataframe.pkl')
                    # path in local filesystem where it was downloaded
                    print("feature_dataframe path: ", feature_dataframe_obj.path)
                    self.feature_dataframe = pkl.loads(feature_dataframe_obj.blob)

                    fleet_harsh_braking_thresh_obj = s3.get('fleet_harsh_braking_thresh.pkl')
                    self.fleet_harsh_braking_thresh = pkl.loads(fleet_harsh_braking_thresh_obj.blob)

                    milliman_scores_df_obj = s3.get('milliman_scores_df.pkl')
                    self.milliman_scores_df = pkl.loads(milliman_scores_df_obj.blob)

        if self.model_config.MAX_HARSH_BRAKE_THREHSOLD is not None:
            log.info("Removing high custom threshold accounts")
            self.high_threshold_connections = self.fleet_harsh_braking_thresh[
                self.fleet_harsh_braking_thresh.harsh_brake_sensitivity_gForce > self.model_config.MAX_HARSH_BRAKE_THREHSOLD
                ].connection_id.unique()

            self.feature_dataframe = self.feature_dataframe[
                ~self.feature_dataframe[self.model_config.CONNECTION_ID].isin(self.high_threshold_connections)
            ]
        else:
            log.info("Skipped custom gForce threshold account removal")

        print("Shape of the dataframe", self.feature_dataframe.shape)

        print(
            "Count of connection handles Id",
            self.feature_dataframe.connection_id.unique().shape,
        )

        print("Count of VINs", self.feature_dataframe.vin.unique().shape)

        # Adding cards with the required information
        current.card.append(Markdown(f"# Data Prep Run Features"))
        current.card.append(
            Table.from_dataframe(self.feature_dataframe),
        )
        self.all_feature_sets = {
            self.model_config.DISTANCE: DistanceConfig.FEATURE_NAMES,
            self.model_config.SPEED: (
                SpeedFeaturesConfig.MAX_FEATURE_NAMES
                + SpeedFeaturesConfig.AVG_FEATURE_NAMES
            ),
            self.model_config.TIME_OF_DAY: TimeFeaturesConfig.TOD_FEATURE_NAMES_FINAL,
            self.model_config.HARSH_BRAKING: HardBrakingFeaturesConfig.GFORCE_FEATURE_NAMES,
            self.model_config.HALT: HaltConfig.FEATURE_NAMES,
        }

        self.feature_sets = {
            key: val for (key, val) in self.all_feature_sets.items() if key in self.model_config.VALID_FEATURE_CATEGORIES
        }
        self.feature_set_keys = list(self.feature_sets.keys())

        super().add_tags(
            "model_name:{model_name}".format(model_name=self.model_config.MODEL_NAME)
        )

        for key, val in dict(self.model_config_overrides).items():
            super().add_tags("{}:{}".format(key, val))

        self.next(self.analyse)

    @retry(times=0)
    @card(type="blank")
    @resources(memory=MemoryRequirements.gb(32), cpu=4)
    @step
    def analyse(self):
        self.select_df = (
            self.feature_dataframe[
                self.feature_dataframe[self.model_config.TARGET_COLUMN].notnull()
                & (
                    self.feature_dataframe[self.model_config.TOTAL_MILES]
                    >= self.model_config.MIN_MILES
                )
                & (
                    self.feature_dataframe[self.model_config.TOTAL_MILES_EVAL]
                    >= self.model_config.MIN_MILES
                )
            ]
            .pivot_table(
                index=self.model_config.CONNECTION_ID,
                values=[self.model_config.ACC_PER_10K_MILES, self.model_config.TOTAL_MILES_EVAL],
                aggfunc=np.mean,
            )
            .reset_index()
        )

        self.select_df["acc_qunatiles"] = pd.qcut(
            self.select_df[self.model_config.ACC_PER_10K_MILES], q=10, duplicates="drop", labels=False
        )

        self.train_connections, self.test_connections = train_test_split(
            self.select_df[self.model_config.CONNECTION_ID].values,
            random_state=self.model_config.RANDOM_STATE,
            train_size=self.model_config.TRAIN_SIZE,
            stratify=self.select_df["acc_qunatiles"].values,
        )

        self.blind_connections, self.test_connections = train_test_split(
            self.select_df[
                self.select_df[self.model_config.CONNECTION_ID].isin(self.test_connections)
            ][self.model_config.CONNECTION_ID].values,
            random_state=self.model_config.RANDOM_STATE,
            train_size=self.model_config.BLIND_SIZE,
            stratify=self.select_df[
                self.select_df[self.model_config.CONNECTION_ID].isin(self.test_connections)
            ]["acc_qunatiles"].values
        )

        # Meta information about the flow
        current.card.append(
            Markdown(
                "# Count of training connections: {}".format(
                    len(self.train_connections)
                )
            )
        )

        current.card.append(
            Markdown(
                "# Count of test connections: {}".format(len(self.test_connections))
            )
        )

        current.card.append(
            Markdown(
                "# Count of blind connections: {}".format(len(self.blind_connections))
            )
        )

        for split in [
            self.train_connections,
            self.test_connections,
            self.blind_connections,
        ]:
            val_idx = (
                (self.feature_dataframe.total_distance >= self.model_config.MIN_MILES)
                & (self.feature_dataframe.connection_id.isin(split))
                & (
                    self.feature_dataframe[self.model_config.TOTAL_MILES_EVAL]
                    >= self.model_config.MIN_MILES
                )
                & self.feature_dataframe[self.model_config.TOTAL_ACCIDENTS].notnull()
            )

            # Filtering the feature dataframe to only contain training intances
            self.split_dataframe = self.feature_dataframe[val_idx]
            self.split_dataframe = self.split_dataframe.reset_index(drop=True)

            # Transformation to be applied to the feature dataframe

            for self.feature_category in self.feature_sets.keys():
                self.feature_list = self.feature_sets.get(self.feature_category)
                current.card.append(
                    Markdown(
                        "# Feature correlation analysis: {}".format(
                            self.feature_category
                        )
                    )
                )
                fig = plt.figure(figsize=(16, 10))
                sns.heatmap(
                    self.split_dataframe[self.feature_list].corr().abs(),
                    annot=True,
                    cmap="hot",
                )
                plt.title(
                    "Correlation between {} features".format(self.feature_category)
                )
                plt.tight_layout()
                current.card.append(Image.from_matplotlib(fig))

                current.card.append(
                    Markdown(
                        "# Feature distribution analysis: {}".format(
                            self.feature_category
                        )
                    )
                )
                fig, ax = plt.subplots(figsize=(16, 10))
                self.split_dataframe[self.feature_list].hist(
                    figsize=(16, 10), bins=100, ax=ax
                )

                current.card.append(Image.from_matplotlib(fig))

                norm_features = list()
                used_features = list()

                fig = plt.figure(figsize=(16, 10))
                fig.suptitle(
                    "Feature relationship with target (Accidents per Million miles)"
                )

                i = 0

                # Performing eda for all the feature catefories
                for feature in self.feature_list:
                    min_val = self.split_dataframe[feature].quantile(0.03)
                    max_val = self.split_dataframe[feature].quantile(0.97)

                    row_number = i // 3
                    col_number = i % 3

                    ax = plt.subplot2grid(
                        (3, 3), (row_number, col_number), colspan=1, rowspan=1, fig=fig
                    )

                    if min_val == max_val:
                        continue

                    # Create quantile plots
                    decile_feature = "{}_deciles".format(feature)
                    self.split_dataframe[decile_feature] = np.nan
                    self.split_dataframe[decile_feature] = pd.qcut(
                        self.split_dataframe[feature],
                        q=10,
                        labels=False,
                        duplicates="drop",
                    )

                    temp_var = self.split_dataframe.pivot_table(
                        index=decile_feature,
                        values=[
                            self.model_config.TOTAL_ACCIDENTS,
                            self.model_config.TOTAL_MILES_EVAL,
                        ],
                        aggfunc=np.nansum,
                    )

                    if temp_var.shape[0] > 0:
                        print(temp_var.head())
                        temp_var["acc_per_mile"] = (
                            temp_var[self.model_config.TOTAL_ACCIDENTS] * 1e6
                        ) / temp_var[self.model_config.TOTAL_MILES_EVAL]
                        ax.plot(temp_var.index, temp_var["acc_per_mile"], label=feature)
                        ax.hlines(
                            temp_var[self.model_config.TOTAL_ACCIDENTS].sum()
                            * 1000000
                            / temp_var[self.model_config.TOTAL_MILES_EVAL].sum(),
                            xmin=0,
                            xmax=temp_var.shape[0],
                            color="black",
                            label="Overall",
                        )

                        ax2 = ax.twinx()
                        ax2.plot(
                            temp_var.index,
                            temp_var[self.model_config.TOTAL_MILES_EVAL],
                            color="r",
                            label="Total Distance",
                        )
                        ax2.set_ylabel("Total Distance (miles)")
                        ax.set_xlabel("Decile")
                        ax.set_ylabel("Accidents per Million miles")
                        ax.grid()
                        ax.set_title("Feature: {}".format(feature))

                    del self.split_dataframe["{}_deciles".format(feature)]

                    print(
                        "Normalisation Parameters: {}".format(feature), min_val, max_val
                    )
                    norm_feature = "norm_{}".format(feature)
                    self.split_dataframe[norm_feature] = (
                        self.split_dataframe[feature] - min_val
                    ) / (max_val - min_val)
                    self.split_dataframe[norm_feature] = self.split_dataframe[
                        norm_feature
                    ].clip(0, 1)
                    norm_features.append(norm_feature)
                    used_features.append(feature)
                    i += 1

                fig.tight_layout()

                current.card.append(
                    Markdown(
                        "# Feature correlation target: {}".format(self.feature_category)
                    )
                )
                current.card.append(Image.from_matplotlib(fig))

                print("Normalised null counts")
                print(self.split_dataframe[norm_features].isna().sum())

                print("Normalised Description")
                print(self.split_dataframe[norm_features].describe())

                current.card.append(
                    Markdown(
                        "# Feature correlation analysis (normalised): {}".format(
                            self.feature_category
                        )
                    )
                )
                fig = plt.figure(figsize=(16, 10))
                sns.heatmap(
                    self.split_dataframe[norm_features].corr().abs(),
                    annot=True,
                    cmap="hot",
                )
                plt.title(
                    "Correlation between normalised {} features".format(
                        self.feature_category
                    )
                )
                plt.tight_layout()

                current.card.append(Image.from_matplotlib(fig))

                current.card.append(
                    Markdown(
                        "# Feature distribution analysis normalised: {}".format(
                            self.feature_category
                        )
                    )
                )
                fig, ax = plt.subplots(figsize=(16, 10))
                self.split_dataframe[norm_features].hist(
                    figsize=(16, 10), bins=100, ax=ax
                )

                current.card.append(Image.from_matplotlib(fig))

                current.card.append(
                    Markdown(
                        "# PCA analysis normalised: {}".format(self.feature_category)
                    )
                )

                fig, ax = plt.subplots(figsize=(16, 10))
                pca = PCA().fit(self.split_dataframe[norm_features])

                # Plotting explained variance ratio per feature and cumulative explained variance ratio
                pd.DataFrame(
                    np.c_[
                        pca.explained_variance_ratio_,
                        np.cumsum(pca.explained_variance_ratio_),
                    ],
                    columns=[
                        "Explained Variance Ratio",
                        "Cumulative Explained Variance Ratio",
                    ],
                ).plot(figsize=(14, 8), ax=ax)
                ax.hlines(
                    0.95,
                    xmin=0,
                    xmax=pca.explained_variance_ratio_.shape[0],
                    color="black",
                    label="95%",
                )

                plt.title(
                    "Principal Component Analysis: {}".format(self.feature_category)
                )
                plt.xlabel("Number of Components")
                plt.ylabel("Explained Variance Ratio")
                plt.grid()
                plt.legend()
                plt.tight_layout()

                current.card.append(Image.from_matplotlib(fig))

                # Remove newly created features
                for feature in norm_features:
                    del self.split_dataframe[feature]

        self.next(self.transform)

    @resources(memory=MemoryRequirements.gb(16), cpu=2)
    @step
    def transform(self):
        # Create a validation index containing connections that have travelled more than minimum miles and are in the
        # training set. These will be used for all the analysis.

        val_idx = (
            (self.feature_dataframe[self.model_config.TOTAL_MILES] >= self.model_config.MIN_MILES)
            & (self.feature_dataframe[self.model_config.CONNECTION_ID].isin(self.train_connections))
            & ( 
                self.feature_dataframe[self.model_config.TOTAL_MILES_EVAL] >= self.model_config.MIN_MILES
            )
            & self.feature_dataframe[self.model_config.TOTAL_ACCIDENTS].notnull()
        )

        # Filtering the feature dataframe to only contain training intances
        self.train_dataframe = self.feature_dataframe[val_idx]
        self.train_dataframe = self.train_dataframe.reset_index(drop=True)

        self.target = self.train_dataframe[self.model_config.TARGET_COLUMN]

        self.min_val_quantile = 0.03
        self.max_val_quantile = 0.97

        self.transformed_training_features, self.transformation_params = transform_all_features(
            feature_df=self.train_dataframe,
            feature_sets=self.feature_sets,
            transformation_params=None,
            min_val_quantile=self.min_val_quantile,
            max_val_quantile=self.max_val_quantile,
        )

        val_idx = (
            (self.feature_dataframe.total_distance >= self.model_config.MIN_MILES)
            & self.feature_dataframe.connection_id.isin(self.test_connections)
            & (
                self.feature_dataframe[self.model_config.TOTAL_MILES_EVAL]
                >= self.model_config.MIN_MILES
            )
            & self.feature_dataframe[self.model_config.TOTAL_ACCIDENTS].notnull()
        )

        self.test_dataframe = self.feature_dataframe[val_idx]
        self.test_dataframe = self.test_dataframe.reset_index(drop=True)

        # Creating blind set dataframe

        val_idx = (
            (self.feature_dataframe.total_distance >= self.model_config.MIN_MILES)
            & self.feature_dataframe.connection_id.isin(self.blind_connections)
            & (
                self.feature_dataframe[self.model_config.TOTAL_MILES_EVAL]
                >= self.model_config.MIN_MILES
            )
            & self.feature_dataframe[self.model_config.TOTAL_ACCIDENTS].notnull()
        )

        self.blind_dataframe = self.feature_dataframe[val_idx]
        self.blind_dataframe = self.blind_dataframe.reset_index(drop=True)

        log.info("Rows in test dataframe: {}".format(self.test_dataframe.shape[0]))

        self.transformed_test_features, _ = transform_all_features(
            feature_df=self.test_dataframe,
            feature_sets=self.feature_sets,
            transformation_params=self.transformation_params,
            min_val_quantile=self.min_val_quantile,
            max_val_quantile=self.max_val_quantile,
        )

        log.info(
            "Rows in transformed test dataframe: {}".format(
                self.transformed_test_features.shape[0]
            )
        )

        self.transformed_blind_features, _ = transform_all_features(
            feature_df=self.blind_dataframe,
            feature_sets=self.feature_sets,
            transformation_params=self.transformation_params,
            min_val_quantile=self.min_val_quantile,
            max_val_quantile=self.max_val_quantile,
        )

        self.list_models = [
            # linear_regression,
            fixed_poisson_model,
            # hist_gradient_boosting_model_grid,
            # hist_gradient_boosting_model_bayes,
        ]

        self.next(self.train, foreach="list_models")

    @resources(memory=MemoryRequirements.gb(32), cpu=4)
    @step
    def train(self):
        self.model_func = self.input
        self.training_feature_cols = self.transformed_training_features.columns.tolist()
        self.train_data = self.transformed_training_features.values
        self.train_target = self.target.values

        model, params = self.model_func(
            train_data=self.train_data,
            train_target=self.train_target,
            random_state=self.model_config.RANDOM_STATE,
            cross_validation_folds=self.model_config.CV,
        )

        self.model_dict = {
            self.model_func.__name__: {
                "model": model,
                "feature_cols": self.training_feature_cols,
                "params": params,
            }
        }

        self.next(self.evaluate)

    @retry(times=2)
    @resources(memory=MemoryRequirements.gb(16), cpu=2)
    @card(type="blank")
    @step
    def evaluate(self, inputs):
        self.model_dict_list = list()
        for input in inputs:
            self.model_dict_list.append(input.model_dict)

        parallel_step_parameters = [
            "model_dict",
            "model_func",
            "training_feature_cols",
            "train_data",
            "train_target",
        ]
        self.merge_artifacts(inputs, exclude=parallel_step_parameters)
        
        log.info("Milliman scores pulled")
        log.info(
            "Rows in train dataframe before joining milliman scores: {}".format(
                self.train_dataframe.shape[0]
            )
        )
        self.train_dataframe = join_milliman_scores(
            feature_dataframe=self.train_dataframe,
            training_start_col="feature_end",
            training_end_col="evaluation_end",
            milliman_score=self.milliman_scores_df,
        )

        self.train_dataframe[self.model_config.ADJUSTED_MILLIMAN_SCORE] = (
            1000 - self.train_dataframe[self.model_config.MILLIMAN_SCORE]
        )
        self.train_dataframe.loc[
            self.train_dataframe[self.model_config.MILLIMAN_SCORE] <= 1,
            self.model_config.ADJUSTED_MILLIMAN_SCORE,
        ] = np.nan

        log.info(
            "Rows in train dataframe after joining milliman scores: {}".format(
                self.train_dataframe.shape[0]
            )
        )

        log.info(
            "Rows in test dataframe before joining milliman scores: {}".format(
                self.test_dataframe.shape[0]
            )
        )

        self.test_dataframe = join_milliman_scores(
            feature_dataframe=self.test_dataframe,
            training_start_col="feature_end",
            training_end_col="evaluation_end",
            milliman_score=self.milliman_scores_df,
        )

        self.test_dataframe[self.model_config.ADJUSTED_MILLIMAN_SCORE] = (
            1000 - self.test_dataframe[self.model_config.MILLIMAN_SCORE]
        )
        self.test_dataframe.loc[
            self.test_dataframe[self.model_config.MILLIMAN_SCORE] <= 1,
            self.model_config.ADJUSTED_MILLIMAN_SCORE,
        ] = np.nan

        log.info(
            "Rows in test dataframe after joining milliman scores: {}".format(
                self.test_dataframe.shape[0]
            )
        )

        print("Test columns", self.test_dataframe.columns.tolist())

        self.blind_dataframe = join_milliman_scores(
            feature_dataframe=self.blind_dataframe,
            training_start_col="feature_end",
            training_end_col="evaluation_end",
            milliman_score=self.milliman_scores_df,
        )

        self.blind_dataframe[self.model_config.ADJUSTED_MILLIMAN_SCORE] = (
            1000 - self.blind_dataframe[self.model_config.MILLIMAN_SCORE]
        )
        self.blind_dataframe.loc[
            self.blind_dataframe[self.model_config.MILLIMAN_SCORE] <= 1,
            self.model_config.ADJUSTED_MILLIMAN_SCORE,
        ] = np.nan

        self.train_results = self.train_dataframe[self.model_config.RESULTS_DF_COLUMNS]
        self.test_results = self.test_dataframe[self.model_config.RESULTS_DF_COLUMNS]
        self.blind_results = self.blind_dataframe[self.model_config.RESULTS_DF_COLUMNS]

        for model_dict in self.model_dict_list:
            for key in model_dict.keys():
                training_input = self.transformed_training_features[
                    model_dict[key]["feature_cols"]
                ]

                try:
                    y_train_hat = model_dict[key]["model"].predict(training_input)
                except:
                    continue

                self.train_results[key] = np.nan
                self.train_results[key] = y_train_hat

                testing_input = self.transformed_test_features[
                    model_dict[key]["feature_cols"]
                ]
                y_test_hat = model_dict[key]["model"].predict(testing_input)
                self.test_results[key] = np.nan
                self.test_results[key] = y_test_hat

                blind_input = self.transformed_blind_features[
                    model_dict[key]["feature_cols"]
                ]
                y_blind_hat = model_dict[key]["model"].predict(blind_input)
                self.blind_results[key] = np.nan
                self.blind_results[key] = y_blind_hat

        self.train_results[self.model_config.RANDOM_MODEL] = (
            self.train_results[self.model_config.TOTAL_ACCIDENTS] * 1e3
        ) / self.train_results[self.model_config.TOTAL_MILES_EVAL]

        self.test_results[self.model_config.RANDOM_MODEL] = (
            self.test_results[self.model_config.TOTAL_ACCIDENTS] * 1e3
        ) / self.test_results[self.model_config.TOTAL_MILES_EVAL]

        self.blind_results[self.model_config.RANDOM_MODEL] = (
            self.blind_results[self.model_config.TOTAL_ACCIDENTS] * 1e3
        ) / self.blind_results[self.model_config.TOTAL_MILES_EVAL]

        df_list = {
            "train": self.train_results,
            "test": self.test_results,
            "blind": self.blind_results,
        }

        self.predicted_cols = [val.__name__ for val in self.list_models] + [
            self.model_config.ADJUSTED_MILLIMAN_SCORE,
            self.model_config.RANDOM_MODEL,
        ]

        i = 0
        fig = plt.figure(figsize=(16, 16))
        for key in df_list:
            ax = plt.subplot2grid((len(df_list), 1), (i, 0), fig=fig)
            considered_df = df_list[key]
            plot_cumulative_profile(
                result_dataframe=considered_df,
                prediction_cols=self.predicted_cols,
                target_col=self.model_config.TOTAL_ACCIDENTS,
                normalise_col=self.model_config.TOTAL_MILES_EVAL,
                normalise=True,
                ax=ax,
                ascending=False,
            )
            ax.set_title(f"{key} set")
            i += 1
        fig.suptitle("Cummulative Profile - Total miles vs Total Accidents")
        fig.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        fig, decile_data = decile_frequency_plot(
            df_list=df_list,
            models=self.predicted_cols,
            accidents=self.model_config.TOTAL_ACCIDENTS,
            distance=self.model_config.TOTAL_MILES_EVAL,
            figsize=(16, 16),
            title="Decile Frequency Plot",
        )
        
        current.card.append(Image.from_matplotlib(fig))
        current.card.append(Table.from_dataframe(decile_data, truncate=False))
        self.next(self.tune_poisson_model)

    @card(type="blank")
    @step
    def tune_poisson_model(self):
        self.untuned_model = None
        for model_dict in self.model_dict_list:
            for key in model_dict.keys():
                if key == fixed_poisson_model.__name__:
                    self.untuned_model_dict = model_dict
                    break

        self.untuned_model = self.untuned_model_dict[fixed_poisson_model.__name__][
            "model"
        ]

        current.card.append(Markdown(f"# Un-tuned Poisson Model"))

        # Plotting the feature importance of the untuned model
        fig, ax = plt.subplots(figsize=(16, 10))
        pd.DataFrame(
            (self.untuned_model.coef_) / np.abs(self.untuned_model.coef_).sum(),
            index=self.transformed_training_features.columns,
        ).abs().sort_values(by=0, ascending=False).plot(kind="bar", ax=ax, rot=45)

        plt.title("Feature importance of Untuned Poisson Model")
        plt.ylabel("Feature Weightage")
        plt.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        # Plotting the feature correlations of the untuned model
        fig = plt.figure(figsize=(16, 10))
        sns.heatmap(
            self.transformed_training_features.corr().abs().round(2),
            annot=True,
            cmap="hot",
        )
        plt.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        # Model tuning:
        self.tuned_feature_sets = {
            key: val for (key, val) in self.feature_sets.items() if key in self.model_config.TUNED_FEATURE_CATEGORIES
        }

        self.transformation_pipeline = TransformationPipeline()

        self.tuned_transformed_training_features = (
            self.transformation_pipeline.fit_transform(
                feature_sets=self.tuned_feature_sets,
                feature_data=self.train_dataframe,
                scaling=False,
            )
        )

        self.tuned_transformed_test_features = self.transformation_pipeline.transform(
            data=self.test_dataframe,
        )

        self.tuned_transformed_blind_features = self.transformation_pipeline.transform(
            data=self.blind_dataframe,
        )

        self.selected_features = (
            self.tuned_transformed_training_features.columns.difference(
                self.model_config.FEATURES_TO_EXCLUDE
            ).tolist()
        )
        self.tuned_train_data = self.tuned_transformed_training_features[
            self.selected_features
        ].values

        self.tuned_train_target = self.target.values

        tuned_poisson_model, params = fixed_poisson_model(
            train_data=self.tuned_train_data,
            train_target=self.tuned_train_target,
            random_state=self.model_config.RANDOM_STATE,
            cross_validation_folds=6,
            sample_weight=np.power(
                self.train_dataframe[self.model_config.TOTAL_MILES].values, 2
            ),
            alpha=self.model_config.TUNED_POISSON_MODEL_PARAMS.get("alpha"),
            max_iter=self.model_config.TUNED_POISSON_MODEL_PARAMS.get("max_iter"),
            solver=self.model_config.TUNED_POISSON_MODEL_PARAMS.get("solver")
        )

        self.tuned_model_dict = {
            fixed_poisson_model.__name__: {
                "model": tuned_poisson_model,
                "feature_cols": self.selected_features,
                "params": params,
            }
        }

        self.tuned_model = self.tuned_model_dict[fixed_poisson_model.__name__]["model"]

        current.card.append(Markdown(f"# Tuned Poisson Model"))

        # Plotting the feature importance of the untuned model
        fig, ax = plt.subplots(figsize=(16, 10))
        pd.DataFrame(
            (self.tuned_model.coef_) / np.abs(self.tuned_model.coef_).sum(),
            index=self.selected_features,
        ).abs().sort_values(by=0, ascending=False).plot(kind="bar", ax=ax, rot=45)

        plt.title("Feature importance of tuned Poisson Model")
        plt.ylabel("Feature Weightage")
        plt.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        # Plotting the feature correlations of the untuned model
        fig = plt.figure(figsize=(16, 10))
        sns.heatmap(
            self.tuned_transformed_training_features[self.selected_features]
            .corr()
            .abs()
            .round(2),
            annot=True,
            cmap="hot",
        )
        plt.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        current.card.append(Markdown("# Tuned Performance Metrics"))

        # Create training results and metrics
        self.tuned_train_results = self.train_dataframe[
            self.model_config.RESULTS_DF_COLUMNS
        ]
        self.tuned_test_results = self.test_dataframe[
            self.model_config.RESULTS_DF_COLUMNS
        ]
        self.tuned_blind_results = self.blind_dataframe[
            self.model_config.RESULTS_DF_COLUMNS
        ]

        # Add poisson_results
        self.tuned_train_results["tuned_poisson"] = self.tuned_model.predict(
            self.tuned_transformed_training_features[self.selected_features].values
        )
        self.tuned_test_results["tuned_poisson"] = self.tuned_model.predict(
            self.tuned_transformed_test_features[self.selected_features].values
        )
        self.tuned_blind_results["tuned_poisson"] = self.tuned_model.predict(
            self.tuned_transformed_blind_features[self.selected_features].values
        )

        self.predicted_cols_final = [
            self.model_config.ADJUSTED_MILLIMAN_SCORE,
            "tuned_poisson",
        ]

        self.df_list = {
            "train": self.tuned_train_results,
            "test": self.tuned_test_results,
            "blind": self.tuned_blind_results,
        }

        fig, self.decile_data = decile_frequency_plot(
            df_list=self.df_list,
            models=self.predicted_cols_final,
            accidents=self.model_config.TOTAL_ACCIDENTS,
            distance=self.model_config.TOTAL_MILES_EVAL,
            figsize=(16, 16),
            title="Decile Frequency Plot",
        )
        current.card.append(Table.from_dataframe(self.decile_data, truncate=False))
        current.card.append(Image.from_matplotlib(fig))

        # Cumulative accuracy profile
        i = 0
        fig = plt.figure(figsize=(16, 16))
        for key in self.df_list:
            ax = plt.subplot2grid((len(self.df_list), 1), (i, 0), fig=fig)
            considered_df = self.df_list[key]
            plot_cumulative_profile(
                result_dataframe=considered_df,
                prediction_cols=self.predicted_cols_final,
                target_col=self.model_config.TOTAL_ACCIDENTS,
                normalise_col=self.model_config.TOTAL_MILES_EVAL,
                normalise=True,
                ax=ax,
                ascending=False,
            )
            ax.set_title(f"{key} set")
            i += 1
        fig.suptitle("Cummulative Profile - Total miles vs Total Accidents")
        fig.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        # Plotting only the intersection segment
        self.df_list_intersection = {
            "train": self.tuned_train_results.dropna(subset=self.predicted_cols_final),
            "test": self.tuned_test_results.dropna(subset=self.predicted_cols_final),
            "blind": self.tuned_blind_results.dropna(subset=self.predicted_cols_final),
        }

        fig, self.decile_data_intersection = decile_frequency_plot(
            df_list=self.df_list_intersection,
            models=self.predicted_cols_final,
            accidents=self.model_config.TOTAL_ACCIDENTS,
            distance=self.model_config.TOTAL_MILES_EVAL,
            figsize=(16, 16),
            title="Decile Frequency Plot (Intersection)",
        )

        current.card.append(
            Table.from_dataframe(self.decile_data_intersection, truncate=False)
        )
        current.card.append(Image.from_matplotlib(fig))

        # Cumulative accuracy profile
        i = 0
        fig = plt.figure(figsize=(16, 16))
        for key in self.df_list_intersection:
            ax = plt.subplot2grid((len(self.df_list_intersection), 1), (i, 0), fig=fig)
            considered_df = self.df_list_intersection[key]
            plot_cumulative_profile(
                result_dataframe=considered_df,
                prediction_cols=self.predicted_cols_final,
                target_col=self.model_config.TOTAL_ACCIDENTS,
                normalise_col=self.model_config.TOTAL_MILES_EVAL,
                normalise=True,
                ax=ax,
                ascending=False,
            )
            ax.set_title(f"{key} set")
            i += 1
        fig.suptitle(
            "Cummulative Profile - Total miles vs Total Accidents (Intersection)"
        )
        fig.tight_layout()
        current.card.append(Image.from_matplotlib(fig))
        
        self.tuned_train_results[self.model_config.COHORT] = 'Train'
        self.tuned_test_results[self.model_config.COHORT] = 'Test'
        self.tuned_blind_results[self.model_config.COHORT] = 'Blind'

        self.tuned_results = pd.concat(
            [
                self.tuned_train_results,
                self.tuned_test_results,
                self.tuned_blind_results,
            ]
        )

        current.card.append(Markdown(f"# Performance metrics on Intersection VINs"))

        self.score_risk = PercentileRanker(
            prior_distribution=self.tuned_results["tuned_poisson"].values, 
            scale=100, 
            round_decimals=0,
            best_first=False,
        )

        self.tuned_results["risk_score"] = self.score_risk.rank(arr=self.tuned_results["tuned_poisson"].values)

        all_results_list = {
            "All": self.tuned_results,
            "Intersection": self.tuned_results.dropna(subset=self.predicted_cols_final),
        }

        fig, self.decile_data_all_intersection = decile_frequency_plot(
            df_list=all_results_list,
            models=self.predicted_cols_final,
            accidents=self.model_config.TOTAL_ACCIDENTS,
            distance=self.model_config.TOTAL_MILES_EVAL,
            figsize=(16, 16),
            title="Decile Frequency Plot",
        )

        current.card.append(
            Table.from_dataframe(self.decile_data_all_intersection, truncate=False)
        )
        current.card.append(Image.from_matplotlib(fig))

        # Cumulative accuracy profile
        i = 0
        fig = plt.figure(figsize=(16, 16))
        for key in all_results_list:
            ax = plt.subplot2grid((len(all_results_list), 1), (i, 0), fig=fig)
            considered_df = all_results_list[key]
            plot_cumulative_profile(
                result_dataframe=considered_df,
                prediction_cols=self.predicted_cols_final,
                target_col=self.model_config.TOTAL_ACCIDENTS,
                normalise_col=self.model_config.TOTAL_MILES_EVAL,
                normalise=True,
                ax=ax,
                ascending=False,
            )
            ax.set_title(f"{key} set")
            i += 1
        fig.suptitle("Cummulative Profile - Total miles vs Total Accidents")
        fig.tight_layout()
        current.card.append(Image.from_matplotlib(fig))

        self.tuned_results['risk_score_dec'] = self.tuned_results['risk_score'] // 10
        self.tuned_results['risk_score_dec'] = self.tuned_results['risk_score_dec'].clip(0, 9)

        piv = self.tuned_results.pivot_table(
            index=['risk_score_dec', self.model_config.COHORT],
            values=[self.model_config.TOTAL_ACCIDENTS, self.model_config.TOTAL_MILES_EVAL],
            aggfunc=np.nansum
        ).reset_index()

        piv['freq'] = piv[self.model_config.TOTAL_ACCIDENTS] * 1e6 / piv[self.model_config.TOTAL_MILES_EVAL]

        fig = plt.figure(figsize=(16, 8))
        sns.lineplot(y='freq', x='risk_score_dec', hue=self.model_config.COHORT, data=piv, linestyle='--', marker='X')
        plt.hlines(
            y=(piv[self.model_config.TOTAL_ACCIDENTS].sum() * 1e6 / piv[self.model_config.TOTAL_MILES_EVAL].sum()),
            xmin=0, 
            xmax=9, 
            colors='black', 
            label='Population Average'
        )
        plt.legend()
        plt.title('Score Decile vs Accident Frequency')
        plt.xlabel('Score Decile')
        plt.ylabel('Accidents per Million Miles')
        current.card.append(Image.from_matplotlib(fig))


        index_list = [
                self.model_config.CONNECTION_ID,
                self.model_config.FEATURE_START,
                self.model_config.FEATURE_END,
                self.model_config.COHORT
            ]

        scores = ['milliman_score', 'tuned_poisson']

        fleet_scores_columns = list()
        self.fleet_score_dfs = list()

        for score in scores:
            # Get column name for fleet scores
            fleet_score = '{}_fleet'.format(score)
            fleet_scores_columns.append(fleet_score)

            fleet_trend = self.tuned_results.pivot_table(
                index=index_list, values=self.model_config.TOTAL_MILES, aggfunc=np.nansum
            )

            fleet_trend = self.tuned_results.merge(fleet_trend, on=index_list, how="left", suffixes=('', '_fleet'))

            fleet_trend[fleet_score] = (
                    fleet_trend[score] * fleet_trend[self.model_config.TOTAL_MILES] /
                    fleet_trend["{}_fleet".format(self.model_config.TOTAL_MILES)]
            )

            fleet_trend = fleet_trend.pivot_table(
                index=index_list,
                values=[fleet_score, self.model_config.TOTAL_ACCIDENTS, self.model_config.TOTAL_MILES_EVAL],
                aggfunc=[np.nansum, len],
            )

            fleet_trend.columns = ['_'.join(col) for col in fleet_trend.columns]
            fleet_trend = fleet_trend.reset_index()

            fleet_trend = fleet_trend.rename(
                    columns={
                        'len_{}'.format(fleet_score): 'vin_count',
                        'nansum_{}'.format(fleet_score): fleet_score,
                        'nansum_{}'.format(self.model_config.TOTAL_ACCIDENTS): self.model_config.TOTAL_ACCIDENTS,
                        'nansum_{}'.format(self.model_config.TOTAL_MILES_EVAL): self.model_config.TOTAL_MILES_EVAL,
                    }
            )

            for col in fleet_trend:
                if 'len_' in col or 'nansum_' in col:
                    del fleet_trend[col]

            self.fleet_score_dfs.append(fleet_trend)

        self.fleet_score_dfs = pd.merge(
            *self.fleet_score_dfs,
            on=index_list + [self.model_config.TOTAL_ACCIDENTS, self.model_config.TOTAL_MILES_EVAL],
            how='outer'
        )

        self.fleet_score_dfs['adjusted_milliman_score_fleet'] = 1000 - self.fleet_score_dfs['milliman_score_fleet']
        self.fleet_predicted_cols = ['adjusted_milliman_score_fleet', 'tuned_poisson_fleet']

        self.fleet_df_list = {
            'All connected Fleets': self.fleet_score_dfs,
        }

        fig, self.fleet_decile_data = decile_frequency_plot(
            df_list=self.fleet_df_list,
            models=self.fleet_predicted_cols,
            accidents=self.model_config.TOTAL_ACCIDENTS,
            distance=self.model_config.TOTAL_MILES_EVAL,
            figsize=(16, 6),
            title="Decile Frequency Plot",
        )

        current.card.append(Markdown(f"# Fleet Level Results"))
        current.card.append(Image.from_matplotlib(fig))
        current.card.append(Table.from_dataframe(self.fleet_decile_data, truncate=False))

        self.fleet_score_risk = PercentileRanker(
            prior_distribution=self.fleet_score_dfs["tuned_poisson_fleet"].values,
            scale=100,
            round_decimals=0,
            best_first=False,
        )

        self.next(self.end)

    @step
    def end(self):
        with metaflow_cross_environment_s3() as s3:
            for artifact_name in [
                'score_risk',
                'tuned_results',
                'fleet_score_risk',
                'tuned_model',
                'model_config',
                'selected_features',
                'predicted_cols_final',
                'transformation_pipeline'
            ]:
                artifact = getattr(self, artifact_name)
                s3.put(f'{artifact_name}.pkl', pkl.dumps(artifact))


if __name__ == "__main__":
    NirvanaRiskScoreModelTraining()
