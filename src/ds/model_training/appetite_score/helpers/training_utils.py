import numpy as np
import pandas as pd
import telemetry.api.logging as logging

from enum import Enum
from matplotlib import pyplot as plt

from skopt.space import Real
from skopt.space import Integer
from skopt.space import Categorical

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import QuantileTransformer

from model_training.risk_score_v1.model.linear import poisson_model
from model_training.appetite_score.config import AppetiteModelConfig

log = logging.getLogger(__name__)


class PipelineType(Enum):
    """
    Enum for the different types of training pipelines.
    """
    POISSON_QT = "poisson_qt"


class TrainPipelines:
    def __init__(self, model_config: AppetiteModelConfig):
        """
        This class keeps model pipelines to be used for experimentation.
        Currently only Poisson regression with quantile transformer is supported but additional models can be added.

        Parameters
        ----------
        model_config
            An instance of AppetiteModelConfig class
        """
        self._training_pipelines = {
            PipelineType.POISSON_QT.value: PoissonRegressionWithQuantileTransformer(
                feature_cols=model_config.SELECTED_FEATURES,
                target_col=model_config.TARGET_COL,
                random_state=model_config.RANDOM_STATE,
            ),
        }

    def get_experiment_pipelines(self, experiment_pipelines: list[str]) -> dict:
        """
        Returns the training pipelines to be used for experiments.

        Parameters
        ----------
        experiment_pipelines: list[str]
            A list of pipeline names to be used for experiments.
            The values should be subset of _training_pipelines keys.

        Returns
        -------
        dict
            A dictionary of training pipelines.
        """
        missing_keys = [val for val in experiment_pipelines if val not in self._training_pipelines.keys()]
        if len(missing_keys) > 0:
            raise ValueError(f"Pipeline(s) {missing_keys} not found in training pipelines.")
        experiment_pipelines = {
            key: self._training_pipelines[key] for key in experiment_pipelines
        }

        return experiment_pipelines


class PoissonRegressionWithQuantileTransformer:
    """
    This class implements a Poisson regression model with a quantile transformer for preprocessing.
    """

    def __init__(self, feature_cols: list, target_col: str, random_state: int):
        self.feature_cols = feature_cols
        self.target_col = target_col
        self.random_state = random_state
        self.transform_pipeline = QuantileTransformer(output_distribution='normal', random_state=random_state)
        self.model = None

    def train(self, train_data: pd.DataFrame):
        """
        Train poisson regression model using the provided training data.
        It uses QuantileTransformer to transform the features to a normal distribution
        and Bayesian optimization to find the best hyperparameters.
        It also trains a percentile ranker to rank the predictions in range [0,100]
        and shap explainer to explain the model.

        Parameters
        ----------
        train_data: pd.DataFrame
            The training data. It should contain the features and target columns.

        """
        self.transform_pipeline = QuantileTransformer(
            output_distribution='normal', random_state=self.random_state)

        X = self.transform_pipeline.fit_transform(train_data[self.feature_cols])
        y = train_data[self.target_col].values

        search_space = {
            'alpha': Real(1e1, 1e2, prior='log-uniform'),
            'max_iter': Integer(100, 3000),
            'fit_intercept': Categorical([True]),
            'solver': Categorical(['newton-cholesky', 'lbfgs']),
        }

        self.model, _ = poisson_model(
            train_data=X,
            train_target=y,
            random_state=self.random_state,
            cross_validation_folds=7,
            scoring='neg_mean_poisson_deviance',
            search_spaces=search_space,
        )

    def plot_feature_importance(self, figsize=(12, 7)):
        """
        Plots the feature importance of the trained model.

        Parameters
        ----------
        figsize: tuple
            The size of the figure to be plotted. Default is (12, 7).

        """
        fig, ax = plt.subplots(figsize=figsize)
        pd.DataFrame(
            self.model.coef_ / np.abs(self.model.coef_).sum(),
            index=self.feature_cols,
        ).abs().sort_values(by=0, ascending=False).plot(kind="barh", ax=ax,)
        ax.set_title("Feature Importance")

        return fig


def get_train_eval_split(
        feature_data: pd.DataFrame,
        train_size: float = 0.4,
        random_state: int = 43,
        make_holdout_set: bool = False,
        holdout_size: float = 0.5,
) -> tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    This function splits the data into train, test and holdout sets.
    The split is stratified based on Accident Frequency, Years in Business, Miles per Unit and PU count.

    Parameters
    ----------
    feature_data: pd.DataFrame
        The data that will be used for training and evaluation. It should contain the following columns:
        - 'Acc_per_1M_miles'
        - 'APPLICATION_REVIEW_ID'
        - 'POWER_UNIT_COUNT'
        - 'MilesPerUnit'
        - 'YearsInBusiness'
    train_size: float
        The proportion of the data to include in the train split. Default is 0.4.
    random_state: int
        Random state for reproducibility. Default is 43.
    make_holdout_set: bool
        Whether to create a holdout set. Default is False.
    holdout_size: float
        The proportion of the data to include in the holdout split out of test. Default is 0.5.

    Returns
    -------
    tuple[np.ndarray, np.ndarray, np.ndarray]
        The train, test and holdout application review IDs.
    """
    log.info("Splitting data into train, test and holdout sets")
    feature_data['acc_freq_quantile'] = pd.qcut(
        feature_data['Acc_per_1M_miles'], q=4, duplicates='drop', labels=False
    ).astype(str)

    feature_data['MPU_bucket'] = np.where(
        feature_data['MilesPerUnit'] <= 75, 0, np.where(feature_data['MilesPerUnit'] <= 110, 1, 2)).astype(str)
    feature_data['YIB_bucket'] = (feature_data['YearsInBusiness'] <= 24).astype(int).astype(str)
    feature_data['vin_cnt_quantile'] = pd.qcut(
        feature_data['POWER_UNIT_COUNT'], q=4, duplicates='drop', labels=False).astype(str)

    feature_data['stratify_col'] = (
        feature_data['MPU_bucket'] + '_' +
        feature_data['acc_freq_quantile'] + '_' +
        feature_data['vin_cnt_quantile']
    )

    # Calculate class counts
    class_counts = feature_data['stratify_col'].value_counts()

    # Identify rare classes
    rare_classes = class_counts[class_counts < 3].index

    # Merge rare classes into a single category
    feature_data.loc[feature_data['stratify_col'].isin(rare_classes), 'stratify_col'] = 'rare_classes'

    train_app_ids, test_app_ids = train_test_split(
        feature_data['APPLICATION_REVIEW_ID'].values,
        random_state=random_state,
        train_size=train_size,
        stratify=feature_data['stratify_col'].values
    )

    holdout_app_ids = np.empty(0, dtype=str)
    if make_holdout_set:
        test_df = feature_data[feature_data['APPLICATION_REVIEW_ID'].isin(test_app_ids)].reset_index(drop=True)
        # Calculate class counts
        class_counts = test_df['stratify_col'].value_counts()

        # Identify rare classes
        rare_classes = class_counts[class_counts < 2].index

        # Merge rare classes into a single category
        test_df.loc[test_df['stratify_col'].isin(rare_classes), 'stratify_col'] = 'rare_classes'

        test_app_ids, holdout_app_ids = train_test_split(
            test_df['APPLICATION_REVIEW_ID'].values,
            random_state=random_state,
            test_size=holdout_size,
            stratify=test_df['stratify_col'].values
        )

    log.info("Split sizes:", train=len(train_app_ids), test=len(test_app_ids), holdout=len(holdout_app_ids))

    return train_app_ids, test_app_ids, holdout_app_ids
