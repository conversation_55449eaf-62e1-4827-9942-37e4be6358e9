from datetime import datetime


class ExperimentInfo:
    EXPERIMENT_IDENTIFIER = "MillimanForSpeedGauge/v7"

    START_DATE = datetime(2020, 7, 1)
    END_DATE = datetime(2022, 7, 1)

    OUTPUT_BUCKET = "nirvana-data-scratch"
    RAW_DATA_BUCKET = "nirvana-telematics-default"

    BASE_FOLDER = "{experiment_identifier}/speedgauge_milliman/{connection_handle}/"
    TRIP_FOLDER = BASE_FOLDER + "VehicleMillimanTrips/{start}_{end}/"
    TRIP_OUTPUT_FORMAT = TRIP_FOLDER + "{vin}.csv"

    SCORE_FOLDER = BASE_FOLDER + "VehicleMillimanScores/{start}_{end}/"
    SCORE_OUTPUT_FORMAT = SCORE_FOLDER + "{window_start}_{window_end}/{vin}_{trip_count}.pkl"

    TRIP_SUMMARY_FOLDER = BASE_FOLDER + "VehicleMillimanTripSummary/{start}_{end}/"
    TRIP_SUMMARY_FORMAT = TRIP_SUMMARY_FOLDER + "{window_start}_{window_end}/{vin}_{trip_count}.txt"

    ADDITIONAL_CUSTOMERS = [
        "e4b06a02-322d-4c46-b73d-22171dba9b2e",
        "f54a8aab-3fd0-4c79-a391-0d6ac17ee74e",
        "ec12f1eb-ae9f-4545-af6f-b5c686ecc9e5",
        "6c23c910-14a9-4c2e-b075-83988ccff6ae",
        "d11e1b10-eed0-45fa-906e-89dea6439b14",
        "be2cb125-4776-4983-b784-c2f7504da5d6",
        "e4edfd78-d9ab-44af-ba15-1995fcae26db",
        "c0302194-8df6-4a02-ae75-9155b2e20c90",
        "9e2e7122-11e6-4bf0-8ee3-df7b8bc74fc3",
        "1e28f61e-be9f-4437-83dc-ad2cfe82ddb1",
        "3e665315-c908-4f7f-a4af-13ef676bee84",
        "2b1b3a74-9295-46cf-8627-1cbf1b247446",
        "33bd845d-9b2f-4444-ac3f-51b6be24ac39",
        "37599ce7-0690-49c0-83cc-d2146f10696f",
        "b9026611-9470-40d3-ae35-64b7b3960e77",
        "6d2f4daf-5cfc-4781-95ce-ead6497d8ddb",
        "a11994b8-85b8-46be-b945-da11ba0bb1c9",
        "1759031b-6e83-42d5-b99c-39baa88211dd",
        "7b4d1d60-0690-436f-9c4c-471fb8e26c01",
        "9eaaaef7-3a98-4a33-b229-472f3de7af4f",
        "312b80f7-89a2-4dc7-ada2-beedcc3c5a8a",
        "51a617a7-896c-409c-b17a-f6fe73c0ba3a",
        "5cac70c3-18e0-4adb-a160-1331df719844",
        "b3d8fdb9-d6d6-46c0-b315-2bc1798329d2",
        "25907d82-82c2-47d9-8481-d57ec750c75a",
        "542366cd-7e4f-4eaa-a989-8b1dbfe6336a",
        "9d7a64b6-3961-478b-9d19-344a1a0085f9",
        "5d5c5724-9f60-4b93-bb48-3889fe142a18",
        "15f08813-30f1-4485-be02-53f6594a5fc4",
        "ceb6213f-5e9b-4182-a5ad-83d166f018b8",
        "b79d4eb8-1ae8-4848-9faa-9a8d51a632a8",
        "848cac46-4f4a-47b9-8113-4ec85b561ab8",
        "a29039ea-8083-457d-9b50-b51cbc97885b",
        "6cd1afe8-c48b-4e16-b31c-d458ec830a52",
        "0ecdcddf-3836-43d6-a2ce-e01a38a24c8d",
        "d8a14b80-fa03-4580-bcaa-f34e5462b9dc",
        "caecf7d5-d535-42b1-a23c-507fb93409fd",
        "5b1aae05-11af-4369-92ed-cccc964e774d",
        "ca31efeb-05d2-43ad-8469-901d8c109ecc",
        "1a799d2b-3fd8-45fd-8e12-e5cd9b5fdda4",
        "b926d7c4-e38b-4a01-8b88-e8e2854d8a2f",
        "616387fc-f615-4e93-9dab-12a73c0952ab",
        "957db24c-5ab9-42a3-bc6c-b8a330802ad0",
        "0499fa05-c6de-43c2-b591-c3be0ef8f27c",
        "53254b1e-0850-4490-b0c4-c70336e0edc8",
        "fb719e1d-5331-49db-a2ca-5d33dbb0bb17",
        "0f5d14da-c79c-4690-87c4-f1ef6a56c968",
        "eef5e472-0d1a-4bb0-a1b4-99fdca92a188",
        "af2eed39-fb58-435a-a494-d6737b2bc87e",
        "a24a9fd1-a9f6-4cce-82b4-787a49da8653",
        "3ec90683-9c24-4a08-b019-85b5a26bcd32",
        "2a6a28ba-8993-463a-86c2-342092a6d3cb",
        "38052bed-0e58-418b-9304-8ecb793be0ba",
        "9fd6c802-9677-4f8e-b6f4-367927989411",
        "d857f1d2-7f75-4434-969a-30a1a5b7d987",
        "32240512-0af5-4853-b8d6-3adad6fcaa55",
        "c360302a-c003-4b15-aadc-d709200249c5",
        "949e37e0-272f-44b6-941e-281e944650de",
        "5afb19c8-5b85-448f-9d4f-a136b9110289",
        "515fd68f-8029-4cd6-b65d-0d4af74b599f",
        "73d75641-ebef-4289-8e04-915018182034",
    ]

    BLIND_SET = [
        "28cd5c80-cb9c-4544-b7eb-a245afed575a",
        "ba12470b-ce7a-4d39-9830-357251084cc8",
        "9e2e7122-11e6-4bf0-8ee3-df7b8bc74fc3",
        "54cb850a-66d7-429f-b09f-4af96175f199",
        "a4274e08-6e90-4003-8206-d1198d1ef122",
        "5620fa76-0e0e-42ab-9585-6dc7b8636ab8",
        "8494fd8e-6e7d-452e-b251-64348d641b0c",
        "274446bc-0e7a-47f6-abe9-fc51fb5e9c72",
        "68f95b9b-4a53-41fa-b5dd-406256ed4ccb",
        "93ffa255-ab12-4476-b4f1-56992fcbc761",
        "d35c44d9-f381-4a84-af0e-7a5316d91906",
        "9a39e0bc-76ff-4f9f-96cb-d3ea5d30dffe",
        "277c92a5-a418-4a17-974f-038ec0f7a49c",
        "c1fc1fd0-6e06-4e78-aa0a-627e786a38e0",
        "26d8f13f-0051-4901-87bf-4a26fee78c74",
        "82370b35-e57d-4685-88c4-c4ba9d5cab7b",
        "3b201359-50e5-4b49-ac74-f062254013f9",
        "f89288c9-2f85-4115-afdd-5e63461f8aec",
        "b81031e9-537c-496d-bd58-0a80667f3246",
        "f7f1b4b4-bc2e-421f-97c6-b3291d7c5604",
        "ba4330e2-f509-41e0-b395-d9b5be6e797c",
        "8f4c4815-71a6-416b-a665-6ee28d5bab21",
        "cf445f53-c0dc-4f73-b96f-0f5cd97edacb",
        "f7229e82-bd33-40bf-ba6b-3fcc188d65d9",
        "a24a9fd1-a9f6-4cce-82b4-787a49da8653",
        "2bee7839-8828-43b1-ab36-59bd4da54b47",
        "2b1b3a74-9295-46cf-8627-1cbf1b247446",
        "1ec84bcb-5fa4-4dfb-b0d6-44de801372db",
        "e4b06a02-322d-4c46-b73d-22171dba9b2e",
        "3e448d00-f08f-4e27-98a8-04cf99d4079a",
        "a31e8a0b-96e4-4519-aeef-e0b0d85ce421",
        "1d523560-58f4-40fc-8ac1-81c9466d20fe",
        "e5a134db-9423-4bef-9ff7-9880bc11bbba",
        "e3185bcc-d24a-45d6-9a3e-1b71ed23ce22",
        "961c31ac-0885-43f9-83f2-ddf978123f3a",
        "04d7cfb5-bdc4-4b34-9b1f-a846df0b387e",
    ]
