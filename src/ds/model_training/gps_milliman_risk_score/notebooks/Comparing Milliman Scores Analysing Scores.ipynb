{"cells": [{"cell_type": "code", "execution_count": null, "id": "80e9347f", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"/Users/<USER>/Documents/code/nirvana/src/ds/\")\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": null, "id": "fdabe51b", "metadata": {}, "outputs": [], "source": ["import os\n", "import copy\n", "import boto3\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from datetime import datetime\n", "\n", "from joblib import delayed\n", "from joblib import Parallel\n", "\n", "import compress_pickle as pkl\n", "\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "ca7fb349", "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots"]}, {"cell_type": "code", "execution_count": null, "id": "8d34253b", "metadata": {}, "outputs": [], "source": ["from s3_utils.s3_file_handler import get_all_keys\n", "from s3_utils.s3_file_handler import read_cached_data"]}, {"cell_type": "code", "execution_count": null, "id": "4ab7c18d", "metadata": {}, "outputs": [], "source": ["import xgboost as xgb\n", "from sklearn.model_selection import GridSearchCV"]}, {"cell_type": "code", "execution_count": null, "id": "f515089d", "metadata": {}, "outputs": [], "source": ["from utils.constants import Constants"]}, {"cell_type": "code", "execution_count": null, "id": "b9b7376c", "metadata": {}, "outputs": [], "source": ["from telematics_utils.milliman.trips import get_vin_trips"]}, {"cell_type": "code", "execution_count": null, "id": "1e77c328", "metadata": {}, "outputs": [], "source": ["from model_training.speed_gauge_risk_score.config import ExperimentInfo\n", "from model_training.speed_gauge_risk_score.milliman_helpers import vin_trips\n", "from model_training.speed_gauge_risk_score.milliman_helpers import get_milliman_file_list"]}, {"cell_type": "code", "execution_count": null, "id": "38a39ad7", "metadata": {}, "outputs": [], "source": ["from model_training.speed_gauge_risk_score.visualisation import debug_error_distribution"]}, {"cell_type": "code", "execution_count": null, "id": "ad812a9b", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": null, "id": "0961b8af", "metadata": {}, "outputs": [], "source": ["LOCAL_CACHE = \"/Users/<USER>/Documents/telematics/notebook/speedgauge_milliman/file_dumps\"\n", "CONNECTION_META_DATA_FILE = \"/Users/<USER>/Documents/telematics/notebook/speedgauge_milliman/file_dumps/fleet_meta_info.csv\"\n", "\n", "\n", "\n", "\n", "meta = [\n", "        [\"25907d82-82c2-47d9-8481-d57ec750c75a\", \"KeepTruckin\", \"MN\"],\n", "        [\"e90cbb56-e9e4-4520-93cd-fbf30b6d93e9\", \"Sam<PERSON><PERSON>\", \"OH\"],\n", "        [\"11ca0e0e-2c03-4433-a7bc-234ceb90473a\", \"Samsara\", \"IL\"],\n", "        [\"e5760995-083c-4e46-b75d-dad6e30c1b8c\", \"Samsara\", \"IL\"],\n", "        [\"0ba0e105-2e03-4274-9fc9-cff9e8506fcd\", \"Samsar<PERSON>\", \"IN\"],\n", "        [\"486e181b-a95b-4a92-a116-1396bfe32815\", \"Samsara\", \"KS\"],\n", "        [\"8ac255ca-897d-4d7d-9d96-9daf5ca25b51\", \"Samsara\", \"IN\"],\n", "        [\"c19bb536-3222-482c-a6da-4b480ebc6bd8\", \"Samsara\", \"WI\"],\n", "        [\"a8648502-cd38-4c20-a522-87d44679d78a\", \"Samsara\", \"IN\"],\n", "        [\"d0029d60-5521-422c-920c-9be925f00f21\", \"Samsara\", \"IN\"],\n", "        [\"dd7d726f-667e-4f30-89a2-5e7c2a2415a3\", \"Sam<PERSON><PERSON>\", \"OH\"],\n", "        [\"98b32cde-d7e3-4f6b-ac38-b3e60933b312\", \"KeepTruckin\", \"MN\"],\n", "        [\"25dd7a4c-73ca-4ec2-8d2f-609acabea77c\", \"Samsara\", \"IL\"],\n", "        [\"f3385be4-76f7-429e-a38c-7400806fd6b6\", \"KeepTruckin\", \"IL\"],\n", "        [\"91c0d08f-98d0-47fb-bb77-5b30c3836dfb\", \"KeepTruckin\", \"IL\"],\n", "        [\"3b201359-50e5-4b49-ac74-f062254013f9\", \"KeepTruckin\", \"MN\"],\n", "        [\"cf83b7aa-7d7b-4f41-8fd7-b23006639bb1\", \"Keep<PERSON>ruckin\", \"IN\"],\n", "        [\"7a25870a-52fe-46fb-ae96-de112125be92\", \"KeepTruckin\", \"IL\"],\n", "        [\"cf816aaa-d1bb-4390-a9f0-1caf3a675972\", \"Samsara\", \"IN\"],\n", "        [\"5736080e-5f50-48cf-a264-e84895a701a6\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"7733b578-30d4-46bd-9861-7d499afdb184\", \"Samsara\", \"OH\"],\n", "        [\"0f5d14da-c79c-4690-87c4-f1ef6a56c968\", \"Samsara\", \"IL\"],\n", "        [\"353ff6bc-2735-4392-9489-72544595dd71\", \"Samsara\", \"IN\"],\n", "        [\"b1a306bf-ff7c-4f6c-babb-c320022f907d\", \"Samsar<PERSON>\", \"TN\"],\n", "        [\"e1221fd5-c458-4164-9e0e-d7fc4f37ae03\", \"KeepTruckin\", \"MN\"],\n", "        [\"6a8c0caf-0973-474b-ac74-53f01454fff1\", \"Samsara\", \"IL\"],\n", "        [\"2253037f-d209-489f-8ec7-ed4445c8b87a\", \"Samsara\", \"IL\"],\n", "        [\"52177289-00cb-49fe-90e3-70d026531c6e\", \"KeepTruckin\", \"IL\"],\n", "        [\"e6c4d7af-cc78-4a22-96f2-d208fe325c0f\", \"KeepTruckin\", \"PA\"],\n", "        [\"b6870024-c7fd-47ac-916f-9f31950382ab\", \"KeepTruckin\", \"WI\"],\n", "        [\"747338ba-6016-447b-889f-dfbeb08fb3cb\", \"KeepTruckin\", \"MN\"],\n", "        [\"9a7d2be3-57d0-433c-a952-f7ccaa7cee83\", \"KeepTruckin\", \"IN\"],\n", "        [\"1bb382d1-1161-4416-a803-1af86bfd978b\", \"Samsara\", \"TN\"],\n", "        [\"d9e7f40b-aed5-45ac-a1f3-990d1d5df917\", \"Samsara\", \"IN\"],\n", "        [\"4c9dd140-febc-4c69-a5c8-dbe128e9da15\", \"Samsara\", \"NE\"],\n", "        [\"fd28587b-d635-40e9-9d6f-3dc11295342b\", \"KeepTruckin\", \"IL\"],\n", "        [\"bd0d09eb-41fc-4bbe-818d-23e4e7eda5d3\", \"KeepTruckin\", \"KS\"],\n", "        [\"045bd334-f8ac-465a-8b73-c3454486b8ab\", \"KeepTruckin\", \"IL\"],\n", "        [\"37911799-f25a-4a7d-b4d1-d61cc38d1363\", \"<PERSON><PERSON><PERSON>\", \"WI\"],\n", "        [\"ff8cc5bf-0273-499b-986d-1b258b13941b\", \"KeepTruckin\", \"IL\"],\n", "        [\"e78a680e-db43-4056-b380-b8d768100561\", \"Samsara\", \"IL\"],\n", "        [\"c60cfdc6-d49d-49cd-a33d-1eba213ce4c8\", \"Samsara\", \"PA\"],\n", "        [\"7f7bbc4d-d57c-4892-84da-c16adcde752d\", \"Samsara\", \"OH\"],\n", "        [\"4c9dd140-febc-4c69-a5c8-dbe128e9da15\", \"Samsara\", \"NE\"],\n", "        [\"8252e376-8692-44f2-b586-8f5e196451b2\", \"Samsar<PERSON>\", \"WI\"],\n", "        [\"6c2600cb-c9ce-4f65-865d-1590aff1be<PERSON>\", \"KeepTruckin\", \"WI\"],\n", "        [\"b6848113-5d40-42ad-a3a8-862c51d71447\", \"KeepTruckin\", \"MN\"],\n", "        [\"d9e7f40b-aed5-45ac-a1f3-990d1d5df917\", \"Samsara\", \"IN\"],\n", "        [\"c9e75cfa-d386-4009-8027-c92a7f5212f3\", \"KeepTruckin\", \"IL\"],\n", "        [\"96682a25-033a-43ea-9081-d38e52ff9be7\", \"KeepTruckin\", \"IL\"],\n", "        [\"f0f4e536-1dff-498e-bfba-ae4292d9324f\", \"Samsar<PERSON>\", \"TN\"],\n", "        [\"c9e75cfa-d386-4009-8027-c92a7f5212f3\", \"KeepTruckin\", \"IL\"],\n", "        [\"2b1b3a74-9295-46cf-8627-1cbf1b247446\", \"Samsara\", \"OH\"],\n", "        [\"431ebcfd-184a-4b38-ab7f-282435da78d2\", \"KeepTruckin\", \"IL\"],\n", "        [\"e1221fd5-c458-4164-9e0e-d7fc4f37ae03\", \"KeepTruckin\", \"MN\"],\n", "        [\"37911799-f25a-4a7d-b4d1-d61cc38d1363\", \"<PERSON><PERSON><PERSON>\", \"WI\"],\n", "        [\"e78a680e-db43-4056-b380-b8d768100561\", \"Samsara\", \"IL\"],\n", "        [\"8252e376-8692-44f2-b586-8f5e196451b2\", \"Samsar<PERSON>\", \"WI\"],\n", "        [\"b6848113-5d40-42ad-a3a8-862c51d71447\", \"KeepTruckin\", \"MN\"],\n", "        [\"ce5039e9-96e0-4c5f-8038-afa4892d50b3\", \"KeepTruckin\", \"MN\"],\n", "        [\"6d4ff907-51bf-4997-8fb8-9fd34864ae6f\", \"KeepTruckin\", \"IN\"],\n", "        [\"9f5bfde8-ff1f-4e02-bd34-dd3778f815dd\", \"KeepTruckin\", \"IL\"],\n", "        [\"694c0135-32c2-4fca-a020-2bf0ec6ddaf3\", \"KeepTruckin\", \"TN\"],\n", "        [\"04e0e840-45ad-4973-ac1a-1d8207b2179a\", \"KeepTruckin\", \"MN\"],\n", "        [\"e8936238-2851-4cca-8394-01519f4d2f74\", \"KeepTruckin\", \"MN\"],\n", "        [\"a950d6dc-18a1-4532-855d-443a95dcb186\", \"KeepTruckin\", \"IN\"],\n", "        [\"9bd33f0e-e2f6-4010-85ec-0cf3993fe6c7\", \"KeepTruckin\", \"IL\"],\n", "        [\"a936a953-76e0-4e61-a99a-ee6d6b6db12c\", \"KeepTruckin\", \"IL\"],\n", "        [\"1b62c26b-1abc-44f9-bb58-cf863792ace6\", \"KeepTruckin\", \"IL\"],\n", "        [\"57211880-b06a-45cb-aa16-6e9deac75b55\", \"KeepTruckin\", \"IL\"],\n", "        [\"4249c50f-d08e-42fd-941a-531a3069a7d8\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"835a33be-fcae-4c40-be6b-d1717c17688b\", \"KeepTruckin\", \"MN\"],\n", "        [\"ba079a1b-0c24-4961-998f-9def54c30a57\", \"Keep<PERSON><PERSON>ckin\", \"OH\"],\n", "        [\"92ffd940-8673-4379-86d7-c30ca1006f70\", \"KeepTruckin\", \"IL\"],\n", "        [\"b57c13cd-e6ef-4091-836a-18d72ae6b5ab\", \"KeepTruckin\", \"IL\"],\n", "        [\"ed01fa07-252e-4911-82d8-38aef006806c\", \"KeepTruckin\", \"IL\"],\n", "        [\"adde3ccb-ba82-4a36-8d55-92dda83f8461\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"e7c00ff7-b628-4128-9349-6953f5e8519c\", \"KeepTruckin\", \"IN\"],\n", "        [\"2077f229-31ef-47e9-902f-bb5b02458702\", \"KeepTruckin\", \"KS\"],\n", "        [\"e530881e-c5a4-4bf7-a0eb-0ee430563eca\", \"KeepTruckin\", \"PA\"],\n", "        [\"630a5df6-baac-4a05-ba19-73806a3a10b3\", \"KeepTruckin\", \"IL\"],\n", "        [\"2d70daef-49ee-4ae8-bbe7-f4a0ac03e2ab\", \"KeepTruckin\", \"IL\"],\n", "        [\"d545138b-8bbc-4163-8694-dfe38a833991\", \"Keep<PERSON>ruckin\", \"IN\"],\n", "        [\"4dec561a-9012-4815-8017-c9631e58b5a1\", \"KeepTruckin\", \"IL\"],\n", "        [\"de9a0a28-f89d-4b74-a6cc-05f759398ba1\", \"KeepTruckin\", \"IA\"],\n", "        [\"4b6197dd-43e6-4011-acf3-1d8b50f1ba60\", \"KeepTruckin\", \"IL\"],\n", "        [\"e7d264c4-19db-4e8e-960a-0bfad4753147\", \"KeepTruckin\", \"IL\"],\n", "        [\"22a8336c-7cb4-446e-b474-96338c116b98\", \"Keep<PERSON>ruckin\", \"MO\"],\n", "        [\"5168454a-e5ba-48f9-bf32-74e2c2bfb866\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"47526a1f-537d-4358-8284-33fd3b45b0ef\", \"KeepTruckin\", \"IL\"],\n", "        [\"0e5d1ae4-1705-4dec-9362-65bdfc4078e5\", \"Keep<PERSON>ruckin\", \"IN\"],\n", "        [\"06a91d72-3590-4cd1-8201-4d8865f5a294\", \"KeepTruckin\", \"IL\"],\n", "        [\"e1f9792a-4d85-4895-a462-e51f18b071f5\", \"Keep<PERSON>ruckin\", \"IN\"],\n", "        [\"c8aef61d-226b-40e6-a883-6742db70bd6c\", \"KeepTruckin\", \"IL\"],\n", "        [\"28cd5c80-cb9c-4544-b7eb-a245afed575a\", \"KeepTruckin\", \"IL\"],\n", "        [\"a4274e08-6e90-4003-8206-d1198d1ef122\", \"KeepTruckin\", \"MN\"],\n", "        [\"9e2e7122-11e6-4bf0-8ee3-df7b8bc74fc3\", \"Sam<PERSON><PERSON>\", \"OH\"],\n", "        [\"9eaaaef7-3a98-4a33-b229-472f3de7af4f\", \"KeepTruckin\", \"IL\"],\n", "        [\"a11994b8-85b8-46be-b945-da11ba0bb1c9\", \"KeepTruckin\", \"MO\"],\n", "        [\"616387fc-f615-4e93-9dab-12a73c0952ab\", \"Keep<PERSON>ruckin\", \"TN\"],\n", "        [\"d857f1d2-7f75-4434-969a-30a1a5b7d987\", \"KeepTruckin\", \"PA\"],\n", "        [\"73d75641-ebef-4289-8e04-915018182034\", \"KeepTruckin\", \"IL\"],\n", "        [\"b926d7c4-e38b-4a01-8b88-e8e2854d8a2f\", \"Keep<PERSON>ruckin\", \"NE\"],\n", "        [\"2a6a28ba-8993-463a-86c2-342092a6d3cb\", \"KeepTruckin\", \"IL\"],\n", "        [\"1a799d2b-3fd8-45fd-8e12-e5cd9b5fdda4\", \"<PERSON><PERSON><PERSON>\", \"TN\"],\n", "        [\"3e665315-c908-4f7f-a4af-13ef676bee84\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"5afb19c8-5b85-448f-9d4f-a136b9110289\", \"Samsara\", \"OH\"],\n", "        [\"e4b06a02-322d-4c46-b73d-22171dba9b2e\", \"Samsara\", \"IL\"],\n", "        [\"5b1aae05-11af-4369-92ed-cccc964e774d\", \"KeepTruckin\", \"MN\"],\n", "        [\"3ec90683-9c24-4a08-b019-85b5a26bcd32\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"7b4d1d60-0690-436f-9c4c-471fb8e26c01\", \"Samsar<PERSON>\", \"WI\"],\n", "        [\"ec12f1eb-ae9f-4545-af6f-b5c686ecc9e5\", \"KeepTruckin\", \"IA\"],\n", "        [\"0f5d14da-c79c-4690-87c4-f1ef6a56c968\", \"Samsara\", \"IL\"],\n", "        [\"af2eed39-fb58-435a-a494-d6737b2bc87e\", \"Samsara\", \"IL\"],\n", "        [\"542366cd-7e4f-4eaa-a989-8b1dbfe6336a\", \"Keep<PERSON>ruckin\", \"NE\"],\n", "        [\"6d2f4daf-5cfc-4781-95ce-ead6497d8ddb\", \"KeepTruckin\", \"IN\"],\n", "        [\"64afb53c-6d4d-4a93-aaa7-5387d731f9a0\", \"Samsara\", \"OH\"],\n", "        [\"848cac46-4f4a-47b9-8113-4ec85b561ab8\", \"KeepTruckin\", \"MO\"],\n", "        [\"a24a9fd1-a9f6-4cce-82b4-787a49da8653\", \"KeepTruckin\", \"TN\"],\n", "        [\"6cd1afe8-c48b-4e16-b31c-d458ec830a52\", \"Samsara\", \"IL\"],\n", "        [\"312b80f7-89a2-4dc7-ada2-beedcc3c5a8a\", \"Samsara\", \"MN\"],\n", "        [\"a29039ea-8083-457d-9b50-b51cbc97885b\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"15f08813-30f1-4485-be02-53f6594a5fc4\", \"KeepTruckin\", \"OH\"],\n", "        [\"ceb6213f-5e9b-4182-a5ad-83d166f018b8\", \"Samsar<PERSON>\", \"TN\"],\n", "        [\"caecf7d5-d535-42b1-a23c-507fb93409fd\", \"Samsara\", \"IL\"],\n", "        [\"5cac70c3-18e0-4adb-a160-1331df719844\", \"KeepTruckin\", \"MN\"],\n", "        [\"b79d4eb8-1ae8-4848-9faa-9a8d51a632a8\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"c360302a-c003-4b15-aadc-d709200249c5\", \"Samsara\", \"NE\"],\n", "        [\"53254b1e-0850-4490-b0c4-c70336e0edc8\", \"Samsara\", \"WI\"],\n", "        [\"b9026611-9470-40d3-ae35-64b7b3960e77\", \"KeepTruckin\", \"IN\"],\n", "        [\"2b1b3a74-9295-46cf-8627-1cbf1b247446\", \"Samsara\", \"OH\"],\n", "        [\"dcf6922f-a3e6-49b7-b2d9-1c69c4e5d319\", \"Samsara\", \"IL\"],\n", "        [\"949e37e0-272f-44b6-941e-281e944650de\", \"Samsara\", \"MO\"],\n", "        [\"5d5c5724-9f60-4b93-bb48-3889fe142a18\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"b3d8fdb9-d6d6-46c0-b315-2bc1798329d2\", \"<PERSON><PERSON><PERSON>\", \"OK\"],\n", "        [\"33bd845d-9b2f-4444-ac3f-51b6be24ac39\", \"Samsara\", \"PA\"],\n", "        [\"ca31efeb-05d2-43ad-8469-901d8c109ecc\", \"Samsara\", \"IL\"],\n", "        [\"1759031b-6e83-42d5-b99c-39baa88211dd\", \"KeepTruckin\", \"OH\"],\n", "        [\"957db24c-5ab9-42a3-bc6c-b8a330802ad0\", \"Samsara\", \"PA\"],\n", "        [\"32240512-0af5-4853-b8d6-3adad6fcaa55\", \"Samsara\", \"IL\"],\n", "        [\"9d7a64b6-3961-478b-9d19-344a1a0085f9\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"51a617a7-896c-409c-b17a-f6fe73c0ba3a\", \"Samsara\", \"IL\"],\n", "        [\"d8a14b80-fa03-4580-bcaa-f34e5462b9dc\", \"KeepTruckin\", \"TN\"],\n", "        [\"fb719e1d-5331-49db-a2ca-5d33dbb0bb17\", \"Samsara\", \"OH\"],\n", "        [\"e4edfd78-d9ab-44af-ba15-1995fcae26db\", \"Samsara\", \"IL\"],\n", "        [\"9e2e7122-11e6-4bf0-8ee3-df7b8bc74fc3\", \"Sam<PERSON><PERSON>\", \"OH\"],\n", "        [\"3e665315-c908-4f7f-a4af-13ef676bee84\", \"Keep<PERSON>ruckin\", \"OH\"],\n", "        [\"d11e1b10-eed0-45fa-906e-89dea6439b14\", \"KeepTruckin\", \"NE\"],\n", "        [\"9fd6c802-9677-4f8e-b6f4-367927989411\", \"Keep<PERSON>ruckin\", \"IN\"],\n", "        [\"be2cb125-4776-4983-b784-c2f7504da5d6\", \"Keep<PERSON>ruckin\", \"MO\"],\n", "        [\"c0302194-8df6-4a02-ae75-9155b2e20c90\", \"Samsara\", \"IL\"],\n", "        [\"25907d82-82c2-47d9-8481-d57ec750c75a\", \"KeepTruckin\", \"MN\"],\n", "        [\"38052bed-0e58-418b-9304-8ecb793be0ba\", \"KeepTruckin\", \"NC\"],\n", "        [\"f54a8aab-3fd0-4c79-a391-0d6ac17ee74e\", \"KeepTruckin\", \"SC\"],\n", "        [\"0ecdcddf-3836-43d6-a2ce-e01a38a24c8d\", \"KeepTruckin\", \"NC\"],\n", "        [\"37599ce7-0690-49c0-83cc-d2146f10696f\", \"Samsara\", \"IL\"],\n", "        [\"515fd68f-8029-4cd6-b65d-0d4af74b599f\", \"Keep<PERSON>ruckin\", \"IN\"],\n", "        [\"1e28f61e-be9f-4437-83dc-ad2cfe82ddb1\", \"Samsara\", \"WI\"],\n", "        [\"0499fa05-c6de-43c2-b591-c3be0ef8f27c\", \"Samsara\", \"IL\"],\n", "        [\"eef5e472-0d1a-4bb0-a1b4-99fdca92a188\", \"KeepTruckin\", \"WI\"],\n", "        [\"6c23c910-14a9-4c2e-b075-83988ccff6ae\", \"Samsara\", \"IL\"],\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "7a467d2f", "metadata": {}, "outputs": [], "source": ["# connection_map = [\n", "#     [\"7f7bbc4d-d57c-4892-84da-c16adcde752d\",\"<PERSON><PERSON><PERSON>\",\"OH\"],\n", "#     [\"e5760995-083c-4e46-b75d-dad6e30c1b8c\",\"Samsara\",\"IL\"],\n", "#     [\"11ca0e0e-2c03-4433-a7bc-234ceb90473a\",\"Samsara\",\"IL\"],\n", "#     [\"e90cbb56-e9e4-4520-93cd-fbf30b6d93e9\",\"<PERSON><PERSON><PERSON>\",\"OH\"],\n", "#     [\"25dd7a4c-73ca-4ec2-8d2f-609acabea77c\",\"Samsara\",\"IL\"],\n", "#     [\"2b1b3a74-9295-46cf-8627-1cbf1b247446\",\"Samsar<PERSON>\",\"OH\"],\n", "#     [\"f0f4e536-1dff-498e-bfba-ae4292d9324f\",\"<PERSON><PERSON><PERSON>\",\"TN\"],\n", "#     [\"96682a25-033a-43ea-9081-d38e52ff9be7\",\"KeepTruckin\",\"IL\"],\n", "#     [\"721a6899-38c8-4d96-90af-11e1c65a7a83\",\"<PERSON><PERSON><PERSON><PERSON>n\",\"OH\"],\n", "#     [\"bfec1907-27e7-469d-9add-f95b2dd65fac\",\"KeepTruckin\",\"IL\"],\n", "#     [\"be439d99-d5ae-4899-822a-a953d4d92aef\",\"Keep<PERSON>ruckin\",\"MN\"],\n", "#     [\"aeb8ee83-b7ae-4421-b9c6-611b242e6256\",\"Samsara\",\"IL\"],\n", "#     [\"4b3aa8f4-f093-472b-8d4e-68e92108e9b7\",\"KeepTruckin\",\"IL\"],\n", "#     [\"6d38b0e7-05a6-4139-bc9a-cb012664bd21\",\"<PERSON>sar<PERSON>\",\"MN\"],\n", "#     [\"bcac4e6f-3676-40e7-9073-7c4127dd8099\",\"<PERSON><PERSON><PERSON>\",\"IN\"]\n", "# ]\n", "# \n", "connection_map = pd.DataFrame(meta, columns=[\"connection\", \"tsp\", \"policy_state\"])\n", "connection_map = connection_map.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "8239ca1f", "metadata": {}, "outputs": [], "source": ["def get_downsampled_vin_trip(vin, connection_id):\n", "    file_summary = get_milliman_file_list(connection_id=connection_id)\n", "    trips = vin_trips(vin=vin, connection_id=connection_id, file_summary=file_summary)\n", "    return trips"]}, {"cell_type": "code", "execution_count": null, "id": "7c35949c", "metadata": {}, "outputs": [], "source": ["def get_score_from_s3(bucket, key):\n", "        s3_client = boto3.client(\"s3\")\n", "        outfile = read_cached_data(s3_client=s3_client, s3_bucket=bucket, cached_file=key)\n", "        out = pkl.load(outfile, compression='gzip')\n", "        out = out.to_dict()\n", "        dates = key.split(\"/\")[-2]\n", "        start_date = dates.split(\"_\")[0]\n", "        end_date = dates.split(\"_\")[1]\n", "        \n", "        out.update(\n", "            {\n", "                \"start_date\": start_date,\n", "                \"end_date\": end_date,\n", "                \"key\": key,\n", "            },\n", "        )\n", "        return out\n", "    \n", "def get_all_scores_for_connection(s3_client, bucket, prefix):\n", "    \n", "    keys = get_all_keys(\n", "        client=s3_client, \n", "        bucket=bucket, \n", "        prefix=prefix,\n", "    )\n", "    \n", "    if len(keys) == 0:\n", "        return None\n", "\n", "    file_keys = np.array(keys)[:, 0]\n", "    file_keys = file_keys.tolist()\n", "    \n", "    all_scores = []\n", "    \n", "    all_scores = Parallel(n_jobs=36)(delayed(get_score_from_s3)(bucket, key) for key in file_keys)\n", "        \n", "    all_scores = pd.DataFrame(all_scores)\n", "        \n", "    return all_scores"]}, {"cell_type": "code", "execution_count": null, "id": "2fe7434c", "metadata": {}, "outputs": [], "source": ["def get_milliman_scores(connection_id, source=\"downsampled\", use_cache=True):\n", "    \n", "    path_for_cache = \"{local_folder}/{tag}/{connection_handle}/{source}\".format(\n", "        tag=ExperimentInfo.EXPERIMENT_IDENTIFIER,\n", "        local_folder=LOCAL_CACHE,\n", "        connection_handle=connection_id,\n", "        source=source,\n", "    )\n", "    \n", "    cache_file = '{}/milliman_trend.csv'.format(path_for_cache)\n", "    \n", "    if (os.path.isfile(cache_file)) & (use_cache is True):\n", "        milliman_scores = pd.read_csv(cache_file)\n", "        return milliman_scores\n", "    \n", "    \n", "    if source == \"downsampled\":\n", "    \n", "        prefix = ExperimentInfo.SCORE_FOLDER.format(\n", "                    experiment_identifier=ExperimentInfo.EXPERIMENT_IDENTIFIER,\n", "                    connection_handle=connection_id,\n", "                    start=str(ExperimentInfo.START_DATE).replace(\" \", \"T\"),\n", "                    end=str(ExperimentInfo.END_DATE).replace(\" \", \"T\"),\n", "                )\n", "        \n", "        bucket = ExperimentInfo.OUTPUT_BUCKET\n", "        \n", "    elif source == \"native\":\n", "        prefix = \"milliman_caching/{connection_handle}/milliman_scores/\".format(connection_handle=connection_id)\n", "        bucket = Constants.CACHE_DATA_BUCKET\n", "\n", "    milliman_scores = get_all_scores_for_connection(\n", "        s3_client=s3_client,\n", "        bucket=bucket,\n", "        prefix=prefix,\n", "    )\n", "    \n", "    if milliman_scores is None:\n", "        return None\n", "\n", "    milliman_scores['connection'] = connection_id\n", "    \n", "    \n", "    \n", "    if not os.path.exists(path_for_cache):\n", "        os.makedirs(path_for_cache)\n", "    \n", "    milliman_scores.to_csv(cache_file, index=False)\n", "    return milliman_scores"]}, {"cell_type": "code", "execution_count": null, "id": "438004d9", "metadata": {}, "outputs": [], "source": ["def get_combined_milliman_scores(source=\"downsampled\", use_cache=True, connections = ExperimentInfo.ADDITIONAL_CUSTOMERS):\n", "    column_list = [\n", "        \"vin\",\n", "        \"estimatedAnnualDistance\", \n", "        \"score\",\n", "        \"positiveReasons\",\n", "        \"negativeReasons\",\n", "        \"start_date\",\n", "        \"end_date\",\n", "        \"key\",\n", "        \"connection\",\n", "    ]\n", "    \n", "    combined_milliman_scores = []\n", "\n", "    for connection_id in connections:\n", "        milliman_scores = get_milliman_scores(connection_id=connection_id, source=source, use_cache=use_cache)\n", "        combined_milliman_scores.append(milliman_scores)\n", "\n", "    combined_milliman_scores = pd.concat(combined_milliman_scores)\n", "    combined_milliman_scores = combined_milliman_scores[combined_milliman_scores.statusCode == 0]\n", "    \n", "    combined_milliman_scores = combined_milliman_scores[column_list]\n", "    \n", "    return combined_milliman_scores"]}, {"cell_type": "code", "execution_count": null, "id": "1e769be7", "metadata": {}, "outputs": [], "source": ["s3_client = boto3.client(\"s3\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2f2f796", "metadata": {}, "outputs": [], "source": ["downsampled_milliman_scores = get_combined_milliman_scores(source=\"downsampled\", use_cache=True)\n", "downsampled_milliman_scores = downsampled_milliman_scores[downsampled_milliman_scores.score.notnull()]"]}, {"cell_type": "code", "execution_count": null, "id": "a2f56369", "metadata": {}, "outputs": [], "source": ["native_milliman_scores = pd.read_csv(\"/Users/<USER>/Documents/telematics/notebook/v1_milliman_trips/v4_trips/v4_scores.csv\")\n", "native_milliman_scores.columns = [i.lower() for i in native_milliman_scores.columns]\n", "native_milliman_scores = native_milliman_scores[native_milliman_scores.version == \"V4\"]\n", "native_milliman_scores = native_milliman_scores[\n", "    native_milliman_scores.score.notnull() & \n", "    (native_milliman_scores.status == \"Success\")\n", "]\n", "\n", "\n", "\n", "native_milliman_scores = native_milliman_scores.rename(columns={\n", "    \"connection_handle_id\": \"connection\",\n", "    \"filename\": \"key\",\n", "    \"estimated_annual_distance\": \"estimatedAnnualDistance\",\n", "    \"positive_reasons\": \"positiveReasons\",\n", "    \"negative_reasons\": \"negativeReasons\",\n", "})"]}, {"cell_type": "code", "execution_count": null, "id": "817b7c0e", "metadata": {}, "outputs": [], "source": ["merged_scores = downsampled_milliman_scores.merge(\n", "    native_milliman_scores,\n", "    on=[\"vin\", \"connection\", \"start_date\", \"end_date\"],\n", "    how=\"inner\",\n", "    suffixes=(\"_downsampled\", \"_native\")\n", ")\n", "\n", "merged_scores = merged_scores[\n", "    (merged_scores.end_date < \"2022-07-01\") &\n", "    (merged_scores.start_date >= \"2020-01-01\")\n", "]\n", "\n", "merged_scores['start_date'] = pd.to_datetime(merged_scores['start_date'])\n", "merged_scores['end_date'] = pd.to_datetime(merged_scores['end_date'])\n", "\n", "\n", "merged_scores['score_diff'] = (merged_scores['score_native'] - merged_scores['score_downsampled']).clip(-200, 200)\n", "\n", "merged_scores[\"window\"] = np.round((merged_scores['end_date'] - merged_scores['start_date']).dt.days / 30)\n", "merged_scores[\"window\"] = merged_scores[\"window\"].astype(str)\n", "\n", "merged_scores = merged_scores.merge(connection_map, on=\"connection\", how=\"left\")"]}, {"cell_type": "markdown", "id": "f3f834a0", "metadata": {}, "source": ["### Add trip count information"]}, {"cell_type": "code", "execution_count": null, "id": "37e4020a", "metadata": {}, "outputs": [], "source": ["new = merged_scores.key_downsampled.str.split(\"/\", expand = True)\n", "key_col = new[new.shape[1] -1 ]\n", "key_col = key_col.str.replace(\".pkl\", \"\")\n", "key_col = key_col.str.split(\"_\", expand = True)\n", "key_col = key_col[1].astype(int)\n", "merged_scores[\"trip_count_downsampled\"] = key_col\n", "merged_scores[\"tpm_downsampled\"] = merged_scores['trip_count_downsampled'] / merged_scores['window'].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "9a940952", "metadata": {}, "outputs": [], "source": ["debug_error_distribution(data=merged_scores, error=\"score_diff\", split=\"tsp\")"]}, {"cell_type": "code", "execution_count": null, "id": "b7750781", "metadata": {}, "outputs": [], "source": ["debug_error_distribution(data=merged_scores, error=\"score_downsampled\", split=\"tsp\")"]}, {"cell_type": "code", "execution_count": null, "id": "ab825513", "metadata": {}, "outputs": [], "source": ["debug_error_distribution(\n", "    data=merged_scores,\n", "    error=\"score_diff\",\n", "    split=\"window\")"]}, {"cell_type": "code", "execution_count": null, "id": "d0cf3709", "metadata": {}, "outputs": [], "source": ["condn = ((merged_scores.estimatedAnnualDistance_downsampled > 30000) &\n", "        (merged_scores.estimatedAnnualDistance_downsampled < 300000) &\n", "        (merged_scores.score_native > 600) & \n", "        (merged_scores.score_downsampled > 600))\n", "        \n", "fig = px.histogram(\n", "    merged_scores[condn],\n", "    x=\"score_diff\", color=\"tsp\", nbins=100)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d8f911fb", "metadata": {}, "outputs": [], "source": ["merged_scores['positiveReasons_downsampled'] = merged_scores['positiveReasons_downsampled'].str.replace(\"'\", \"\").str.replace(\"[\", \"\").str.replace(\"]\", \"\")\n", "merged_scores['negativeReasons_downsampled'] = merged_scores['negativeReasons_downsampled'].str.replace(\"'\", \"\").str.replace(\"[\", \"\").str.replace(\"]\", \"\")\n", "\n", "merged_scores['positiveReasons_native'] = merged_scores['positiveReasons_native'].str.replace(\"'\", \"\").str.replace(\"[\", \"\").str.replace(\"]\", \"\")\n", "merged_scores['negativeReasons_native'] = merged_scores['negativeReasons_native'].str.replace(\"'\", \"\").str.replace(\"[\", \"\").str.replace(\"]\", \"\")"]}, {"cell_type": "code", "execution_count": null, "id": "087488e5", "metadata": {}, "outputs": [], "source": ["merged_scores[\"reason_code\"] = \\\n", "    merged_scores['positiveReasons_downsampled'].map(str) + \"||\" + merged_scores['negativeReasons_downsampled'].map(str)"]}, {"cell_type": "code", "execution_count": null, "id": "add41082", "metadata": {}, "outputs": [], "source": ["test, train = train_test_split(merged_scores.connection.unique(), test_size=0.7, random_state=43)"]}, {"cell_type": "code", "execution_count": null, "id": "d50276d1", "metadata": {}, "outputs": [], "source": ["merged_scores[\"tag\"] = \"Others\"\n", "merged_scores.loc[merged_scores.connection.isin(train), \"tag\"] = \"Train\"\n", "merged_scores.loc[merged_scores.connection.isin(test), \"tag\"] = \"Test\""]}, {"cell_type": "code", "execution_count": null, "id": "c6656c01", "metadata": {}, "outputs": [], "source": ["pivot = merged_scores[\n", "        (merged_scores.estimatedAnnualDistance_downsampled > 30000) &\n", "        (merged_scores.estimatedAnnualDistance_downsampled < 300000) &\n", "        (merged_scores.score_native > 600) & \n", "        (merged_scores.score_downsampled > 600) &\n", "        (merged_scores.tsp == \"KeepTruckin\") &\n", "        (merged_scores.tag == \"Train\")\n", "    ].pivot_table(index=[\"reason_code\"], values='score_diff', aggfunc=[np.nanmedian, len])\n", "\n", "\n", "pivot = pivot[pivot['len'][\"score_diff\"] > 40].sort_values(by=(\"nanmedian\", \"score_diff\"))\n", "pivot.columns = [\"_\".join(i) for i in pivot.columns]\n", "pivot = pivot[[\"nanmedian_score_diff\"]]\n", "pivot = pivot.reset_index()\n", "\n", "\n", "corrected_merged_scores = merged_scores.merge(pivot, on=['reason_code'], how='left')\n", "corrected_merged_scores[\"nanmedian_score_diff\"] = corrected_merged_scores[\"nanmedian_score_diff\"].fillna(0)\n", "\n", "\n", "corrected_merged_scores['updated_score'] = corrected_merged_scores['score_downsampled'] + corrected_merged_scores[\"nanmedian_score_diff\"]\n", "corrected_merged_scores['updated_score_diff'] = corrected_merged_scores['score_native'] - corrected_merged_scores['updated_score']"]}, {"cell_type": "code", "execution_count": null, "id": "7b071c17", "metadata": {}, "outputs": [], "source": ["condn = (\n", "    (corrected_merged_scores.estimatedAnnualDistance_downsampled > 30000) &\n", "    (corrected_merged_scores.estimatedAnnualDistance_downsampled < 300000) &\n", "    (corrected_merged_scores.score_native > 600) & \n", "    (corrected_merged_scores.score_downsampled > 600) #&\n", "#     (corrected_merged_scores.tsp ==  \"KeepTruckin\")\n", ")\n", "        \n", "debug_error_distribution(\n", "    data=corrected_merged_scores[condn],\n", "    error=\"updated_score_diff\", \n", "    split=\"tsp\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d9b1bca", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(\n", "    corrected_merged_scores[\n", "        (corrected_merged_scores.estimatedAnnualDistance_downsampled > 30000) &\n", "        (corrected_merged_scores.estimatedAnnualDistance_downsampled < 300000) &\n", "        (corrected_merged_scores.score_native > 600) & \n", "        (corrected_merged_scores.score_downsampled > 600) &\n", "        (corrected_merged_scores.tsp == \"KeepTruckin\")\n", "    ],\n", "    x=\"updated_score_diff\", color=\"reason_code\", nbins=50)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f289fd37", "metadata": {}, "outputs": [], "source": ["corrected_merged_scores[\"prod_downsampled\"] = \\\n", "    corrected_merged_scores[\"updated_score\"] * corrected_merged_scores[\"estimatedAnnualDistance_downsampled\"]\n", "\n", "corrected_merged_scores[\"prod_native\"] = \\\n", "    corrected_merged_scores[\"score_native\"] * corrected_merged_scores[\"estimatedAnnualDistance_native\"]\n", "\n", "combined_scores = corrected_merged_scores.pivot_table(\n", "    index=[\"connection\", \"window\", \"end_date\", \"tsp\", \"tag\"],\n", "    values=[\"prod_downsampled\", \"prod_native\",\n", "            \"estimatedAnnualDistance_downsampled\", \"estimatedAnnualDistance_native\"\n", "           ],\n", "    aggfunc=np.nansum\n", ")\n", "\n", "combined_scores[\"combined_downsampled\"] =\\\n", "    combined_scores[\"prod_downsampled\"] / combined_scores[\"estimatedAnnualDistance_downsampled\"]\n", "\n", "combined_scores[\"combined_native\"] =\\\n", "    combined_scores[\"prod_native\"] / combined_scores[\"estimatedAnnualDistance_native\"]\n", "\n", "combined_scores[\"combined_score_diff\"] = \\\n", "    combined_scores[\"combined_native\"] - combined_scores[\"combined_downsampled\"]\n", "\n", "combined_scores = combined_scores.reset_index()\n", "\n", "\n", "avg_distance = corrected_merged_scores.pivot_table(\n", "    index=[\"connection\", \"window\", \"end_date\", \"tsp\", \"tag\"],\n", "    values=[\"estimatedAnnualDistance_downsampled\", \"tpm_downsampled\"],\n", "    aggfunc=np.nanmedian\n", ")\n", "avg_distance = avg_distance.reset_index()\n", "avg_distance = avg_distance.rename(\n", "    columns={\n", "        \"estimatedAnnualDistance_downsampled\": \"mean_estimatedAnnualDistance_downsampled\",\n", "        \"tpm_downsampled\": \"mean_tpm_downsampled\"\n", "    }\n", ")\n", "combined_scores = combined_scores.merge(\n", "    avg_distance,\n", "    on=[\"connection\", \"window\", \"end_date\", \"tsp\", \"tag\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "75dc234b", "metadata": {}, "outputs": [], "source": ["fig = debug_error_distribution(data=combined_scores,\n", "                               error=\"combined_score_diff\", \n", "                               split=\"tag\"\n", "                              )\n", "fig.update_layout(\n", "    autosize=False,\n", "    width=1000,\n", "    height=750,)"]}, {"cell_type": "code", "execution_count": null, "id": "93832680", "metadata": {}, "outputs": [], "source": ["combined_scores.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "78eefe81", "metadata": {}, "outputs": [], "source": ["px.scatter(\n", "    combined_scores, \n", "    y=\"combined_score_diff\", \n", "#     x=\"mean_tpm_downsampled\",\n", "    x=\"mean_estimatedAnnualDistance_downsampled\",\n", "    color=\"tag\",\n", "    trendline=\"ols\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "79336476", "metadata": {}, "outputs": [], "source": ["corrected_merged_scores.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e715abc6", "metadata": {}, "outputs": [], "source": ["condn = (\n", "    (corrected_merged_scores.estimatedAnnualDistance_downsampled > 30000) &\n", "    (corrected_merged_scores.estimatedAnnualDistance_downsampled < 300000) &\n", "    (corrected_merged_scores.score_native > 600) & \n", "    (corrected_merged_scores.score_downsampled > 600)\n", "        )\n", "px.scatter(\n", "    corrected_merged_scores[condn & (corrected_merged_scores.window.astype(str) == \"6.0\")],\n", "    x=\"tpm_downsampled\",\n", "    y=\"updated_score_diff\",\n", "    color=\"estimatedAnnualDistance_downsampled\",\n", "    trendline=\"ols\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "913fa6e6", "metadata": {}, "outputs": [], "source": ["# condn = (\n", "#     (merged_scores.estimatedAnnualDistance_downsampled > 30000) &\n", "#     (merged_scores.estimatedAnnualDistance_downsampled < 300000) &\n", "#     (merged_scores.score_native > 600) & \n", "#     (merged_scores.score_downsampled > 600)\n", "#         )\n", "# px.scatter(\n", "#     merged_scores[condn],\n", "#     x=(merged_scores[condn][\"estimatedAnnualDistance_native\"] - merged_scores[condn][\"estimatedAnnualDistance_downsampled\"]),\n", "#     y=\"score_diff\",\n", "#     color=\"tpm_downsampled\",\n", "#     trendline=\"ols\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "2191275c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "metaflow-poc-gt9NFMQa-py3.9", "language": "python", "name": "metaflow-poc-gt9nfmqa-py3.9"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}