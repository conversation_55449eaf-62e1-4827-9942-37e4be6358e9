import numpy as np

import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from telematics_utils.risk_scores.milliman.gps.config import EngineMode


def debug_downsampling_plot(vin_trips, vin_df, downsampled_df, start_datetime, end_datetime, vin):
    downsampled_df_val_idx = (downsampled_df.timestamp.dt.date >= start_datetime.date()) & (
        downsampled_df.timestamp.dt.date < end_datetime.date()
    )

    vin_df_val_idx = (vin_df.timestamp.dt.date >= start_datetime.date()) & (
        vin_df.timestamp.dt.date < end_datetime.date()
    )

    # Create figure with secondary y-axis
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=downsampled_df[downsampled_df_val_idx].timestamp,
            y=downsampled_df[downsampled_df_val_idx].speedMilesPerHour,
            name="speed",
        ),
        secondary_y=False,
    )

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=vin_df[vin_df_val_idx].timestamp, y=vin_df[vin_df_val_idx].speedMilesPerHour, name="granular_speed"
        ),
        secondary_y=False,
    )

    # Add traces
    fig.add_trace(
        go.Scatter(x=vin_df[vin_df_val_idx].timestamp, y=vin_df[vin_df_val_idx].engine_state, name="Engine"),
        secondary_y=False,
    )

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=downsampled_df[downsampled_df_val_idx].timestamp,
            y=downsampled_df[downsampled_df_val_idx].duration.clip(0, 1800),
            line=dict(color="#000000"),
            name="Sampling",
        ),
        secondary_y=True,
    )

    # Add traces
    fig.add_trace(go.Scatter(x=vin_trips.time, y=vin_trips.val, name="Trips"), secondary_y=False)

    # Set x-axis title
    fig.update_xaxes(title_text="Time")

    # Add figure title
    fig.update_layout(title_text="{}".format(vin))

    # Set y-axes titles
    fig.update_yaxes(title_text="<b>Speed</b>", secondary_y=False)
    fig.update_yaxes(title_text="<b>Sampling</b>", secondary_y=True)
    return fig


def debug_engine_state(vin_df, downsampled_df, start_datetime, end_datetime, vin):
    downsampled_df["engine_plot"] = np.nan

    downsampled_df.loc[downsampled_df.Engine == EngineMode.ON, "engine_plot"] = 55
    downsampled_df.loc[downsampled_df.Engine == EngineMode.OFF, "engine_plot"] = 0

    downsampled_df_val_idx = (downsampled_df.timestamp.dt.date >= start_datetime.date()) & (
        downsampled_df.timestamp.dt.date < end_datetime.date()
    )

    vin_df_val_idx = (vin_df.timestamp.dt.date >= start_datetime.date()) & (
        vin_df.timestamp.dt.date < end_datetime.date()
    )

    # Create figure with secondary y-axis
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=downsampled_df[downsampled_df_val_idx].timestamp,
            y=downsampled_df[downsampled_df_val_idx].speedMilesPerHour,
            name="speed",
        ),
        secondary_y=False,
    )

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=downsampled_df[downsampled_df_val_idx].timestamp,
            y=downsampled_df[downsampled_df_val_idx].engine_plot,
            name="ProxyEngine",
        ),
        secondary_y=False,
    )

    # Add traces
    fig.add_trace(
        go.Scatter(x=vin_df[vin_df_val_idx].timestamp, y=vin_df[vin_df_val_idx].engine_state, name="Engine"),
        secondary_y=False,
    )

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=downsampled_df[downsampled_df_val_idx].timestamp,
            y=downsampled_df[downsampled_df_val_idx].duration.clip(0, 1800),
            line=dict(color="#000000"),
            name="Sampling",
        ),
        secondary_y=True,
    )

    # Set x-axis title
    fig.update_xaxes(title_text="Time")

    # Add figure title
    fig.update_layout(title_text="{}".format(vin))

    # Set y-axes titles
    fig.update_yaxes(title_text="<b>Speed</b>", secondary_y=False)
    fig.update_yaxes(title_text="<b>Sampling</b>", secondary_y=True)
    return fig


def debug_error_distribution(data, error, split=None):
    figures = [
        px.ecdf(
            data,
            x=error,
            color=split,
        ),
        px.histogram(data, x=error, color=split),
    ]

    fig = make_subplots(rows=len(figures), cols=1, shared_xaxes=True)

    for i, figure in enumerate(figures):
        for trace in range(len(figure["data"])):
            fig.append_trace(figure["data"][trace], row=i + 1, col=1)

    return fig
