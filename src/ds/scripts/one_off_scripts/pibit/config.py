class ApiDocsConfig:
    URL = "api.pibit.ai"
    API_HEADERS = {
        'x-api-key': 'ZzU4CwLskqa40Hv1f2vLP21uGyUc5lLF5cEhLe2M'
    }

    # Document ID Input
    # pass True if document id csv is in local or pass False if it has to be fetched from S3
    LOCAL_STORAGE = True

    # Provide s3 bucket if LOCAL_STORAGE is set False
    S3_BUCKET = ""

    # Provide local path to document id csv if LOCAL_STORAGE is set True else pass s3 path
    DOCUMENT_ID_CSV_PATH = "scripts/one_off_scripts/pibit/qa_docs.csv"

    # Document ID column name used in the csv
    DOCUMENT_ID_COL = "document_id"

    # Output csv file path
    OUTPATH = "scripts/one_off_scripts/pibit/pibit_api_status.csv"
