import copy
import json

import numpy as np
import pandas as pd

from utils.constants import Constants

from telematics_utils.vin.config import VinMetricKey

import telemetry.api.logging as logging
from telematics_utils.helpers.gps import get_distance_from_gps
from telematics_utils.helpers.gps import get_garaging_location_rank_and_county

log = logging.getLogger(__name__)


def get_vin_level_distance_vs_duration(all_vin_metric: dict) -> pd.DataFrame:
    all_average_distance = []
    for vin in all_vin_metric:
        vin_metrics = all_vin_metric.get(vin, dict()).get("vin_metrics", dict())

        if len(vin_metrics) == 0:
            continue

        average_distance = vin_metrics.get(VinMetricKey.AVERAGE_DAILY_DISTANCE)
        average_distance_gps = vin_metrics.get(VinMetricKey.AVERAGE_DAILY_DISTANCE_GPS)
        estimated_annual = average_distance * Constants.DAYS_IN_YEAR
        estimated_annual_gps = average_distance_gps * Constants.DAYS_IN_YEAR
        all_average_distance.append(
            [
                vin,
                np.round(average_distance, 2),
                np.round(estimated_annual, 2),
                np.round(average_distance_gps, 2),
                np.round(estimated_annual_gps, 2),
            ]
        )

    all_average_distance = np.array(all_average_distance)
    all_average_distance = pd.DataFrame(
        all_average_distance,
        columns=[
            "vin",
            "daily_average",
            "estimated_annual",
            "daily_average_gps",
            "estimated_annual_gps",
        ],
    )
    return all_average_distance


def prepare_tabular_vin_level_info(all_vin_metric: dict, distance_vs_duration: pd.DataFrame, fleet_metrics: dict):
    dot_garage = fleet_metrics.get("primary_garaging_location")

    garaging_table = []

    fleet_garaging_centers = fleet_metrics.get("garaging_center")
    log.info("Processing Fleet Garaging location start")
    fleet_info = get_garaging_location_rank_and_county("Fleet Level", fleet_garaging_centers)
    log.info("Processing Fleet Garaging location end")
    garaging_table.append(fleet_info)

    vin_table = []
    for vin in all_vin_metric:
        log.info("Processing VIN to combine metrics start", vin=vin)
        vin_metric = all_vin_metric.get(vin).get("vin_metrics")
        if len(vin_metric) == 0:
            log.info("No VIN data available to generate score")
            continue
        vin_radius = vin_metric.get(VinMetricKey.RADIUS)
        odo_files = vin_metric.get("odo_file")
        vin_garaging_centers = vin_metric.get(VinMetricKey.GARAGING_CENTER)
        vin_radius_location = vin_metric.get(VinMetricKey.RADIUS_LOCATION)
        farthest_county = vin_metric.get(VinMetricKey.FARTHEST_COUNTY)
        garaging_county = vin_metric.get(VinMetricKey.GARAGING_COUNTY)
        vin_garage = vin_metric.get(VinMetricKey.PRIMARY_GARAGING_LOCATION)
        vin_hazard_duration = vin_metric.get(VinMetricKey.HAZARD_DURATION)
        vin_hazard_distance = vin_metric.get(VinMetricKey.HAZARD_DISTANCE)
        vin_hazard_duration_pct = vin_metric.get(VinMetricKey.HAZARD_DURATION_PCT)
        vin_hazard_distance_pct = vin_metric.get(VinMetricKey.HAZARD_DISTANCE_PCT)
        data_start = vin_metric.get(VinMetricKey.DATA_START)
        data_end = vin_metric.get(VinMetricKey.DATA_END)
        total_distance = vin_metric.get(VinMetricKey.TOTAL_DISTANCE)

        log.info("Processing VIN to combine metrics end", vin=vin)

        miles_data = distance_vs_duration[distance_vs_duration.vin == vin]

        if miles_data.shape[0] > 0:
            daily_distance_odo = miles_data["daily_average"].iloc[0]
            estimated_annual_odo = miles_data["estimated_annual"].iloc[0]
            daily_distance_gps = miles_data["daily_average_gps"].iloc[0]
            estimated_annual_gps = miles_data["estimated_annual_gps"].iloc[0]
            daily_distance_best = copy.deepcopy(daily_distance_odo)
            estimated_annual_best = copy.deepcopy(estimated_annual_odo)

            if odo_files == 0:
                daily_distance_odo = np.nan
                estimated_annual_odo = np.nan

        else:
            daily_distance_odo = np.nan
            estimated_annual_odo = np.nan
            daily_distance_gps = np.nan
            estimated_annual_gps = np.nan
            daily_distance_best = np.nan
            estimated_annual_best = np.nan

        log.info("Processing VIN to processing miles data", vin=vin)

        if vin_garage.empty():
            distance_from_dot_garage = np.nan
        else:
            distance_from_dot_garage = get_distance_from_gps(
                vin_garage.lat, vin_garage.lon, dot_garage.lat, dot_garage.lon
            )

        # Rounding and representing number for aesthetics
        vin_hazard_distance = round(vin_hazard_distance, 2)
        vin_hazard_distance_pct = round(vin_hazard_distance_pct, 2)

        # Rounding and representing number for aesthetics
        vin_hazard_duration = round(vin_hazard_duration / Constants.SECONDS_IN_HOUR, 2)
        vin_hazard_duration_pct = round(vin_hazard_duration_pct, 2)

        vin_table.append(
            {
                "VIN": vin,
                "Data Start": data_start,
                "Data End": data_end,
                "Total Miles": total_distance,
                "Radius of Operation (miles)": vin_radius,
                "Average Daily miles": daily_distance_best,
                "Average Daily Miles (Odo)": daily_distance_odo,
                "Average Daily Miles (GPS)": daily_distance_gps,
                "Estimated Annual Miles": estimated_annual_best,
                "Estimated Annual Miles (Odo)": estimated_annual_odo,
                "Estimated Annual Miles (GPS)": estimated_annual_gps,
                "Distance from Garaging (miles)": distance_from_dot_garage,
                "Distance in HZs (miles / %)": "{} ({}%)".format(vin_hazard_distance, vin_hazard_distance_pct),
                "Time in HZs (Hours / %)": "{} ({}%)".format(vin_hazard_duration, vin_hazard_duration_pct),
                "Garaging County": garaging_county,
                "Farthest Operational Location": "{}, {}".format(
                    np.round(vin_radius_location[0], 4),
                    np.round(vin_radius_location[1], 4),
                ),
                "Farthest County": farthest_county,
                "Source": "Telematics Data",
            }
        )

        vin_garaging_info = {"Distance from Garaging (miles)": distance_from_dot_garage}
        log.info("Processing VIN to Get garaging location start", vin=vin)
        vin_all_garaging_info = get_garaging_location_rank_and_county(vin, vin_garaging_centers)
        log.info("Processing VIN to Get garaging location end", vin=vin)
        vin_garaging_info.update(vin_all_garaging_info)
        garaging_table.append(vin_garaging_info)

    log.info("Creating vin and garaging table")
    vin_table = json.dumps(vin_table)
    vin_table = pd.read_json(vin_table)

    garaging_table = json.dumps(garaging_table)
    garaging_table = pd.read_json(garaging_table)

    return vin_table, garaging_table


def get_fleet_run_rate(week_view: pd.DataFrame) -> pd.DataFrame:
    """
    This function gives week-on-week fleet run rate (Annual mileage) using weekly utilisation

    Parameters
    ----------
    week_view
        A dataframe of week-on-week trend of fleet level weekly and yearly utilisation

    Returns
    -------
    pd.DataFrame
        A dataframe of fleet run rate

    """
    fleet_run_rate = week_view[['week', 'available_vins', 'vin', 'yearly_utilisation']]

    weekly_vals = fleet_run_rate["yearly_utilisation"].values.ravel()

    # Get yearly run rate
    if weekly_vals.shape[0] > Constants.WEEKS_IN_A_YEAR:
        mask_length = Constants.WEEKS_IN_A_YEAR
        mask = np.ones(mask_length, dtype=float) / mask_length
        yearly_vals = np.convolve(weekly_vals, mask, mode="valid")
        yearly_vals = np.concatenate((mask, yearly_vals), axis=None)
        yearly_vals[:mask_length] = np.nan
        fleet_run_rate['yearly_mileage'] = yearly_vals[:-1]
    else:
        fleet_run_rate['yearly_mileage'] = np.nan

    # Get half-yearly run rate
    if weekly_vals.shape[0] > Constants.WEEKS_IN_6_MONTHS:
        mask_length = Constants.WEEKS_IN_6_MONTHS
        mask = np.ones(mask_length, dtype=float) / mask_length
        half_yearly_vals = np.convolve(weekly_vals, mask, mode="valid")
        half_yearly_vals = np.concatenate((mask, half_yearly_vals), axis=None)
        half_yearly_vals[:mask_length] = np.nan
        fleet_run_rate['half_yearly_mileage'] = half_yearly_vals[:-1]
    else:
        fleet_run_rate['half_yearly_mileage'] = np.nan

    # Get quarterly run-rate
    if weekly_vals.shape[0] > Constants.WEEKS_IN_A_QUARTER:
        mask_length = Constants.WEEKS_IN_A_QUARTER
        mask = np.ones(mask_length, dtype=float) / mask_length
        quarterly_vals = np.convolve(weekly_vals, mask, mode="valid")
        quarterly_vals = np.concatenate((mask, quarterly_vals), axis=None)
        quarterly_vals[:mask_length] = np.nan
        fleet_run_rate['quarterly_mileage'] = quarterly_vals[:-1]
    else:
        fleet_run_rate['quarterly_mileage'] = np.nan

    # Get monthly run-rate
    if weekly_vals.shape[0] > Constants.WEEKS_IN_A_MONTH:
        mask_length = Constants.WEEKS_IN_A_MONTH
        mask = np.ones(mask_length, dtype=float) / mask_length
        monthly_vals = np.convolve(weekly_vals, mask, mode="valid")
        monthly_vals = np.concatenate((mask, monthly_vals), axis=None)
        monthly_vals[:mask_length] = np.nan
        fleet_run_rate['monthly_mileage'] = monthly_vals[:-1]
    else:
        fleet_run_rate['monthly_mileage'] = np.nan

    fleet_run_rate.rename(columns={
        'week': 'iso_week_start',
        'available_vins': 'vin_count',
        'vin': 'active_vin_count',
        'yearly_utilisation': 'mileage',
    }, inplace=True)

    return fleet_run_rate
