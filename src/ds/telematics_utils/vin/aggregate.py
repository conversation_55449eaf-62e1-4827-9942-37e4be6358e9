import numpy as np
import pandas as pd
import geopandas as gpd

from utils.wrapper import timeit
from utils.constants import Constants
import telemetry.api.logging as logging
from telematics_utils.vin.config import VinDataFrameCols
from telematics_utils.vin.config import MileageAggregation
from telematics_utils.helpers.gps import get_hazard_zone_metrics
from telematics_utils.vin.data_quality import get_sampling_rate

log = logging.getLogger(__name__)


@timeit
def aggregate_hazard_zone_metrics(vin: str, vin_df: pd.DataFrame) -> tuple[float, float, float, float, np.ndarray]:
    """

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    Returns
    -------
    tuple[float, float, float, float, np.ndarray]
        A tuple containing distance, percentage distance, duration and percentage duration spent in hazard zones and
        a numpy array containing county level distance and duration

    """

    log.info("Getting hazard zone metrics", vin=vin)
    hazard_zone_data = get_hazard_zone_metrics(vin_df)

    exclusion_array = []
    exclusion_zones = hazard_zone_data.get("exclusion_zones", [])

    for zone in exclusion_zones:
        exc = exclusion_zones[zone].get("exc_zone")
        county = exclusion_zones[zone].get("county")
        distance = exclusion_zones[zone].get("distance")
        duration = exclusion_zones[zone].get("duration")
        exclusion_array.append([exc[0], exc[1], county, distance, duration])

    exclusion_array = np.array(exclusion_array)

    # Compute Hazard Zone metrics

    hazard_dist = hazard_zone_data["distance"]
    total_dist = np.nansum(vin_df["distance"])
    hazard_dist_pct = (hazard_dist * 100) / total_dist

    hazard_duration = hazard_zone_data["duration"]
    total_duration = np.nansum(vin_df["duration"])
    hazard_duration_pct = (hazard_duration * 100) / total_duration

    return hazard_dist, hazard_dist_pct, hazard_duration, hazard_duration_pct, exclusion_array


@timeit
def get_aggregate_mileage(
        vin: str,
        vin_df: pd.DataFrame,
        mode: MileageAggregation,
        addl_agg_col: list = None,
) -> pd.DataFrame:
    """
    This function computes aggregate distances using odometer and GPS values. The output pandas dataframe contains mode,
    distance and distance_gps where aggregate is the start date for which mileage is aggregated, distance and
    distance_gps are mileages using best distance i.e odo or model based and gps only distance.

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    mode: str
        Mode of aggregation i.e day, week or month

    addl_agg_col: list
        A list of additional column names to be used for aggregation.
        Optional parameter with default value None in which case only mode will be used for aggregation.

    Returns
    -------
    pd.DataFrame
        A pandas dataframe containing aggregate mileages.

    Raises
    -------
    TypeError
        TypeError is thrown when the aggregation mode is not supported

    """
    if mode == MileageAggregation.DAY:
        period = "D"
    elif mode == MileageAggregation.WEEK:
        period = "W"
    elif mode == MileageAggregation.MONTH:
        period = "M"
    else:
        raise TypeError("{}: Invalid Aggregation Type".format(mode))

    if addl_agg_col is None:
        addl_agg_col = list()

    log.info("Getting {} level mileages for each {} combination".format(mode.value, addl_agg_col), vin=vin)

    vin_df[mode.value] = vin_df.date.dt.to_period(period).dt.start_time
    index_cols = [mode.value] + addl_agg_col
    pivot = vin_df.pivot_table(
        index=index_cols, 
        values=[VinDataFrameCols.DISTANCE, VinDataFrameCols.GPS_DISTANCE, VinDataFrameCols.ODO_DISTANCE], 
        aggfunc=np.nansum
    )
    pivot = pivot.reset_index()

    pivot[mode.value] = pd.to_datetime(pivot[mode.value]).dt.date
    return pivot


@timeit
def get_aggregate_driving_duration(
    vin: str, 
    vin_df: pd.DataFrame, 
    mode: MileageAggregation, 
    addl_agg_col: list = None,
    max_non_moving_speed: float = 3.0,
) -> pd.DataFrame:
    """
    This function computes aggregate moving and stationary duration in seconds at required aggregation level.
    The output pandas dataframe contains [aggregation columns], duration_moving and duration_stationary columns.

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    mode: str
        Mode of aggregation i.e day, week or month
    
    addl_agg_col: list
        A list of additional column names to be used for aggregation.
        Optional parameter with default value None in which case only mode will be used for aggregation.
    
    max_non_moving_speed: float
        Maximum speed in mph above which a vehicle will be considered moving.
        Optional parameter with default value 3.0

    Returns
    -------
    pd.DataFrame
        A pandas dataframe containing aggregate mileages.

    Raises
    -------
    TypeError
        TypeError is thrown when the aggregation mode is not supported

    """
    req_cols = ["duration_stationary", "duration_moving",]

    if mode == MileageAggregation.DAY:
        period = "D"
    elif mode == MileageAggregation.WEEK:
        period = "W"
    elif mode == MileageAggregation.MONTH:
        period = "M"
    else:
        raise TypeError("{}: Invalid Aggregation Type".format(mode))
    
    if addl_agg_col is None:
        addl_agg_col = list()

    log.info("Getting {} level moving and non-moving duration for each {} combination".format(mode.value, addl_agg_col), vin=vin)

    index_cols = [mode.value] + addl_agg_col

    moving_veh_idx = vin_df.speed > max_non_moving_speed
    vin_df.loc[moving_veh_idx, "is_moving"] = "1"
    vin_df.loc[~moving_veh_idx, "is_moving"] = "0"
    duration_pivot = vin_df.pivot_table(index=index_cols, columns="is_moving", values=VinDataFrameCols.TIME_GAP, aggfunc=np.nansum).fillna(0).reset_index()
    duration_pivot = duration_pivot.rename(columns={"0": "duration_stationary", "1": "duration_moving"})
    duration_pivot[mode.value] = pd.to_datetime(duration_pivot[mode.value]).dt.date
    
    for col in req_cols:
        if col not in duration_pivot.columns:
            duration_pivot[col] = 0

    return duration_pivot


def get_aggregate_driving_metrics(
    vin: str, 
    vin_df: pd.DataFrame, 
    mode: MileageAggregation, 
    addl_agg_col: list = None,
    max_non_moving_speed: float = 3.0,
) -> pd.DataFrame:
    """
    This function computes aggregate driving summary (distances, durations) at required aggregation level.

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    mode: str
        Mode of aggregation i.e day, week or month
    
    addl_agg_col: list
        A list of additional column names to be used for aggregation.
        Optional parameter with default value None in which case only mode will be used for aggregation.
    
    max_non_moving_speed: float
        Maximum speed in mph above which a vehicle will be considered moving.
        Optional parameter with default value 3.0

    Returns
    -------
    pd.DataFrame
        A pandas dataframe containing aggregate mileages.

    Raises
    -------
    TypeError
        TypeError is thrown when the aggregation mode is not supported

    """
    distance_pivot = get_aggregate_mileage(vin=vin, vin_df=vin_df, mode=mode, addl_agg_col=addl_agg_col)
    duration_pivot = get_aggregate_driving_duration(
        vin=vin, vin_df=vin_df, mode=mode, addl_agg_col=addl_agg_col, max_non_moving_speed=max_non_moving_speed
    )

    if addl_agg_col is None:
        addl_agg_col = list()
    
    index_cols = [mode.value] + addl_agg_col

    driving_metrics = pd.merge(distance_pivot, duration_pivot, on=index_cols, how="outer")

    return driving_metrics


def get_aggregate_sampling_metrics(vin: str, vin_df: pd.DataFrame, mode: MileageAggregation) -> pd.DataFrame:
    """
    This function computes GPS data sampling rates and sample counts at required time (Day/Week/Month) granularity
    for moving and stationary modes.

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    mode: str
        Mode of aggregation i.e. day, week or month

    Returns
    -------
    pd.DataFrame
        A pandas dataframe containing aggregate mileages.

    Raises
    -------
    TypeError
        TypeError is thrown when the aggregation mode is not supported

    """
    if mode == MileageAggregation.DAY:
        period = "D"
    elif mode == MileageAggregation.WEEK:
        period = "W"
    elif mode == MileageAggregation.MONTH:
        period = "M"
    else:
        raise TypeError("{}: Invalid Aggregation Type".format(mode))

    log.info("Getting {} level sampling metrics".format(mode.value), vin=vin)

    vin_df[mode.value] = vin_df.date.dt.to_period(period).dt.start_time
    moving_veh_idx = vin_df.speed > 0

    vin_df.loc[moving_veh_idx, "is_moving"] = "1"
    vin_df.loc[~moving_veh_idx, "is_moving"] = "0"
    pivot = vin_df.pivot_table(index=mode.value, columns="is_moving", values="duration", aggfunc=[np.nansum, len])
    pivot.columns = ["_".join(col) for col in pivot.columns]
    pivot.reset_index(inplace=True)

    pivot.rename(columns={"nansum_0": "stationary_duration",
                          "nansum_1": "moving_duration",
                          "len_0": "stationary_samples",
                          "len_1": "moving_samples",
                          },
                 inplace=True
                 )

    # Add moving sampling rates
    moving_vin_df = vin_df[moving_veh_idx]
    sampling_rates_df = moving_vin_df.groupby(mode.value, as_index=False)['duration'].agg(get_sampling_rate)
    sampling_rates_df.rename(columns={"duration": "moving_sampling_rates"}, inplace=True)
    pivot = pd.merge(pivot, sampling_rates_df, on=mode.value, how='left')

    # Add stationary sampling rates
    stationary_vin_df = vin_df[~moving_veh_idx]
    sampling_rates_df = stationary_vin_df.groupby(mode.value, as_index=False)['duration'].agg(get_sampling_rate)
    sampling_rates_df.rename(columns={"duration": "stationary_sampling_rates"}, inplace=True)
    pivot = pd.merge(pivot, sampling_rates_df, on=mode.value, how='left')

    pivot[mode.value] = pd.to_datetime(pivot[mode.value]).dt.date
    return pivot


def get_aggregate_odometer_reading(vin: str, vin_df: pd.DataFrame, mode: MileageAggregation) -> pd.DataFrame:
    """
    This function computes aggregate odometer reading (min, max). The output pandas dataframe contains mode,
    minimum and maximum odometer readings where aggregate is the start date for which odometer value is aggregated.

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    mode: str
        Mode of aggregation i.e day, week or month

    Returns
    -------
    pd.DataFrame
        A pandas dataframe containing aggregate odometer readings.

    Raises
    -------
    TypeError
        TypeError is thrown when the aggregation mode is not supported

    """
    if mode == MileageAggregation.DAY:
        period = "D"
    elif mode == MileageAggregation.WEEK:
        period = "W"
    elif mode == MileageAggregation.MONTH:
        period = "M"
    else:
        raise TypeError("{}: Invalid Aggregation Type".format(mode))

    log.info("Getting {} level odometer aggregates".format(mode.value), vin=vin)

    vin_df[mode.value] = vin_df.date.dt.to_period(period).dt.start_time
    pivot = vin_df.pivot_table(index=mode.value, values=["valuemetres"], aggfunc=[np.min, np.max])
    pivot.columns = ["_".join(col) for col in pivot.columns]
    pivot = pivot.reset_index()
    pivot.rename(columns={"amin_valuemetres": "odometer_reading_min", "amax_valuemetres": "odometer_reading_max"},
                 inplace=True)
    pivot[mode.value] = pd.to_datetime(pivot[mode.value]).dt.date
    return pivot


def get_aggregate_mileage_billing(vin: str, vin_df: pd.DataFrame, mode: MileageAggregation) -> pd.DataFrame:
    """
    Computes aggregated distance for requested mode.

    We treat odometer as primary source of truth and return odometer based distances whenever present and return
    GPS distances whenever odometer distances are not available. The source column highlights the same.

    Note: Odometer distances are computed as-
        `Max odometer value current window - Max odometer value of previous window`
        Window is mode
        In case of data gap we will see a large odometer jump which is currently okay as business logic.
        If things change and we want to measure only logged distance then we will need to switch to
        `Max odometer value current window - Min odometer value current window`

        GPS distances are computed by summing GPS activity for that window.

    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration

    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime

    mode: str
        Mode of aggregation. Supported modes are
        - day (MileageAggregation.DAY)
        - week (MileageAggregation.WEEK)
        - month (MileageAggregation.MONTH)

    Returns
    -------
    pd.DataFrame
        A pandas dataframe containing aggregate mileages. Contains mode, distance, source, odometer_start and
        odometer_end.

        Example - odometer data is available (valuemetre key)
                        day    distance    source odometer_start  odometer_end
            0   2021-11-19       860.2  Odometer   1277823469.9  1277824330.1
            1   2021-11-20    0.217418  Odometer   1277824330.1  1277824680.0
            2   2021-11-21         0.0  Odometer   1277824680.0  1277824680.0
            3   2021-11-22  403.531075  Odometer   1277824680.0  1278474098.7
            4   2021-11-23   356.38156  Odometer   1278474098.7  1279047637.8

        Example - odometer data is not available
                        day    distance source  odometer_start  odometer_end
            0   2021-11-19    0.539848    GPS             NaN           NaN
            1   2021-11-20    0.163553    GPS             NaN           NaN
            2   2021-11-21    0.005507    GPS             NaN           NaN
            3   2021-11-22  400.884267    GPS             NaN           NaN
            4   2021-11-23  355.072244    GPS             NaN           NaN
    Raises
    -------
    TypeError
        TypeError is thrown when the aggregation mode is not supported

    Important Todo
    - Odometer distances in `get_aggregate_mileage` is converted from metres to miles for every record and then
        aggregated. In this function we have aggregated the values in metres first and then converted to miles
    - GPS based distances are unchanged and should be changed to conversion after aggregation
    """
    if mode == MileageAggregation.DAY:
        period = "D"
    elif mode == MileageAggregation.WEEK:
        period = "W"
    elif mode == MileageAggregation.MONTH:
        period = "M"
    else:
        raise TypeError("{}: Invalid Aggregation Type".format(mode))

    log.info("Getting {} level mileages".format(mode.value), vin=vin)
    vin_df[mode.value] = vin_df.date.dt.to_period(period).dt.start_time

    # -- Step 1a: compute odometer distances
    odometer_max_pivot = vin_df.pivot_table(index=mode.value, values=["valuemetres"], aggfunc=np.max)
    odometer_max_pivot.reset_index(inplace=True)

    odometer_previous_max_pivot = odometer_max_pivot.shift(1)
    odo_distances = (odometer_max_pivot - odometer_previous_max_pivot) / (
        Constants.METERS_IN_KM * Constants.KM_TO_MILES
    )
    if not odo_distances.empty:
        odo_distances.rename(columns={"valuemetres": "distance"}, inplace=True)
        odo_distances[mode.value] = odometer_max_pivot[mode.value]
        odo_distances["source"] = "Odometer"
        odo_distances["odometer_start"] = odometer_previous_max_pivot["valuemetres"]
        odo_distances["odometer_end"] = odometer_max_pivot["valuemetres"]

        # -- Step 1b: create first distance record from min and max of odometer from same mode duration instead of max
        # from previous window.
        first_mode_key = odometer_max_pivot.loc[0][mode.value]
        first_record_odo_start = vin_df[vin_df[mode.value] == first_mode_key].valuemetres.min()
        first_record_odo_end = vin_df[vin_df[mode.value] == first_mode_key].valuemetres.max()
        if first_record_odo_start >= 0 and first_record_odo_end >= 0:
            distance = (first_record_odo_end - first_record_odo_start) / (
                Constants.METERS_IN_KM * Constants.KM_TO_MILES
            )

            odo_distances.at[0, ["distance", "source", "odometer_start", "odometer_end"]] = [
                distance,
                "Odometer",
                first_record_odo_start,
                first_record_odo_end,
            ]
        mileage_df = odo_distances
    # -- Step 2: compute gps distances if odometer data is not available
    else:
        gps_distances = vin_df.pivot_table(index=mode.value, values=["distance"], aggfunc=np.nansum)
        # gps_distances.rename(columns={"distance_gps": "distance"}, inplace=True)
        gps_distances["source"] = "GPS"
        gps_distances["odometer_start"] = np.nan
        gps_distances["odometer_end"] = np.nan
        gps_distances.reset_index(inplace=True)
        gps_distances.rename(columns={"index": mode.value})

        mileage_df = gps_distances

    mileage_df["distance"] = mileage_df["distance"].round(4)
    mileage_df[mode.value] = pd.to_datetime(mileage_df[mode.value]).dt.date

    return mileage_df


def get_continuous_driving_events(vin: str, vin_df: pd.DataFrame, distance_col: str = VinDataFrameCols.GPS_DISTANCE,
                                  distance_buffer: float = 0.0, agg_distance_buffer: float = 0.5) -> pd.DataFrame:
    """
    This function gives a dataframe for continuous driving events from given telematics data
    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration
    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime
    distance_col: str
        Name of distance column
    distance_buffer: float
        Distance buffer to be considered as continuous driving. If a sample has distance below this buffer it will not
        be considered as driving on
    agg_distance_buffer: float
        Aggregate distance buffer to be considered as continuous driving
    Returns
    -------
    pd.DataFrame
        A dataframe of continuous driving events
    """
    log.info("Getting continuous driving events", vin=vin)

    first_timestamp = vin_df[VinDataFrameCols.TIMESTAMP].min()
    last_timestamp = vin_df[VinDataFrameCols.TIMESTAMP].max()

    continuous_driving_events = pd.DataFrame()

    run_id_col = 'run_id'
    mask = vin_df[distance_col] > distance_buffer
    vin_df[run_id_col] = np.nan
    vin_df.loc[mask != mask.shift(1), run_id_col] = 1
    vin_df[run_id_col] = vin_df[run_id_col].fillna(0)
    vin_df[run_id_col] = vin_df[run_id_col].cumsum()

    pivot = vin_df.groupby(run_id_col).agg(
        {VinDataFrameCols.TIMESTAMP: [np.min, np.max], distance_col: [np.nansum, len]})
    pivot.columns = ["_".join(col) for col in pivot.columns]
    pivot.reset_index(drop=True, inplace=True)

    column_map = {
        "{}_amin".format(VinDataFrameCols.TIMESTAMP): "start_ts",
        "{}_amax".format(VinDataFrameCols.TIMESTAMP): "end_ts",
        "{}_nansum".format(distance_col): "distance",
        "{}_len".format(distance_col): "sample_count"
    }

    pivot.rename(columns=column_map, inplace=True)
    pivot = pivot[pivot["distance"] > agg_distance_buffer]

    if len(pivot) > 0:
        pivot["duration"] = (pivot["end_ts"] - pivot["start_ts"]).dt.seconds
        pivot["first_timestamp"] = first_timestamp
        pivot["last_timestamp"] = last_timestamp
        continuous_driving_events = pivot

    return continuous_driving_events


def get_halt_events(vin: str, vin_df: pd.DataFrame, speed_col: str = VinDataFrameCols.SPEED,
                    speed_limit: float = 3) -> pd.DataFrame:
    """
    This function gives a dataframe for halt events (duration in which a vehicle is considered not moving)
    from given telematics data
    Parameters
    ----------
    vin: str
        Vehicle Identification Number of the vehicle under consideration
    vin_df: pd.DataFrame
        Pandas dataframe containing telematics data for vehicle sorted by datetime
    speed_col: str
        Name of speed column
    speed_limit: float
        Speed in miles above which a vehicle will be considered moving
    Returns
    -------
    pd.DataFrame
        A dataframe of halt events
    """
    log.info("Getting halt events", vin=vin)

    first_timestamp = vin_df[VinDataFrameCols.TIMESTAMP].min()
    last_timestamp = vin_df[VinDataFrameCols.TIMESTAMP].max()

    halt_events = pd.DataFrame()
    vin_df["is_moving"] = vin_df[speed_col] > speed_limit
    vin_df["halt_id"] = vin_df["is_moving"].cumsum()

    valid_halt_ids = vin_df[~vin_df["is_moving"]]["halt_id"].unique().tolist()
    halt_df = vin_df[vin_df["halt_id"].isin(valid_halt_ids)]

    if len(halt_df) > 0:
        halt_events = halt_df.pivot_table(
            index="halt_id", values=VinDataFrameCols.TIMESTAMP, aggfunc=[np.nanmin, np.nanmax])
        halt_events.columns = ["_".join(col) for col in halt_events.columns]

        halt_events["duration"] = (halt_events["nanmax_date"] - halt_events["nanmin_date"]).dt.seconds
        halt_events.rename(columns={"nanmax_date": "start_ts", "nanmin_date": "end_ts"}, inplace=True)
        halt_events.reset_index(drop=True, inplace=True)

        halt_events["first_timestamp"] = first_timestamp
        halt_events["last_timestamp"] = last_timestamp

    return halt_events
