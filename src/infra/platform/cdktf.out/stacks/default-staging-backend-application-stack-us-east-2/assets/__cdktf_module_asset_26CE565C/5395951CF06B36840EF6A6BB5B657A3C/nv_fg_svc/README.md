# `nv_fg_svc` Module

Fargate module for Nirvana Services

<!-- BEGIN_TF_DOCS -->




## Outputs

| Name | Description |
|------|-------------|
| <a name="output_arn"></a> [arn](#output\_arn) | The Amazon Resource Name (ARN) that identifies the service. |
| <a name="output_cluster"></a> [cluster](#output\_cluster) | The Amazon Resource Name (ARN) of cluster which the service runs on. |
| <a name="output_discovery_service_name"></a> [discovery\_service\_name](#output\_discovery\_service\_name) | The name of the `aws_service_discovery_service` |
| <a name="output_lb_access_sg_arn"></a> [lb\_access\_sg\_arn](#output\_lb\_access\_sg\_arn) | The ARN of the security group |
| <a name="output_lb_access_sg_id"></a> [lb\_access\_sg\_id](#output\_lb\_access\_sg\_id) | The ID of the security group |
| <a name="output_lb_arn"></a> [lb\_arn](#output\_lb\_arn) | The ARN of the load balancer. |
| <a name="output_lb_dns_name"></a> [lb\_dns\_name](#output\_lb\_dns\_name) | The DNS name of the load balancer. |
| <a name="output_lb_zone_id"></a> [lb\_zone\_id](#output\_lb\_zone\_id) | The canonical hosted zone ID of the load balancer (to be used in a Route 53 Alias record). |
| <a name="output_name"></a> [name](#output\_name) | The name of the service. |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_ecs_alb"></a> [ecs\_alb](#module\_ecs\_alb) | ../nv_svc_alb | n/a |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_ecs_cluster_arn"></a> [ecs\_cluster\_arn](#input\_ecs\_cluster\_arn) | ARN of an ECS cluster | `any` | n/a | yes |
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Name prefix for resources on AWS | `any` | n/a | yes |
| <a name="input_private_subnets"></a> [private\_subnets](#input\_private\_subnets) | The private subnets associated with the task or service. | `list(string)` | n/a | yes |
| <a name="input_service_discovery_namespace_id"></a> [service\_discovery\_namespace\_id](#input\_service\_discovery\_namespace\_id) | The service discovery namespace id to create the service\_registry. We don't strictly need this for some of our services, but we do it for future proofing. It doesn't hurt to have DNS names for our services. | `string` | n/a | yes |
| <a name="input_task_definition_arn"></a> [task\_definition\_arn](#input\_task\_definition\_arn) | (Required) The full ARN of the task definition that you want to run in your service. | `any` | n/a | yes |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | ID of the VPC | `any` | n/a | yes |
| <a name="input_container_name"></a> [container\_name](#input\_container\_name) | Name of the running container. Required if create\_alb is true. | `string` | `""` | no |
| <a name="input_container_port"></a> [container\_port](#input\_container\_port) | Port of the running container. Required if create\_alb is true. | `number` | `0` | no |
| <a name="input_create_alb"></a> [create\_alb](#input\_create\_alb) | (Optional) Create and assign a load balancer with the service. Default false. | `bool` | `false` | no |
| <a name="input_deployment_maximum_percent"></a> [deployment\_maximum\_percent](#input\_deployment\_maximum\_percent) | (Optional) The upper limit (as a percentage of the service's desiredCount) of the number of running tasks that can be running in a service during a deployment. | `number` | `200` | no |
| <a name="input_deployment_minimum_healthy_percent"></a> [deployment\_minimum\_healthy\_percent](#input\_deployment\_minimum\_healthy\_percent) | (Optional) The lower limit (as a percentage of the service's desiredCount) of the number of running tasks that must remain running and healthy in a service during a deployment. | `number` | `100` | no |
| <a name="input_desired_count"></a> [desired\_count](#input\_desired\_count) | (Optional) The number of instances of the task definition to place and keep running. Defaults to 0. | `number` | `1` | no |
| <a name="input_force_new_deployment"></a> [force\_new\_deployment](#input\_force\_new\_deployment) | (Optional) Enable to force a new task deployment of the service. This can be used to update tasks to use a newer Docker image with same image/tag combination (e.g. myimage:latest), roll Fargate tasks onto a newer platform version, or immediately deploy ordered\_placement\_strategy and placement\_constraints updates. | `bool` | `false` | no |
| <a name="input_lb_default_certificate_arn"></a> [lb\_default\_certificate\_arn](#input\_lb\_default\_certificate\_arn) | (Optional) The ARN of the default SSL server certificate. Required if var.https\_ports is set. | `string` | `null` | no |
| <a name="input_lb_http_ingress_cidr_blocks"></a> [lb\_http\_ingress\_cidr\_blocks](#input\_lb\_http\_ingress\_cidr\_blocks) | List of CIDR blocks to allowed to access the Load Balancer through HTTP | `list(string)` | <pre>[<br>  "0.0.0.0/0"<br>]</pre> | no |
| <a name="input_lb_http_ports"></a> [lb\_http\_ports](#input\_lb\_http\_ports) | Map of Objects defining the HTTP listener's behaviour. | <pre>map(object({<br>    listener_port = number<br>    host          = string<br>    path          = string<br>    target_port   = string<br>    protocol      = string<br>    query         = string<br>    status_code   = string<br>  }))</pre> | <pre>{<br>  "force_https": {<br>    "host": "#{host}",<br>    "listener_port": 80,<br>    "path": "/#{path}",<br>    "protocol": "HTTPS",<br>    "query": "#{query}",<br>    "status_code": "HTTP_301",<br>    "target_port": "443"<br>  }<br>}</pre> | no |
| <a name="input_lb_https_ingress_cidr_blocks"></a> [lb\_https\_ingress\_cidr\_blocks](#input\_lb\_https\_ingress\_cidr\_blocks) | List of CIDR blocks to allowed to access the Load Balancer through HTTPS | `list(string)` | <pre>[<br>  "0.0.0.0/0"<br>]</pre> | no |
| <a name="input_lb_https_ports"></a> [lb\_https\_ports](#input\_lb\_https\_ports) | Map of objects defining the HTTPS listener's behaviour. | <pre>map(object({<br>    listener_port = number<br>  }))</pre> | <pre>{<br>  "force_https": {<br>    "listener_port": 443<br>  }<br>}</pre> | no |
| <a name="input_lb_internal"></a> [lb\_internal](#input\_lb\_internal) | (Optional) If true, the LB will be internal. | `bool` | `false` | no |
| <a name="input_lb_load_balancing_algorithm_type"></a> [lb\_load\_balancing\_algorithm\_type](#input\_lb\_load\_balancing\_algorithm\_type) | (Optional) Determines how the load balancer selects targets when routing requests. The value is round\_robin or least\_outstanding\_requests. The default is round\_robin. | `string` | `"round_robin"` | no |
| <a name="input_lb_slow_start"></a> [lb\_slow\_start](#input\_lb\_slow\_start) | (Optional) The amount time for targets to warm up before the load balancer sends them a full share of requests. The range is 30-900 seconds or 0 to disable. The default value is 0 seconds. | `number` | `0` | no |
| <a name="input_lb_ssl_policy"></a> [lb\_ssl\_policy](#input\_lb\_ssl\_policy) | (Optional) The name of the SSL Policy for the listener. . Required if var.https\_ports is set. | `string` | `null` | no |
| <a name="input_lb_target_group_deregistration_delay"></a> [lb\_target\_group\_deregistration\_delay](#input\_lb\_target\_group\_deregistration\_delay) | (Optional) Amount time for Elastic Load Balancing to wait before changing the state of a deregistering target from draining to unused. The range is 0-3600 seconds. The default value is 300 seconds. | `number` | `300` | no |
| <a name="input_lb_target_group_health_check_enabled"></a> [lb\_target\_group\_health\_check\_enabled](#input\_lb\_target\_group\_health\_check\_enabled) | (Optional) Indicates whether health checks are enabled. Defaults to true. | `bool` | `true` | no |
| <a name="input_lb_target_group_health_check_healthy_threshold"></a> [lb\_target\_group\_health\_check\_healthy\_threshold](#input\_lb\_target\_group\_health\_check\_healthy\_threshold) | (Optional) The number of consecutive health checks successes required before considering an unhealthy target healthy. Defaults to 3. | `number` | `3` | no |
| <a name="input_lb_target_group_health_check_interval"></a> [lb\_target\_group\_health\_check\_interval](#input\_lb\_target\_group\_health\_check\_interval) | (Optional) The approximate amount of time, in seconds, between health checks of an individual target. Minimum value 5 seconds, Maximum value 300 seconds. Default 30 seconds. | `number` | `30` | no |
| <a name="input_lb_target_group_health_check_matcher"></a> [lb\_target\_group\_health\_check\_matcher](#input\_lb\_target\_group\_health\_check\_matcher) | The HTTP codes to use when checking for a successful response from a target. You can specify multiple values (for example, "200,202") or a range of values (for example, "200-299"). Default is 200. | `string` | `"200"` | no |
| <a name="input_lb_target_group_health_check_path"></a> [lb\_target\_group\_health\_check\_path](#input\_lb\_target\_group\_health\_check\_path) | The destination for the health check request. | `string` | `"/"` | no |
| <a name="input_lb_target_group_health_check_timeout"></a> [lb\_target\_group\_health\_check\_timeout](#input\_lb\_target\_group\_health\_check\_timeout) | (Optional) The amount of time, in seconds, during which no response means a failed health check. The range is 2 to 120 seconds, and the default is 5 seconds. | `number` | `5` | no |
| <a name="input_lb_target_group_health_check_unhealthy_threshold"></a> [lb\_target\_group\_health\_check\_unhealthy\_threshold](#input\_lb\_target\_group\_health\_check\_unhealthy\_threshold) | (Optional) The number of consecutive health check failures required before considering the target unhealthy. Defaults to 3. | `number` | `3` | no |
| <a name="input_public_subnets"></a> [public\_subnets](#input\_public\_subnets) | (Optional)The public subnets associated with the task or service. Required if lb\_internal is true. | `list(string)` | `[]` | no |
| <a name="input_security_groups"></a> [security\_groups](#input\_security\_groups) | (Optional) The security groups associated with the task or service. | `list(string)` | `[]` | no |
| <a name="input_stickiness_cookie_duration"></a> [stickiness\_cookie\_duration](#input\_stickiness\_cookie\_duration) | (Optional) The time period, in seconds, during which requests from a client should be routed to the same target. | `number` | `86400` | no |
| <a name="input_stickiness_cookie_name"></a> [stickiness\_cookie\_name](#input\_stickiness\_cookie\_name) | (Optional) Name of the application based cookie. Only needed when type is app\_cookie. | `string` | `null` | no |
| <a name="input_stickiness_enabled"></a> [stickiness\_enabled](#input\_stickiness\_enabled) | (Optional) Indicates whether stickiness is enabled. Defaults to false. | `bool` | `false` | no |
| <a name="input_stickiness_type"></a> [stickiness\_type](#input\_stickiness\_type) | (Optional) The type of sticky sessions. The only current possible values are lb\_cookie, app\_cookie for ALBs, and source\_ip for NLBs. | `string` | `"lb_cookie"` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Resource tags | `map(string)` | `{}` | no |

## Resources

| Name | Type |
|------|------|
| [aws_ecs_service.service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_service) | resource |
| [aws_security_group.ecs_tasks_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_service_discovery_service.service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/service_discovery_service) | resource |


<!-- END_TF_DOCS -->