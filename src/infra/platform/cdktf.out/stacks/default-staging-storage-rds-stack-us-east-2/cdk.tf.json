{"//": {"metadata": {"backend": "s3", "overrides": {"stack": ["terraform"]}, "stackName": "default-staging-storage-rds-stack-us-east-2", "version": "0.20.10"}, "outputs": {"default-staging-storage-rds-stack-us-east-2": {"cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.address": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.password": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.port": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.username": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername", "cross-stack-output-aws_db_instance.NhtsaDB_F7136F88.address": "cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address", "cross-stack-output-aws_db_instance.NhtsaDB_F7136F88.password": "cross-stack-output-aws_db_instanceNhtsaDB_F7136F88password"}}}, "data": {"aws_iam_policy_document": {"backup_policy": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/backup_policy", "uniqueId": "backup_policy"}}, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "principals": [{"identifiers": ["backup.amazonaws.com"], "type": "Service"}]}]}, "migration_lambda_assume_role_policy": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/migration_lambda_assume_role_policy", "uniqueId": "migration_lambda_assume_role_policy"}}, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "principals": [{"identifiers": ["lambda.amazonaws.com"], "type": "Service"}]}]}, "rds_assume_role_policy": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/rds_assume_role_policy", "uniqueId": "rds_assume_role_policy"}}, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "principals": [{"identifiers": ["rds.amazonaws.com"], "type": "Service"}]}]}}, "terraform_remote_state": {"cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/root/default-root-coreinfra-ecr-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-coreinfra-legacy-network-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}}}, "output": {"cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.address}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.password}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.port}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.username}"}, "cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address": {"sensitive": true, "value": "${aws_db_instance.NhtsaDB_F7136F88.address}"}, "cross-stack-output-aws_db_instanceNhtsaDB_F7136F88password": {"sensitive": true, "value": "${aws_db_instance.NhtsaDB_F7136F88.password}"}}, "provider": {"aws": [{"allowed_account_ids": ["************"], "assume_role": [{"role_arn": "arn:aws:iam::************:role/AdministratorAccessForManagementAccountUsers"}], "default_tags": [{"tags": {"environment": "staging", "group": "storage", "infraWorkspace": "default", "region": "us-east-2", "stackName": "rds-stack"}}], "region": "us-east-2"}], "postgresql": [{"database": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "host": "${aws_db_instance.ApplicationDB_49B5873C.address}", "password": "${aws_db_instance.ApplicationDB_49B5873C.password}", "port": "${aws_db_instance.ApplicationDB_49B5873C.port}", "sslmode": "disable", "superuser": false, "username": "${aws_db_instance.ApplicationDB_49B5873C.username}"}], "random": [{}]}, "resource": {"aws_backup_plan": {"ApplicationDB_plan_C9EDBF75": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/plan", "uniqueId": "ApplicationDB_plan_C9EDBF75"}}, "name": "app_db_backup_plan", "rule": [{"enable_continuous_backup": true, "lifecycle": {"delete_after": 30}, "rule_name": "app_db_hourly_backup_rule", "schedule": "cron(0 * ? * * *)", "target_vault_name": "${aws_backup_vault.backup_vault.name}"}]}}, "aws_backup_selection": {"ApplicationDB_selection_CFFF8A13": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/selection", "uniqueId": "ApplicationDB_selection_CFFF8A13"}}, "iam_role_arn": "${aws_iam_role.backup_role.arn}", "name": "app_db_backup_selection", "plan_id": "${aws_backup_plan.ApplicationDB_plan_C9EDBF75.id}", "resources": ["${aws_db_instance.ApplicationDB_49B5873C.arn}"]}}, "aws_backup_vault": {"backup_vault": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/backup_vault", "uniqueId": "backup_vault"}}, "name": "rds_cont_backup_vault"}}, "aws_db_instance": {"ApplicationDB_49B5873C": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/ApplicationDB", "uniqueId": "ApplicationDB_49B5873C"}}, "allocated_storage": 200, "apply_immediately": true, "auto_minor_version_upgrade": false, "db_name": "postgres", "db_subnet_group_name": "${aws_db_subnet_group.ApplicationDB_SubnetGroup_18AD770F.name}", "deletion_protection": true, "enabled_cloudwatch_logs_exports": ["postgresql"], "engine": "postgres", "engine_version": "16.6", "identifier": "staging-application-database", "instance_class": "db.t3.medium", "maintenance_window": "Mon:07:00-Mon:08:00", "max_allocated_storage": 500, "multi_az": false, "parameter_group_name": "${aws_db_parameter_group.ApplicationDB_ParameterGroup-postgres16-staging-application-database_501AB82E.name}", "password": "${aws_secretsmanager_secret_version.ApplicationDB_SecretValue_7841D7E4.secret_string}", "port": 5432, "skip_final_snapshot": true, "storage_type": "gp3", "username": "postgres"}, "ApplicationDB_ApplicationDBReadonlyReplica_CF83BAF4": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/ApplicationDBReadonlyReplica", "uniqueId": "ApplicationDB_ApplicationDBReadonlyReplica_CF83BAF4"}}, "allocated_storage": 200, "identifier": "readonly-staging-application-database", "instance_class": "db.t3.medium", "max_allocated_storage": 500, "parameter_group_name": "${aws_db_instance.ApplicationDB_49B5873C.parameter_group_name}", "replicate_source_db": "${aws_db_instance.ApplicationDB_49B5873C.identifier}", "skip_final_snapshot": true, "storage_type": "gp3"}, "NhtsaDB_F7136F88": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/NhtsaDB", "uniqueId": "NhtsaDB_F7136F88"}}, "allocated_storage": 20, "apply_immediately": false, "auto_minor_version_upgrade": false, "db_subnet_group_name": "${aws_db_subnet_group.NhtsaDB_SubnetGroup_4E1F527E.name}", "deletion_protection": true, "enabled_cloudwatch_logs_exports": ["error"], "engine": "sqlserver-ex", "engine_version": "15.00.4073.23.v1", "identifier": "staging-nhtsa-database", "instance_class": "db.t3.small", "license_model": "license-included", "maintenance_window": "Mon:07:00-Mon:08:00", "max_allocated_storage": 100, "option_group_name": "${aws_db_option_group.NhtsaDB_OptionGroup_D2222933.name}", "password": "${aws_secretsmanager_secret_version.NhtsaDB_SecretValue_EBD7DE9E.secret_string}", "port": 1433, "skip_final_snapshot": true, "storage_type": "gp2", "username": "sa", "vpc_security_group_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"]}}, "aws_db_option_group": {"NhtsaDB_OptionGroup_D2222933": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/OptionGroup", "uniqueId": "NhtsaDB_OptionGroup_D2222933"}}, "engine_name": "sqlserver-ex", "major_engine_version": "15.00", "name": "staging-nhtsa-database", "option": [{"option_name": "SQLSERVER_BACKUP_RESTORE", "option_settings": [{"name": "IAM_ROLE_ARN", "value": "${aws_iam_role.NhtsaDB_staging-nhtsa-database_Role_E8C41EC3.arn}"}]}]}}, "aws_db_parameter_group": {"ApplicationDB_ParameterGroup-postgres16-staging-application-database_501AB82E": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/ParameterGroup-postgres16-staging-application-database", "uniqueId": "ApplicationDB_ParameterGroup-postgres16-staging-application-database_501AB82E"}}, "family": "postgres16", "name": "staging-application-database-postgres16", "parameter": [{"name": "log_min_duration_statement", "value": "100"}, {"name": "rds.log_retention_period", "value": "10080"}, {"name": "log_statement", "value": "ddl"}, {"name": "rds.force_ssl", "value": "0"}]}}, "aws_db_subnet_group": {"ApplicationDB_SubnetGroup_18AD770F": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/SubnetGroup", "uniqueId": "ApplicationDB_SubnetGroup_18AD770F"}}, "name": "staging-application-database", "subnet_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"]}, "NhtsaDB_SubnetGroup_4E1F527E": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/SubnetGroup", "uniqueId": "NhtsaDB_SubnetGroup_4E1F527E"}}, "name": "staging-nhtsa-database", "subnet_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"]}}, "aws_iam_policy": {"NhtsaDB_Policy_CF40B012": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/Policy", "uniqueId": "NhtsaDB_Policy_CF40B012"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": [\n        \"s3:GetBucketLocation\",\n        \"s3:ListBucket\"\n      ],\n      \"Resource\": \"arn:aws:s3:::staging-nirvana-nhtsa-dumps\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:AbortMultipartUpload\",\n        \"s3:GetObject\",\n        \"s3:ListMultipartUploadParts\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::staging-nirvana-nhtsa-dumps/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}}, "aws_iam_role": {"NhtsaDB_staging-nhtsa-database_Role_E8C41EC3": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/staging-nhtsa-database/Role", "uniqueId": "NhtsaDB_staging-nhtsa-database_Role_E8C41EC3"}}, "assume_role_policy": "${data.aws_iam_policy_document.rds_assume_role_policy.json}", "name": "staging-nhtsa-database"}, "backup_role": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/backup_role", "uniqueId": "backup_role"}}, "assume_role_policy": "${data.aws_iam_policy_document.backup_policy.json}", "name": "rds-cont-backup-role"}, "migration_lambda_role": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/migration_lambda_role", "uniqueId": "migration_lambda_role"}}, "assume_role_policy": "${data.aws_iam_policy_document.migration_lambda_assume_role_policy.json}", "name": "db-migrate-lambda"}}, "aws_iam_role_policy_attachment": {"NhtsaDB_PolicyAttachment_87D866C2": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/PolicyAttachment", "uniqueId": "NhtsaDB_PolicyAttachment_87D866C2"}}, "policy_arn": "${aws_iam_policy.NhtsaDB_Policy_CF40B012.arn}", "role": "${aws_iam_role.NhtsaDB_staging-nhtsa-database_Role_E8C41EC3.name}"}, "backup_role_policy_attachment": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/backup_role_policy_attachment", "uniqueId": "backup_role_policy_attachment"}}, "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup", "role": "${aws_iam_role.backup_role.name}"}, "migration_lambda_vpc_access_plus": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/migration_lambda_vpc_access_plus", "uniqueId": "migration_lambda_vpc_access_plus"}}, "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", "role": "${aws_iam_role.migration_lambda_role.name}"}}, "aws_lambda_function": {"migration_lambda": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/migration_lambda", "uniqueId": "migration_lambda"}}, "description": "Lambda for managing migrations & postups", "environment": {"variables": {"DATABASES_FMCSAREADONLY_HOST": "fmcsa-aurora-db-cluster.cluster-ro-csx3hx8rg1kh.us-east-2.rds.amazonaws.com", "DATABASES_FMCSAREADONLY_NAME": "postgres", "DATABASES_FMCSAREADONLY_PASSWORD": "arn:aws:secretsmanager:us-east-2:667656038718:secret:fmcsa-db-pass-default-YxYdCO", "DATABASES_FMCSAREADONLY_PORT": "5432", "DATABASES_FMCSAREADONLY_USERNAME": "postgres", "DATABASES_FMCSAWRITE_HOST": "fmcsa-aurora-db-cluster.cluster-csx3hx8rg1kh.us-east-2.rds.amazonaws.com", "DATABASES_FMCSAWRITE_NAME": "postgres", "DATABASES_FMCSAWRITE_PASSWORD": "arn:aws:secretsmanager:us-east-2:667656038718:secret:fmcsa-db-pass-default-YxYdCO", "DATABASES_FMCSAWRITE_PORT": "5432", "DATABASES_FMCSAWRITE_USERNAME": "postgres", "DATABASES_FMCSA_HOST": "fmcsa-aurora-db-cluster.cluster-csx3hx8rg1kh.us-east-2.rds.amazonaws.com", "DATABASES_FMCSA_NAME": "postgres", "DATABASES_FMCSA_PASSWORD": "arn:aws:secretsmanager:us-east-2:667656038718:secret:fmcsa-db-pass-default-YxYdCO", "DATABASES_FMCSA_PORT": "5432", "DATABASES_FMCSA_USERNAME": "postgres", "DATABASES_NIRVANA_HOST": "${aws_db_instance.ApplicationDB_49B5873C.address}", "DATABASES_NIRVANA_NAME": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "DATABASES_NIRVANA_PASSWORD": "${aws_db_instance.ApplicationDB_49B5873C.password}", "DATABASES_NIRVANA_PORT": "${aws_db_instance.ApplicationDB_49B5873C.port}", "DATABASES_NIRVANA_USERNAME": "${aws_db_instance.ApplicationDB_49B5873C.username}", "ENV": "prod"}}, "function_name": "db-migrate-lambda", "image_uri": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryDbMigrateLambdarepository_url}:a5df0d9b", "lifecycle": {"ignore_changes": ["image_uri"]}, "memory_size": 512, "package_type": "Image", "reserved_concurrent_executions": 1, "role": "${aws_iam_role.migration_lambda_role.arn}", "timeout": 900, "vpc_config": {"security_group_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "subnet_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"]}}}, "aws_secretsmanager_secret": {"ApplicationDB_Secret_2875640C": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Secret", "uniqueId": "ApplicationDB_Secret_2875640C"}}, "name": "staging-application-database"}, "ApplicationDB_Secret_app-db-ds-user-password_A8D8F226": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-ds-user-password", "uniqueId": "ApplicationDB_Secret_app-db-ds-user-password_A8D8F226"}}, "name": "app-db-ds-user-password"}, "ApplicationDB_Secret_app-db-migrator-password_3AAB7A7F": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-migrator-password", "uniqueId": "ApplicationDB_Secret_app-db-migrator-password_3AAB7A7F"}}, "name": "app-db-migrator-password"}, "ApplicationDB_Secret_app-db-readonly-password_761953E6": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-readonly-password", "uniqueId": "ApplicationDB_Secret_app-db-readonly-password_761953E6"}}, "name": "app-db-readonly-password"}, "ApplicationDB_Secret_app-db-readwrite-password_AFF5A8C5": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-readwrite-password", "uniqueId": "ApplicationDB_Secret_app-db-readwrite-password_AFF5A8C5"}}, "name": "app-db-readwrite-password"}, "NhtsaDB_Secret_D38DDBBB": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/Secret", "uniqueId": "NhtsaDB_Secret_D38DDBBB"}}, "name": "staging-nhtsa-database"}}, "aws_secretsmanager_secret_version": {"ApplicationDB_SecretValue_7841D7E4": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/SecretValue", "uniqueId": "ApplicationDB_SecretValue_7841D7E4"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_2875640C.id}", "secret_string": "${random_password.ApplicationDB_Password_D15CF219.result}"}, "ApplicationDB_SecretValue_app-db-ds-user-password_4F3AA105": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-ds-user-password", "uniqueId": "ApplicationDB_SecretValue_app-db-ds-user-password_4F3AA105"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-ds-user-password_A8D8F226.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-ds-user-password_1AC329F6.result}"}, "ApplicationDB_SecretValue_app-db-migrator-password_3807CF8B": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-migrator-password", "uniqueId": "ApplicationDB_SecretValue_app-db-migrator-password_3807CF8B"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-migrator-password_3AAB7A7F.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-migrator-password_66F9C8A7.result}"}, "ApplicationDB_SecretValue_app-db-readonly-password_D8C00E55": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-readonly-password", "uniqueId": "ApplicationDB_SecretValue_app-db-readonly-password_D8C00E55"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-readonly-password_761953E6.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-readonly-password_128EBAB7.result}"}, "ApplicationDB_SecretValue_app-db-readwrite-password_47B2BD30": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-readwrite-password", "uniqueId": "ApplicationDB_SecretValue_app-db-readwrite-password_47B2BD30"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-readwrite-password_AFF5A8C5.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-readwrite-password_30F635AA.result}"}, "NhtsaDB_SecretValue_EBD7DE9E": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/SecretValue", "uniqueId": "NhtsaDB_SecretValue_EBD7DE9E"}}, "secret_id": "${aws_secretsmanager_secret.NhtsaDB_Secret_D38DDBBB.id}", "secret_string": "${random_password.NhtsaDB_Password_BB580154.result}"}}, "postgresql_grant": {"ApplicationDB_migrator_role_grant_create_on_db_47E3D13F": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_create_on_db", "uniqueId": "ApplicationDB_migrator_role_grant_create_on_db_47E3D13F"}}, "database": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "object_type": "database", "privileges": ["CREATE"], "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}"}, "ApplicationDB_migrator_role_grant_create_on_public_F3C04CBD": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_create_on_public", "uniqueId": "ApplicationDB_migrator_role_grant_create_on_public_F3C04CBD"}}, "database": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "object_type": "schema", "privileges": ["CREATE"], "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}", "schema": "public"}}, "postgresql_grant_role": {"ApplicationDB_migrator_role_grant_read_55714E9C": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_read", "uniqueId": "ApplicationDB_migrator_role_grant_read_55714E9C"}}, "grant_role": "pg_read_all_data", "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}", "with_admin_option": true}, "ApplicationDB_migrator_role_grant_write_72CBFEDC": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_write", "uniqueId": "ApplicationDB_migrator_role_grant_write_72CBFEDC"}}, "grant_role": "pg_write_all_data", "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}", "with_admin_option": true}}, "postgresql_role": {"ApplicationDB_migrator_role_CF52D44D": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/migrator_role", "uniqueId": "ApplicationDB_migrator_role_CF52D44D"}}, "create_database": true, "create_role": true, "lifecycle": {"ignore_changes": ["roles"]}, "login": true, "name": "migrator", "password": "${aws_secretsmanager_secret_version.ApplicationDB_SecretValue_app-db-migrator-password_3807CF8B.secret_string}", "provider": "postgresql"}}, "random_password": {"ApplicationDB_Password_D15CF219": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Password", "uniqueId": "ApplicationDB_Password_D15CF219"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-ds-user-password_1AC329F6": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-ds-user-password", "uniqueId": "ApplicationDB_Password_app-db-ds-user-password_1AC329F6"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-migrator-password_66F9C8A7": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-migrator-password", "uniqueId": "ApplicationDB_Password_app-db-migrator-password_66F9C8A7"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-readonly-password_128EBAB7": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-readonly-password", "uniqueId": "ApplicationDB_Password_app-db-readonly-password_128EBAB7"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-readwrite-password_30F635AA": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-readwrite-password", "uniqueId": "ApplicationDB_Password_app-db-readwrite-password_30F635AA"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "NhtsaDB_Password_BB580154": {"//": {"metadata": {"path": "default-staging-storage-rds-stack-us-east-2/NhtsaDB/Password", "uniqueId": "NhtsaDB_Password_BB580154"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_lower": 2, "min_numeric": 2, "min_special": 2, "min_upper": 2, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}}}, "terraform": {"backend": {"s3": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-storage-rds-stack-us-east-2.json", "region": "us-east-2"}}, "required_providers": {"aws": {"source": "aws", "version": "5.88.0"}, "postgresql": {"source": "cyrilgdn/postgresql", "version": "1.25.0"}, "random": {"source": "hashicorp/random", "version": "3.6.0"}}, "required_version": "1.7.5"}}