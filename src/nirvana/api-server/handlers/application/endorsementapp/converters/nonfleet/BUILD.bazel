load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "nonfleet",
    srcs = ["convert.go"],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/application/endorsementapp/converters/nonfleet",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/application/endorsementapp/converters",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/type_utils",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/model",
        "//nirvana/openapi-specs/components/insurance-bundle",
        "//nirvana/openapi-specs/components/nirvana",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_oapi_codegen_runtime//types",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "nonfleet_test",
    srcs = ["convert_test.go"],
    embed = [":nonfleet"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/model",
        "//nirvana/openapi-specs/components/insurance-bundle",
        "//nirvana/openapi-specs/components/nirvana",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
