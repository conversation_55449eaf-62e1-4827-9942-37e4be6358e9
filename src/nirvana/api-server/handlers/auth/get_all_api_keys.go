package auth

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/auth/transformer"
	"nirvanatech.com/nirvana/api-server/interceptors/auth/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth/enums"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func HandleGetAllAPIKeysAuthz(ctx context.Context) error {
	// Check if user is superuser
	if !authz.UserFromContext(ctx).IsSuperuser() {
		return common.NewNirvanaUnauthorizedErrorf(
			errors.New("only superusers can get API keys"),
			"Create API Key", "corresponding to Non Superuser Role")
	}
	return nil
}

func HandleGetAllAPIKeys(
	echoCtx echo.Context,
	deps deps.Deps,
) error {
	ctx := echoCtx.Request().Context()
	log.Info(ctx, "Fetching all api keys")

	var response []oapi_common.APIKeysWithAgency

	apiKeys, err := deps.AuthWrapper.FetchAllAPIKey(ctx)
	if err != nil {
		log.Error(ctx, "Error encountered while fetching api keys", log.Err(err))
		return common.NewNirvanaInternalServerWithReason(err, "Failed to fetch api keys")
	}

	for _, apiKey := range apiKeys {
		var (
			scope    oapi_common.APIKeyScope
			agencyId *uuid.UUID
		)

		err = json.Unmarshal(apiKey.Scope, &scope)
		if err != nil {
			log.Error(ctx, "Error encountered while unmarshalling scope", log.Err(err))
			return common.NewNirvanaInternalServerWithReason(err, "Failed to unmarshal scope")
		}

		status := apiKey.Status
		if apiKey.ExpiresAt.Before(time.Now()) {
			status = enums.KeyStatusExpired.String()
		}
		oapiStatus, err := transformer.MapStatusToOAPIStatus(status)
		if err != nil {
			log.Error(ctx, "Error encountered while transforming key status", log.Err(err))
			return common.NewNirvanaInternalServerWithReason(err, "Failed to convert key status")
		}

		agencyRoles, err := deps.AuthWrapper.FetchAgencyRoles(ctx, apiKey.UserID)
		if err != nil {
			log.Error(ctx, "Error encountered while fetching agency roles", log.Err(err))
			return common.NewNirvanaInternalServerWithReason(err, "Failed to fetch agency roles")
		}
		for _, agencyRole := range agencyRoles {
			if agencyRole.Group == authz.NirvanaAPIUserRole {
				if agencyRole.AgencyID != nil {
					agencyId = agencyRole.AgencyID
					break
				}
			}
		}
		if agencyId == nil {
			log.Error(ctx, "Error encountered while fetching agency ID", log.Err(err))
			return common.NewNirvanaInternalServerWithReason(err, "No Agency Id associated with the user in NirvanaAPIUserRole")
		}
		agency, err := deps.AgencyWrapper.FetchAgency(ctx, *agencyId)
		if err != nil {
			log.Error(ctx, "Error encountered while fetching agency", log.Err(err))
			return common.NewNirvanaInternalServerWithReason(err, "Failed to fetch agency")
		}

		response = append(response, oapi_common.APIKeysWithAgency{
			AgencyName: agency.Name,
			CreatedAt:  apiKey.CreatedAt,
			CreatedBy:  apiKey.CreatedBy,
			ExpiresAt:  apiKey.ExpiresAt,
			KeyId:      apiKey.ID,
			Scope:      scope,
			Status:     *oapiStatus,
			UpdatedAt:  apiKey.UpdatedAt,
			UserId:     apiKey.UserID,
		})
	}

	return echoCtx.JSON(
		http.StatusOK,
		response)
}
