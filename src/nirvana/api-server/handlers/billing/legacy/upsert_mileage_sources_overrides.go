package legacy

import (
	"context"
	"fmt"
	"net/http"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"

	billing_interceptor "nirvanatech.com/nirvana/api-server/interceptors/billing/deps"
	"nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/billing/legacy/mileagesrc"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/billing/mileage_source_override"
	oapi_billing "nirvanatech.com/nirvana/openapi-specs/components/billing"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/telematics"
)

func HandleUpsertMileageSourcesOverrides(
	ctx context.Context,
	deps billing_interceptor.Deps,
	req oapi_billing.UpsertMileageSourcesOverridesRequest,
) UpsertMileageSourcesOverridesResponse {
	handleBadRequest := func(err error, msg string) UpsertMileageSourcesOverridesResponse {
		log.Error(ctx, msg, log.Any("req", req), log.Err(err))
		return UpsertMileageSourcesOverridesResponse{
			BadRequestError: &oapi_common.ErrorMessage{Message: fmt.Sprintf("%s: %s", msg, err.Error())},
		}
	}
	handleServerError := func(err error, msg string) UpsertMileageSourcesOverridesResponse {
		log.Error(ctx, msg, log.Any("req", req), log.Err(err))
		return UpsertMileageSourcesOverridesResponse{
			ServerError: &oapi_common.ErrorMessage{Message: "something went wrong"},
		}
	}

	if len(req.MileageSourcesOverrides) == 0 {
		return UpsertMileageSourcesOverridesResponse{
			BadRequestError: &oapi_common.ErrorMessage{Message: "No mileage sources overrides are present in the request"},
		}
	}

	var msos []mileage_source_override.MileageSourceOverride

	for _, inputMSO := range req.MileageSourcesOverrides {
		id := uuid.New()
		if inputMSO.Id != nil {
			parsedID, err := uuid.Parse(inputMSO.Id.String())
			if err != nil {
				return handleBadRequest(err, fmt.Sprintf("Invalid ID %s", inputMSO.Id))
			}
			id = parsedID
		}

		if inputMSO.InitialStatus != nil && inputMSO.Id != nil {
			return handleBadRequest(
				errors.New("initial status can only be set on creation, not update"),
				"Can't update initial status",
			)
		}

		tsp, connectionHandleId, err := composeMSOConnectionData(inputMSO.Source, inputMSO.Type, inputMSO.TSP, inputMSO.ConnectionHandleId)
		if err != nil {
			return handleBadRequest(err, "Invalid connection data")
		}

		source, sourceType, err := composeMSOSourceAndType(inputMSO.Source, inputMSO.Type, tsp)
		if err != nil {
			return handleBadRequest(err, "Invalid mileage source & type combination")
		}

		var vehicleTags []string
		if inputMSO.VehicleTags != nil {
			vehicleTags = *inputMSO.VehicleTags
		}

		var excludedVINs []string
		if inputMSO.ExcludedVINs != nil {
			excludedVINs = *inputMSO.ExcludedVINs
		}

		msos = append(msos, mileage_source_override.MileageSourceOverride{
			Id:                 id,
			PolicyIdentifier:   inputMSO.PolicyIdentifier,
			PolicyIssuanceYear: inputMSO.PolicyIssuanceYear,
			Source:             *source,
			Type:               sourceType,
			TSP:                tsp,
			ConnectionHandleId: connectionHandleId,
			VehicleTags:        vehicleTags,
			ExcludedVINs:       excludedVINs,
			Status:             mapOAPIStatusToMSO(inputMSO.InitialStatus),
		})
	}

	err := deps.MileageSourcesClient.AssertNoEquivalentMileageSourcesOverrides(ctx, msos)
	if err != nil && errors.Is(err, mileagesrc.ErrDuplicateMSO) {
		return handleBadRequest(err, "Duplicate mileage sources overrides found")
	}
	if err != nil {
		return handleServerError(err, "Unexpected error validating duplicate mileage sources overrides")
	}

	err = deps.MileageSourcesClient.UpsertImmutableMileageSourcesOverrides(ctx, msos)
	if err != nil {
		return handleServerError(err, "Unexpected error upserting mileage sources overrides")
	}

	ids := slice_utils.Map(msos, func(mso mileage_source_override.MileageSourceOverride) openapi_types.UUID {
		return mso.Id
	})
	return UpsertMileageSourcesOverridesResponse{
		Response: &oapi_billing.UpsertMileageSourcesOverridesResponse{
			Ids: ids,
		},
	}
}

func mapOAPIStatusToMSO(initialStatus *oapi_billing.MileageSourceOverrideStatus) enums.MileageSourceOverrideStatus {
	if initialStatus == nil {
		// we default to active MSOs
		return enums.MileageSourceOverrideStatusActive
	}
	switch *initialStatus {
	case oapi_billing.MileageSourceOverrideStatusActive:
		return enums.MileageSourceOverrideStatusActive
	case oapi_billing.MileageSourceOverrideStatusDisabled:
		return enums.MileageSourceOverrideStatusDisabled
	case oapi_billing.MileageSourceOverrideStatusDeleted:
		return enums.MileageSourceOverrideStatusDeleted
	}
	return enums.MileageSourceOverrideStatusActive
}

func composeMSOConnectionData(
	oapiSource oapi_billing.MileageSourceOverrideInputSource,
	oapiType *oapi_billing.MileageSourceOverrideInputType,
	oapiTSP *string,
	oapiConnHandleID *openapi_types.UUID,
) (*telematics.TSP, *uuid.UUID, error) {
	isRequired := oapiSource == oapi_billing.MileageSourceOverrideInputSourceTSP
	if isRequired && oapiTSP == nil {
		return nil, nil, errors.New("tsp is required")
	}
	if isRequired && oapiConnHandleID == nil {
		return nil, nil, errors.New("connectionHandleId is required")
	}

	var tsp *telematics.TSP
	var connectionHandleId *uuid.UUID

	if oapiTSP == nil && oapiConnHandleID != nil {
		return nil, nil, errors.New("tsp is required when a connection handle ID is present")
	}
	if oapiTSP != nil && oapiConnHandleID == nil {
		return nil, nil, errors.New("connection handle ID is required when TSP is present")
	}
	if oapiTSP == nil && oapiConnHandleID == nil {
		return tsp, connectionHandleId, nil
	}

	cannotHaveConnectionData := oapiType != nil &&
		(*oapiType == oapi_billing.MileageSourceOverrideInputTypeELDExempted ||
			*oapiType == oapi_billing.MileageSourceOverrideInputTypeOwnerOperators ||
			*oapiType == oapi_billing.MileageSourceOverrideInputTypeMinimumMileageGuarantee)
	if cannotHaveConnectionData {
		return nil, nil, errors.Newf("connection data cannot be present for type %s", oapiType)
	}

	tspValue, err := telematics.TSPString(*oapiTSP)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to parse TSP")
	}
	connHandleId, err := uuid.Parse(oapiConnHandleID.String())
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to parse connection handle ID")
	}
	return &tspValue, &connHandleId, nil
}

func composeMSOSourceAndType(
	oapiSource oapi_billing.MileageSourceOverrideInputSource,
	oapiSourceType *oapi_billing.MileageSourceOverrideInputType,
	tsp *telematics.TSP,
) (*enums.MileageSource, *enums.MileageSourceType, error) {
	isSupportedIFTA := tsp != nil && mileagesrc.DoesTSPSupportIFTA(*tsp)

	var source enums.MileageSource
	switch oapiSource {
	case oapi_billing.MileageSourceOverrideInputSourceTSP:
		source = enums.MileageSourceTSP
	case oapi_billing.MileageSourceOverrideInputSourceSelfReporter:
		source = enums.MileageSourceSelfReporter
	default:
		return nil, nil, errors.Newf("unsupported mileage source %s", oapiSource)
	}

	if oapiSourceType == nil {
		return pointer_utils.ToPointer(enums.MileageSourceTSP), nil, nil
	}

	var sourceType enums.MileageSourceType
	switch *oapiSourceType {
	case oapi_billing.MileageSourceOverrideInputTypeIFTADisabled:
		if !isSupportedIFTA {
			return nil, nil, errors.Newf("cannot assign IFTA Disabled for TSP %s", tsp)
		}
		sourceType = enums.MileageSourceTypeIFTADisabled
	case oapi_billing.MileageSourceOverrideInputTypeELDExempted:
		sourceType = enums.MileageSourceTypeELDExempted
	case oapi_billing.MileageSourceOverrideInputTypeNonCompliantTSP:
		sourceType = enums.MileageSourceTypeNonCompliantTSP
	case oapi_billing.MileageSourceOverrideInputTypeOwnerOperators:
		sourceType = enums.MileageSourceTypeOwnerOperators
	case oapi_billing.MileageSourceOverrideInputTypeMinimumMileageGuarantee:
		sourceType = enums.MileageSourceTypeMinimumMileageGuarantee
	default:
		return nil, nil, errors.Newf("invalid source type %s", *oapiSourceType)
	}

	return &source, &sourceType, nil
}

type UpsertMileageSourcesOverridesResponse struct {
	Response        *oapi_billing.UpsertMileageSourcesOverridesResponse
	BadRequestError *oapi_common.ErrorMessage
	ServerError     *oapi_common.ErrorMessage
}

func (c *UpsertMileageSourcesOverridesResponse) StatusCode() int {
	switch {
	case c.Response != nil:
		return http.StatusOK
	case c.BadRequestError != nil:
		return http.StatusBadRequest
	case c.ServerError != nil:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

func (c *UpsertMileageSourcesOverridesResponse) Body() interface{} {
	switch {
	case c.Response != nil:
		return *c.Response
	case c.BadRequestError != nil:
		return *c.BadRequestError
	case c.ServerError != nil:
		return *c.ServerError
	default:
		return nil
	}
}
