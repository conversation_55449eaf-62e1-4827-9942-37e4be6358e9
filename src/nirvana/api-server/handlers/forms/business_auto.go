package forms

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	forms_interceptor "nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

func getBusinessAutoCoverages(ctx context.Context, deps forms_interceptor.Deps, appId uuid.UUID) ([]app_enums.Coverage, error) {
	app, err := deps.BusinessAutoAppWrapper.GetByID(ctx, appId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get application id %s", appId)
	}

	primaryCoveragesInApp := app.CoveragesInfo.GetPrimaryCoverageTypes()

	return primaryCoveragesInApp, nil
}
