package forms

import (
	"context"
	"net/http"

	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	"nirvanatech.com/nirvana/policy_common/constants"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
)

func GetFormCompilationHistoryAuthz(
	ctx context.Context,
	deps deps.Deps,
	req GetFormCompilationHistoryRequest,
) common.HandlerAuthzResponse {
	return hasPermissionOverApp(ctx, deps, req.ProgramType, req.ApplicationID, authz.ReadAction)
}

func HandleGetFormCompilationHistory(
	ctx context.Context,
	deps deps.Deps,
	req GetFormCompilationHistoryRequest,
) GetApplicationFormCompilationHistoryResponse {
	// Decorate the context with application id
	ctx = log.ContextWithFields(ctx, ApplicationId(req.ApplicationID.String()))
	formsDb, err := deps.FormsGenerator.GetFormsCompilationHistory(ctx, req.ApplicationID)
	if err != nil {
		log.Error(ctx, "HandleGetFormCompilationHistory: unable to generate form compilation", log.Err(err))
		errMessage := helpers.WrapErrorMessage(err, "unable to generate form compilation")
		return GetApplicationFormCompilationHistoryResponse{
			Error: &errMessage,
		}
	}
	formCompilations := make([]oapi_forms.FormCompilation, 0)
	if formsDb != nil {
		for _, f := range *formsDb {
			formsComp, err := BindFormCompToRest(f)
			if err != nil {
				log.Error(ctx, "HandleGetFormCompilationHistory: unable to bind form compilation from db", log.Err(err))
				errMessage := helpers.WrapErrorMessage(err, "unable to bind form compilation from db")
				return GetApplicationFormCompilationHistoryResponse{
					Error: &errMessage,
				}
			}
			formCompilations = append(formCompilations, *formsComp)
		}
	}
	log.Info(ctx, "HandleGetFormCompilationHistory: successfully generated form compilation")
	return GetApplicationFormCompilationHistoryResponse{
		Success: &formCompilations,
	}
}

type GetFormCompilationHistoryRequest struct {
	ApplicationID   uuid.UUID
	ApplicationType constants.ApplicationType
	ProgramType     policy_enums.ProgramType
}

type GetApplicationFormCompilationHistoryResponse struct {
	Success     *[]oapi_forms.FormCompilation
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (u *GetApplicationFormCompilationHistoryResponse) StatusCode() int {
	switch {
	case u.Success != nil:
		return http.StatusOK
	case u.Error != nil:
		return http.StatusUnprocessableEntity
	case u.ServerError != nil:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

func (u *GetApplicationFormCompilationHistoryResponse) Body() interface{} {
	switch {
	case u.Success != nil:
		return *u.Success
	case u.Error != nil:
		return *u.Error
	case u.ServerError != nil:
		return *u.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = (*GetApplicationFormCompilationHistoryResponse)(nil)
