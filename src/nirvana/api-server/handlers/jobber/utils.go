package jobber

import (
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapi_jobber "nirvanatech.com/nirvana/openapi-specs/components/jobber"
)

func JobRunToOApi(jobRun *jtypes.JobRun) oapi_jobber.JobRun {
	var schedule *string
	if jobRun.State.Schedule != nil && jobRun.State.Schedule.Config.Type == jtypes.Cron {
		schedule = &jobRun.State.Schedule.Config.CronSpec
	}
	coalescedStartTime := jobRun.PickupTime
	if jobRun.StartTime.Valid {
		coalescedStartTime = jobRun.StartTime.Time
	}
	return oapi_jobber.JobRun{
		CloudWatchLink: getCWLink(jobRun),
		Error:          pointer_utils.String(jobRun.Error),
		FinishedAt:     jobRun.FinishTime.Ptr(),
		JobRunId:       jobRun.JobRunId.String(),
		Owner:          jobRun.Owner.Ptr(),
		StartedAt:      coalescedStartTime,
		Status:         jobRun.Status.String(),
		Schedule:       schedule,
	}
}

func getCWLink(_ *jtypes.JobRun) string {
	// TODO: Complete Me.
	// This is excessively difficult because AWS Console uses some weird techniques to URL encode their query params.
	// The links below describe the algorithm that we would have to replicate in golang.
	// Cost-benefit analysis of doing this is not favourable right now.
	// 1. Official response from a AWS Support Engineer:
	// https://repost.aws/questions/QUkdGEQP7rQZmDBUaB2Ai2Qg/aws-cloud-watch-log-insights-generate-url
	// 2. Unofficial StackOverflow answer from some very determined folks who reverse-engineered the encoding
	// https://stackoverflow.com/a/68638660
	return "https://us-east-2.console.aws.amazon.com/cloudwatch/home?region=us-east-2#logsV2:logs-insights$3FqueryDetail$3D$257E$2528end$257E0$257Estart$257E-43200$257EtimeType$257E$2527RELATIVE$257Eunit$257E$2527seconds$257EeditorString$257E$2527fields*20*40timestamp*2c*20*40message*0a*7c*20filter*20jobRunId*20*3d*20*22MyJobRunId*3a*3a*3a*3a1*22*0a*7c*20sort*20*40timestamp*20desc$257EisLiveTail$257Efalse$257EqueryId$257E$25276a264e3b-8fcd-4dbb-8813-4e88258b92aa$257Esource$257E$2528$257E$2527default-data-infra-job-processor-logs2022091306114921920000000b$2529$2529"
}
