package utils

import (
	"context"

	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_application/deps"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz/checker"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/constants"
)

func HandleUnauthzAppsFiltering[T nf_app.AppInfo](
	ctx context.Context,
	deps deps.Deps,
	appObjs []nf_app.Application[T],
) ([]nf_app.Application[T], error) {
	user := authz.UserFromContext(ctx)
	isQA := false
	for _, role := range user.Roles {
		if (role.AgencyID != nil && *role.AgencyID == constants.KiwiQAAgencyID) || role.Group == authz.QAUnderwriterRole {
			isQA = true
		} else {
			isQA = false
		}
	}
	if isQA {
		var filteredApps []nf_app.Application[T]
		for _, application := range appObjs {
			if application.AgencyID == constants.KiwiQAAgencyID {
				filteredApps = append(filteredApps, application)
			}
		}
		return filteredApps, nil
	}
	filteredApps, err := filterUnauthzApps(ctx, deps, appObjs)
	if err != nil {
		return filteredApps, err
	}
	return filteredApps, nil
}

func filterUnauthzApps[T nf_app.AppInfo](
	ctx context.Context,
	deps deps.Deps,
	apps []nf_app.Application[T],
) ([]nf_app.Application[T], error) {
	var filteredApps []nf_app.Application[T]
	user := authz.UserFromContext(ctx)
	isExternalDemoUser := user.HasRoleInAgency(constants.NirvanaDemoAgencyID)
	for _, a := range apps {
		hasPermission, err := deps.AuthzChecker.CheckPermission(
			authz.UserFromContext(ctx), &a, authz.ReadAction)
		switch {
		case err != nil:
			return nil, errors.Wrap(err, "unable to check permissions over app")
		case hasPermission:
			if isExternalDemoUser && a.AgencyID != constants.NirvanaDemoAgencyID {
				continue
			}
			filteredApps = append(filteredApps, a)
		}
	}
	return filteredApps, nil
}

func HasPermissionOverApp(
	ctx context.Context,
	user authz.User,
	action authz.Action,
	appId uuid.UUID,
	progTyp policy_enums.ProgramType,
	admittedAppWrapper nf_app.Wrapper[*admitted_app.AdmittedApp],
	authzChecker *checker.Checker,
) common.HandlerAuthzResponse {
	var hasPermission bool
	var pErr error
	if progTyp != policy_enums.ProgramTypeNonFleetAdmitted {
		return common.HandlerAuthzResponse{
			AuthzError: &helpers.GenericAuthzError,
		}
	}

	appObj, err := admittedAppWrapper.GetAppById(ctx, appId)
	if err != nil {
		log.Error(ctx, "unable to fetch app", log.String("app id", appId.String()), log.Err(err))
		return common.HandlerAuthzResponse{ServerError: err}
	}
	hasPermission, pErr = authzChecker.CheckPermission(user, appObj, action)

	switch {
	case pErr != nil:
		log.Error(ctx, "unable to check permission over app",
			log.String("app id", appId.String()), log.Err(pErr))
		return common.HandlerAuthzResponse{
			AuthzError: &helpers.GenericServerError,
		}
	case !hasPermission:
		log.Error(ctx, "not authorized", log.String("app id", appId.String()),
			log.Any("user", user), log.String("action", string(action)))
		return common.HandlerAuthzResponse{
			AuthzError: &helpers.GenericAuthzError,
		}
	default:
		return common.HandlerAuthzResponse{IsAuthorized: true}
	}
}

func HasPermissionOverAgencyApps(
	ctx context.Context,
	deps deps.Deps,
	user authz.User,
	action authz.Action,
	agencyId uuid.UUID,
) common.HandlerAuthzResponse {
	a, err := deps.AgencyWrapper.FetchAgency(ctx, agencyId)
	if err != nil {
		log.Error(ctx, "unable to fetch agency", log.String("agencyId", agencyId.String()), log.Err(err))
		return common.HandlerAuthzResponse{ServerError: err}
	}
	apps := agency.NewApplications(a.ID)
	hasPermission, err := deps.AuthzChecker.CheckPermission(
		user, apps, action)
	switch {
	case err != nil:
		log.Error(ctx, "unable to check permission over agency apps",
			log.String("agencyId", agencyId.String()), log.Err(err))
		return common.HandlerAuthzResponse{
			AuthzError: &helpers.GenericServerError,
		}
	case !hasPermission:
		log.Error(ctx, "not authorized", log.String("agencyId", agencyId.String()),
			log.Any("apps", apps), log.Any("user", user), log.String("action", string(action)))
		return common.HandlerAuthzResponse{
			AuthzError: &helpers.GenericAuthzError,
		}
	default:
		return common.HandlerAuthzResponse{IsAuthorized: true}
	}
}
