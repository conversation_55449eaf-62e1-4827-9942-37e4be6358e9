load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "nonfleet_underwriting",
    srcs = [
        "approve_application.go",
        "close_application.go",
        "decline_application.go",
        "get_actions.go",
        "get_app_review_documents.go",
        "get_app_review_drivers.go",
        "get_app_review_drivers_v2.go",
        "get_app_review_equipments.go",
        "get_app_review_flags.go",
        "get_app_review_list.go",
        "get_app_review_losses.go",
        "get_app_review_notes.go",
        "get_app_review_operations.go",
        "get_app_review_packages.go",
        "get_app_review_safety.go",
        "get_app_review_summary.go",
        "get_app_review_timeline.go",
        "get_app_reviews.go",
        "get_express_lane_feedback.go",
        "get_safety_score.go",
        "get_scrape_info.go",
        "get_violations_list.go",
        "oapi_types_mappers.go",
        "patch_express_lane_feedback.go",
        "post_app_review_documents.go",
        "post_scrape.go",
        "post_uw_quote_submit.go",
        "refer_application.go",
        "rollback_nf_app_review.go",
        "set_mvr_pull.go",
        "update_app_review_assignee.go",
        "update_app_review_driver.go",
        "update_app_review_driver_record.go",
        "update_app_review_drivers.go",
        "update_app_review_equipments.go",
        "update_app_review_losses.go",
        "update_app_review_note.go",
        "update_app_review_operations.go",
        "update_app_review_packages.go",
        "update_app_review_safety.go",
        "update_safety_score.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application",
        "//nirvana/api-server/handlers/nonfleet_underwriting/utils",
        "//nirvana/api-server/helpers",
        "//nirvana/api-server/interceptors/nonfleet_underwriting/deps",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/math_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/random_utils",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/rule_runs",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/events",
        "//nirvana/events/nonfleet_events",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/jobber/jtypes",
        "//nirvana/nonfleet/authorities/fact",
        "//nirvana/nonfleet/calculators/creditscore",
        "//nirvana/nonfleet/calculators/usdotscore",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/quoting-jobs",
        "//nirvana/nonfleet/rating",
        "//nirvana/nonfleet/rating-jobs",
        "//nirvana/nonfleet/rule_engine/registry",
        "//nirvana/nonfleet/state-machine",
        "//nirvana/nonfleet/underwriting_panels",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/nonfleet/underwriting_panels/summary",
        "//nirvana/openapi-specs/api_server_uw/nonfleet_uw",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/rating/mvr",
        "//nirvana/servers/quote_scraper",
        "//nirvana/servers/quote_scraper/enums",
        "//nirvana/underwriting/app_review/actions/permission",
        "//nirvana/underwriting/common-utils",
        "//nirvana/underwriting/jobs",
        "//nirvana/underwriting/platform/safety_score",
        "@com_github_brianvoe_gofakeit_v6//:gofakeit",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_labstack_echo_v4//:echo",
        "@com_github_oapi_codegen_runtime//types",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "nonfleet_underwriting_test",
    srcs = ["get_actions_test.go"],
    embed = [":nonfleet_underwriting"],
    deps = [
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/underwriting/app_review/actions/permission",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
