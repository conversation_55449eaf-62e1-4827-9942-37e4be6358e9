package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels"

	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"

	"nirvanatech.com/nirvana/api-server/common"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewLossesAuthz(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewLossesRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewLosses(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewLossesRequest,
) GetApplicationReviewLossesResponse {
	log.Info(ctx, "HandleGetApplicationReviewLosses", log.Any("req", request))
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return GetApplicationReviewLossesResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}

	var losses *oapi_uw.ApplicationReviewLosses
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		losses, gErr = getLosses(ctx, deps.AdmittedBasePanel, deps.AdmittedLossesPanel, request.AppReviewID)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return GetApplicationReviewLossesResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get losses, invalid program type %v", programType),
			},
		}
	}

	if gErr != nil {
		return GetApplicationReviewLossesResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get losses %v", gErr)},
		}
	}

	return GetApplicationReviewLossesResponse{
		Success: losses,
	}
}

func getLosses[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	lossPanel *underwriting_panels.LossesPanel[T],
	reviewID string,
) (*oapi_uw.ApplicationReviewLosses, error) {
	input, err := basePanel.GetPanelInput(ctx, reviewID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}

	return lossPanel.GetLossInfo(input), nil
}

type GetApplicationReviewLossesRequest struct {
	AppReviewID string
}

type GetApplicationReviewLossesResponse struct {
	Success     *oapi_uw.ApplicationReviewLosses
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewLossesResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewLossesResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewLossesResponse{}
