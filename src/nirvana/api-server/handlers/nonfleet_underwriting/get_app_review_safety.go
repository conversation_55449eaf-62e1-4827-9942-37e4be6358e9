package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels"

	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"

	"nirvanatech.com/nirvana/api-server/common"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewSafetyAuthz(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewSafetyRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewSafety(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewSafetyRequest,
) GetApplicationReviewSafetyResponse {
	log.Info(ctx, "HandleGetApplicationReviewSafety", log.Any("req", request))

	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return GetApplicationReviewSafetyResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}

	var safety *oapi_uw.ApplicationReviewSafety
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		safety, gErr = getSafetyDetails(ctx, deps.AdmittedBasePanel, deps.AdmittedSafetyPanel, request.AppReviewID)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return GetApplicationReviewSafetyResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get packages, invalid program type %v", programType)},
		}
	}

	if gErr != nil {
		return GetApplicationReviewSafetyResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get safety details %v", gErr)},
		}
	}

	return GetApplicationReviewSafetyResponse{
		Success: safety,
	}
}

func getSafetyDetails[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	safetyPanel *underwriting_panels.SafetyPanel[T],
	appReviewID string,
) (*oapi_uw.ApplicationReviewSafety, error) {
	input, err := basePanel.GetPanelInput(ctx, appReviewID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}

	crashHistory, crashSummary, err := safetyPanel.CrashRecords(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get crash history")
	}

	basicScores, err := safetyPanel.BasicScores(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get basic scores")
	}

	oosViolations, err := safetyPanel.OOSViolations(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get OOS violation")
	}

	dotRating, ratingDate, err := safetyPanel.DOTRating(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get DOT rating")
	}

	severeViolations, err := safetyPanel.SevereViolations(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get severe violations")
	}

	return &oapi_uw.ApplicationReviewSafety{
		CrashRecords:        crashHistory,
		CrashRecordsSummary: *crashSummary,
		OOSSummary:          oosViolations,
		BasicScores:         basicScores,
		EffectiveDate:       ratingDate,
		IsReviewed:          safetyPanel.IsReviewed(input),
		SafetyRating:        dotRating,
		SevereViolations:    severeViolations,
		SafetyCredit:        safetyPanel.GetSafetyCredit(input),
	}, nil
}

type GetApplicationReviewSafetyRequest struct {
	AppReviewID string
}

type GetApplicationReviewSafetyResponse struct {
	Success     *oapi_uw.ApplicationReviewSafety
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewSafetyResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewSafetyResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewSafetyResponse{}
