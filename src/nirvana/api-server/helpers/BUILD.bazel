load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "helpers",
    srcs = [
        "file.go",
        "open_api.go",
        "serde_utils.go",
        "uw.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/helpers",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_labstack_echo_v4//:echo",
    ],
)
