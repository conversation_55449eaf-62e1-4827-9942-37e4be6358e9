package deps

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"go.uber.org/fx"
	"gopkg.in/segmentio/analytics-go.v3"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/summary"
	actions "nirvanatech.com/nirvana/underwriting/app_review/actions/permission"

	"nirvanatech.com/nirvana/external_data_management/data_processing"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/rule_runs"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/infra/authz/checker"
	statemachine "nirvanatech.com/nirvana/nonfleet/state-machine"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels"
	"nirvanatech.com/nirvana/servers/quote_scraper"
)

type Deps struct {
	fx.In

	AdmittedDriversPanel       *driver.DriversPanel[*admitted_app.AdmittedApp]
	AdmittedBasePanel          *base_panel.BasePanel[*admitted_app.AdmittedApp]
	AdmittedPackagesPanel      *underwriting_panels.PackagesPanel[*admitted_app.AdmittedApp]
	AdmittedLossesPanel        *underwriting_panels.LossesPanel[*admitted_app.AdmittedApp]
	AdmittedSummaryPanel       *summary.SummaryPanel[*admitted_app.AdmittedApp]
	AdmittedSafetyPanel        *underwriting_panels.SafetyPanel[*admitted_app.AdmittedApp]
	AdmittedEquipmentPanel     *underwriting_panels.EquipmentsPanel[*admitted_app.AdmittedApp]
	AdmittedOperationsPanel    *operations.OperationsPanel[*admitted_app.AdmittedApp]
	NFApplicationReviewWrapper nf_app_review.Wrapper
	RuleRunsWrapper            rule_runs.Wrapper
	AdmittedAppWrapper         nf_app.Wrapper[*admitted_app.AdmittedApp]
	AuthzChecker               *checker.Checker
	NFAppStateMachineWrapper   *statemachine.NFAppStateMachineImpl
	AuthWrapper                auth.DataWrapper
	FileUploadManager          file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	Jobber                     quoting_jobber.Client
	QuoteScraperClient         quote_scraper.QuoteScraperClient
	MVRClient                  mvr.MVRClient
	FFClient                   feature_flag_lib.Client
	FetcherClientFactory       data_fetching.FetcherClientFactory
	ProcessorClientFactory     data_processing.ProcessorClientFactory
	SegmentClient              analytics.Client
	ActionPermissionManager    actions.ActionPermissionManager
	Clk                        clock.Clock
}
