package enums

//go:generate go run github.com/dmarkham/enumer -type=MileageSource -trimprefix=MileageSource
type MileageSource int

const (
	MileageSourceTSP MileageSource = iota + 1
	MileageSourceSelfReporter
)

//go:generate go run github.com/dmarkham/enumer -type=MileageSourceType -trimprefix=MileageSourceType
type MileageSourceType int

const (
	MileageSourceTypeIFTADisabled MileageSourceType = iota + 1
	MileageSourceTypeELDExempted
	MileageSourceTypeOwnerOperators
	MileageSourceTypeNonCompliantTSP
	MileageSourceTypeMinimumMileageGuarantee
)

//go:generate go run github.com/dmarkham/enumer -type=MileageSourceOverrideStatus -trimprefix=MileageSourceOverrideStatus
type MileageSourceOverrideStatus int

const (
	// MileageSourceOverrideStatusActive represents a mileage source that is actively being used.
	MileageSourceOverrideStatusActive MileageSourceOverrideStatus = iota
	// MileageSourceOverrideStatusDeleted represents a mileage source override that should no longer exist (i.e. soft
	// deleted). Whenever possible, prefer "Deleted" over "Disabled".
	MileageSourceOverrideStatusDeleted
	// MileageSourceOverrideStatusDisabled is an internal status used to convey that an existing mileage source that is
	// NOT an override (e.g. from an application), and thus cannot be soft-deleted, should at least be ignored as a
	// mileage source. Whenever possible, prefer "Deleted" over "Disabled". This status will go away once we are able to
	// read connections from connections manager service instead of from applications.
	MileageSourceOverrideStatusDisabled
)

//go:generate go run github.com/dmarkham/enumer -type=MileageSourceOverrideStatusReason -trimprefix=MileageSourceOverrideStatusReason
type MileageSourceOverrideStatusReason int

const (
	MileageSourceOverrideStatusReasonTSPSwitching MileageSourceOverrideStatusReason = iota + 1
	MileageSourceOverrideStatusReasonUnreliableData
	MileageSourceOverrideStatusReasonCancelled
	MileageSourceOverrideStatusReasonNoLongerInUse
	MileageSourceOverrideStatusReasonOther
)
