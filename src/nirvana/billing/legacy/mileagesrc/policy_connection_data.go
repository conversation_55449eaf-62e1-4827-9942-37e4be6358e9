package mileagesrc

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/telematics"
)

// policyConnectionData stores the connection data for a given policy identifier + issuance year combination. More
// importantly, it provides us with a consolidated struct we can use to merge into mileage sources overrides, regardless
// of what was the origin of this data.
type policyConnectionData struct {
	policyIdentifier string
	issuanceYear     int16
	tsp              *telematics.TSP
	connHandleId     *uuid.UUID
}

func (c MileageSourcesClient) newConnDataFromPolicy(
	ctx context.Context, p *policy.Policy,
) (*policyConnectionData, error) {
	pn := p.PolicyNumber
	cd := &policyConnectionData{
		policyIdentifier: pn.GetPolicyIdentifier(),
		issuanceYear:     int16(pn.GetPolicyIssuanceYear().Year()),
	}

	app, err := c.deps.AppWrapper.GetAppById(ctx, p.ApplicationId.String())
	if err != nil {
		return nil, errors.Wrapf(err, "unable to fetch application %s", p.ApplicationId)
	}

	tsp := app.TSPEnum
	cHId := app.TSPConnHandleId
	if tsp == nil || cHId == nil {
		return cd, nil
	}

	connectionHandleId, err := uuid.Parse(*cHId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse connection handle ID %s", cHId)
	}

	cd.tsp = tsp
	cd.connHandleId = &connectionHandleId
	return cd, nil
}

func (cd policyConnectionData) mergeIntoMileageSources(
	mss MileageSources,
	disabledConnHandleIDs []uuid.UUID,
) MileageSources {
	if cd.connHandleId == nil {
		return mss
	}

	// Ignore if explicitly disabled
	if slice_utils.Contains(disabledConnHandleIDs, *cd.connHandleId) {
		return mss
	}

	// Ignore if overridden
	for _, ms := range mss {
		if ms.ConnectionHandleId != nil && *ms.ConnectionHandleId == *cd.connHandleId {
			return mss
		}
	}

	// Append otherwise
	ms := cd.toMileageSource()
	if ms != nil {
		mss = append(mss, *ms)
	}
	return mss
}

func (cd policyConnectionData) toMileageSource() *MileageSourceDetails {
	if cd.tsp == nil || cd.connHandleId == nil {
		return nil
	}

	return &MileageSourceDetails{
		PolicyIdentifier:   cd.policyIdentifier,
		PolicyIssuanceYear: cd.issuanceYear,
		Source:             enums.MileageSourceTSP,
		TSP:                cd.tsp,
		ConnectionHandleId: cd.connHandleId,
	}
}
