package pricing

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// buildCompany converts app company info to proto
func buildCompany(app *model.BusinessAutoApp) (*ptypes.BusinessAuto_Company, error) {
	if app.CompanyInfo.USState == us_states.InvalidStateCode {
		return nil, errors.New("company state is required")
	}

	company := &ptypes.BusinessAuto_Company{
		Id:    app.ID.String(), // Use application ID as company identifier for now
		State: app.CompanyInfo.USState.ToCode(),
	}

	// Map optional fields
	if app.CompanyInfo.AnnualCostOfHire != nil {
		company.AnnualCostOfHire = pointer_utils.ToPointer(int64(*app.CompanyInfo.AnnualCostOfHire))
	}

	if app.CompanyInfo.NoOfEmployees != nil && app.CompanyInfo.PerOfEmployeesOperatingOwnAutos != nil {
		// Calculate number of employees operating personal vehicles
		numEmployees := float64(*app.CompanyInfo.NoOfEmployees)
		percentage := float64(*app.CompanyInfo.PerOfEmployeesOperatingOwnAutos)
		company.NumberOfEmployeesOperatingPersonalVehiclesForWork = int64(numEmployees * percentage / 100.0)
	}

	// Map filings info
	if app.FilingsInfo != nil {
		company.HasMultiStateFilings = app.FilingsInfo.HasMultiStateFilings
		company.HasFMCSAFilings = app.FilingsInfo.HasFMCSAFilings
		company.HasDOTFilings = app.FilingsInfo.HasDOTFilings
	}

	// Map individual named insured flag
	if app.CompanyInfo.HasIndividualNamedInsured != nil {
		company.IsIndividualNamedInsured = *app.CompanyInfo.HasIndividualNamedInsured
	}

	// Map max value of hired autos
	if app.CompanyInfo.MaximumValueOfHiredAutos != nil {
		company.MaxValueOfHiredAutos = int64(*app.CompanyInfo.MaximumValueOfHiredAutos)
	}

	return company, nil
}
