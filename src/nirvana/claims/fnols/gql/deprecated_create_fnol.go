package gql

import (
	"context"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/client"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	fnols_client "nirvanatech.com/nirvana/claims/fnols/client"
	"nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

type Reporter struct {
	FirstName *string `json:"firstName"`
	LastName  *string `json:"lastName"`
	Phone     *string `json:"phone"`
	Email     *string `json:"email"`
}

type Police struct {
	AgencyName   *string `json:"agencyName"`
	ReportNumber *string `json:"reportNumber"`
}

type ClaimVehicle struct {
	VIN                *string `graphql:"vin" json:"vin"`
	RegistrationNumber *string `json:"registrationNumber"`
}

type CreateFNOLArgs struct {
	PolicyNumber     string
	Description      *string
	NoticeType       enums.FnolNoticeType
	LossLocation     *string
	LossState        string
	LossDate         time.Time
	InjuriesInvolved *bool
	Police           *Police
	Reporter         Reporter
	InsuredVehicles  *[]ClaimVehicle
	OtherVehicles    *[]ClaimVehicle
	InsuredName      *string
	LineOfBusiness   *claim_enums.LineOfBusiness
	AttachmentKeys   *[]string
	DraftFnolId      *uuid.UUID
	Source           *enums.FnolSource
	Provider         *claim_enums.ClaimsProvider
}

// Deprecated: this method will soon be removed in favor of submitFnol.
func (r *Resolver) createFNOL(ctx context.Context, args CreateFNOLArgs) (responseFnol *Fnol, err error) {
	args.PolicyNumber = strings.ToUpper(strings.Trim(args.PolicyNumber, " "))

	ctx = log.ContextWithFields(
		ctx,
		log.String("operation", "createFNOL"),
		log.String("policyNumber", args.PolicyNumber),
		log.Stringer("user", authz.UserFromContext(ctx).ID),
	)

	defer func() {
		if err != nil {
			log.Error(ctx, "failed to create FNOL", log.Err(err))
		}
		r.deps.MetricsClient.IncOperation(ctx, metrics.OperationCreateFNOLRequest, err)
	}()

	policyAllowed, err := client.IsPolicyAllowed(args.PolicyNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to check if policy %s is allowed", args.PolicyNumber)
	}
	if !policyAllowed {
		return nil, errors.Newf("Cannot create FNOL for policy %s", args.PolicyNumber)
	}

	p, err := r.deps.PolicyClient.GetLatestPolicy(ctx, args.PolicyNumber, false)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch policy %s", args.PolicyNumber)
	}

	if err := r.deps.AuthzClient.CanCreateFnols(ctx, p.PolicyNumber.String()); err != nil {
		return nil, err
	}

	var attachmentKeys []string
	if args.AttachmentKeys != nil {
		attachmentKeys = *args.AttachmentKeys
	}

	authzUser := authz.UserFromContext(ctx)

	fnol, err := r.legacyNewFnolFromArgs(ctx, args, attachmentKeys, authzUser)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create new FNOL from args")
	}

	var lob claim_enums.LineOfBusiness
	if args.LineOfBusiness != nil {
		lob = *args.LineOfBusiness
	} else {
		lob, err = fnols_client.GetLineOfBusiness(*p.PolicyNumber)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to get line of business for policy %s", p.PolicyNumber,
			)
		}
	}
	narsArgs := fnols_client.NARSArgs{
		LineOfBusiness: lob,
		InsuredName:    args.InsuredName,
		Phone:          args.Reporter.Phone,
		Email:          args.Reporter.Email,
		FirstName:      args.Reporter.FirstName,
		LastName:       args.Reporter.LastName,
	}
	registerFNOLArgs := fnols_client.RegisterFNOLArgs{
		FNOL:            *fnol,
		AttachmentsKeys: attachmentKeys,
		NARSArgs:        narsArgs,
	}
	err = r.deps.FnolClient.RegisterFNOL(ctx, registerFNOLArgs)
	if err != nil {
		return nil, errors.Wrap(err, "failed to register new FNOL")
	}

	appIdsIsTestMapping, err := r.deps.ApplicationClient.GetAppIDsToIsTestMap(ctx, p.ApplicationId)
	if err != nil {
		return nil, err
	}

	createdByName := authzUser.FullName()
	responseFnol = fnolFromDB(*fnol).WithCreatedBy(createdByName).WithPolicy(*p, appIdsIsTestMapping[p.ApplicationId])

	return responseFnol, nil
}

// Deprecated: this method will soon be removed in favor of newFnolFromArgs.
func (r *Resolver) legacyNewFnolFromArgs(ctx context.Context, args CreateFNOLArgs, attachmentKeys []string, user authz.User) (*db.ClaimFnol, error) {
	description := "N/A"
	if args.Description != nil && *args.Description != "" {
		description = *args.Description
	}
	lossLocation := "N/A"
	if args.LossLocation != nil && *args.LossLocation != "" {
		lossLocation = *args.LossLocation
	}
	injuries := false
	if args.InjuriesInvolved != nil {
		injuries = *args.InjuriesInvolved
	}
	firstName := "N/A"
	if args.Reporter.FirstName != nil && *args.Reporter.FirstName != "" {
		firstName = *args.Reporter.FirstName
	}
	lastName := "N/A"
	if args.Reporter.LastName != nil && *args.Reporter.LastName != "" {
		lastName = *args.Reporter.LastName
	}
	phone := "N/A"
	if args.Reporter.Phone != nil && *args.Reporter.Phone != "" {
		phone = *args.Reporter.Phone
	}

	var policeAgencyName *string
	var policeReportNumber *string
	if args.Police != nil {
		policeAgencyName = args.Police.AgencyName
		policeReportNumber = args.Police.ReportNumber
	}

	ccn, err := r.deps.FnolClient.DeprecatedNewClientClaimNumber(ctx, args.PolicyNumber)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate client claim number")
	}

	fnol, err := db.
		NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithClientClaimNumber(pointer_utils.String(ccn)).
		WithPolicyNumber(pointer_utils.String(args.PolicyNumber)).
		WithIncidentDescription(pointer_utils.String(description)).
		WithNoticeType(&args.NoticeType).
		WithLossLocation(pointer_utils.String(lossLocation)).
		WithLossState(pointer_utils.String(args.LossState)).
		WithLossDatetime(pointer_utils.Time(args.LossDate)).
		WithInjuriesInvolved(pointer_utils.Bool(injuries)).
		WithPoliceAgencyName(policeAgencyName).
		WithPoliceReportNumber(policeReportNumber).
		WithCreatedBy(user.ID).
		WithSubmittedFrom(pointer_utils.FromPointerOr(args.Source, enums.FnolSourceUnknown)).
		Build()
	if err != nil {
		return nil, errors.Wrap(err, "failed to build fnol")
	}

	reporter, err := db.
		NewFnolContactBuilder().
		WithFnolId(fnol.Id).
		WithContactType(enums.FnolContactTypeReporter).
		WithFirstName(firstName).
		WithLastName(lastName).
		WithPhone(phone).
		WithEmail(args.Reporter.Email).
		Build()
	if err != nil {
		return nil, errors.Wrap(err, "failed to build FNOL contact")
	}
	fnol.Contacts = append(fnol.Contacts, *reporter)

	if args.InsuredVehicles != nil {
		for _, v := range *args.InsuredVehicles {
			insuredVehicle, err := db.NewFnolVehicleBuilder().
				WithFnolId(fnol.Id).
				WithIsInsuredVehicle(true).
				WithVIN(v.VIN).
				WithRegistrationNumber(v.RegistrationNumber).
				Build()
			if err != nil {
				return nil, errors.Wrap(err, "failed to build FNOL insured vehicle")
			}
			fnol.Vehicles = append(fnol.Vehicles, *insuredVehicle)
		}
	}

	if args.OtherVehicles != nil {
		for _, v := range *args.OtherVehicles {
			insuredVehicle, err := db.NewFnolVehicleBuilder().
				WithFnolId(fnol.Id).
				WithIsInsuredVehicle(false).
				WithVIN(v.VIN).
				WithRegistrationNumber(v.RegistrationNumber).
				Build()
			if err != nil {
				return nil, errors.Wrap(err, "failed to build FNOL other vehicle")
			}
			fnol.Vehicles = append(fnol.Vehicles, *insuredVehicle)
		}
	}

	accountId := authz.UserFromContext(ctx).ID
	for _, key := range attachmentKeys {
		fileHandleId, err := r.deps.FnolClient.ParseAttachmentKeyToHandleId(accountId, key)
		if err != nil {
			return nil, err
		}
		attachment, err := db.NewFnolAttachmentBuilder().
			WithFnolId(fnol.Id).
			WithHandleId(fileHandleId).
			Build()
		if err != nil {
			return nil, err
		}
		fnol.Attachments = append(fnol.Attachments, *attachment)
	}

	return fnol, nil
}
