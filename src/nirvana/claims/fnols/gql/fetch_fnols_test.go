package gql_test

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/google/uuid"

	claim_enums "nirvanatech.com/nirvana/claims/enums"
	fnols_db "nirvanatech.com/nirvana/claims/fnols/db"
	fnol_enums "nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/claims/fnols/gql"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/policy/constants"
	"nirvanatech.com/nirvana/policy/enums"
)

func (f *fnolsResolverTestSuite) TestGetFNOLs() {
	ctx := context.Background()

	f.Run("With no Fnols present", func() {
		user := f.usersFixture.Superuser.AuthzUser()
		fnols, err := f.getFNOLs(ctx, user, nil)
		f.Require().NoError(err)
		f.Require().Empty(fnols)
	})

	agencyId := f.agencyFixture.Agency.ID

	var dotNumber1 int64 = 123
	var dotNumber2 int64 = 456

	fleet1 := fleet.Fleet{
		ID:        uuid.New(),
		Name:      "Fleet 1",
		DotNumber: fmt.Sprintf("%d", dotNumber1),
	}
	f.Require().NoError(f.fleetWrapper.InsertFleet(ctx, fleet1))

	fleet2 := fleet.Fleet{
		ID:        uuid.New(),
		Name:      "Fleet 2",
		DotNumber: fmt.Sprintf("%d", dotNumber2),
	}
	f.Require().NoError(f.fleetWrapper.InsertFleet(ctx, fleet2))

	startDate := time_utils.NewDate(2024, 1, 1).ToTime()
	endDate := startDate.AddDate(1, 0, 0)

	policy1 := f.seedPolicy(
		ctx,
		agencyId,
		dotNumber1,
		enums.PolicyStateActive,
		constants.NirvanaPolicyCarrierALPrefix,
		"2000000",
		startDate,
		endDate,
	)

	lossDatetime := f.clk.Now().Round(time.Millisecond).UTC()
	narsFnol1, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithPolicyNumber(pointer_utils.String(policy1.PolicyNumber.String())).
		WithLossDatetime(&lossDatetime).
		WithCreatedBy(f.usersFixture.FleetAdmin.ID).
		Build()
	f.Require().NoError(err)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *narsFnol1))

	policy2 := f.seedPolicy(
		ctx,
		agencyId,
		dotNumber2,
		enums.PolicyStateActive,
		constants.NirvanaPolicyCarrierALPrefix,
		"3000000",
		startDate,
		endDate,
	)

	narsFnol2, err2 := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithPolicyNumber(pointer_utils.String(policy2.PolicyNumber.String())).
		WithLossDatetime(&lossDatetime).
		WithCreatedBy(f.usersFixture.FleetAdmin.ID).
		Build()
	f.Require().NoError(err2)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *narsFnol2))

	narsDraftFnol, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithStatus(fnol_enums.FnolStatusDraft).
		WithPolicyNumber(nil).
		Build()
	f.Require().NoError(err)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *narsDraftFnol))

	bizAutoPolicyNumber := constants.NirvanaBusinessAutoALPrefix + "4000000-24"
	bizAutoFnol, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithPolicyNumber(pointer_utils.String(bizAutoPolicyNumber)).
		WithLossDatetime(&lossDatetime).
		WithCreatedBy(f.usersFixture.FleetAdmin.ID).
		Build()
	f.Require().NoError(err)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *bizAutoFnol))

	snapsheetFnol1, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		WithPolicyNumber(pointer_utils.String(policy1.PolicyNumber.String())).
		WithLossDatetime(&lossDatetime).
		WithCreatedBy(f.usersFixture.FleetAdmin.ID).
		Build()
	f.Require().NoError(err)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *snapsheetFnol1))

	snapsheetFnol2, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		WithPolicyNumber(pointer_utils.String(policy2.PolicyNumber.String())).
		WithLossDatetime(&lossDatetime).
		WithCreatedBy(f.usersFixture.FleetAdmin.ID).
		Build()
	f.Require().NoError(err)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *snapsheetFnol2))

	testCases := []struct {
		name      string
		user      authz.User
		dotNumber *int64
		want      []gql.Fnol
		wantErr   bool
	}{
		{
			name: "With SuperUser and no DotNumber",
			user: f.usersFixture.Superuser.AuthzUser(),
			want: []gql.Fnol{
				{
					Id:            narsFnol1.Id,
					PolicyNumber:  *narsFnol1.PolicyNumber,
					NoticeType:    *narsFnol1.NoticeType,
					LossState:     *narsFnol1.LossState,
					LossDatetime:  *narsFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      narsFnol1.Source,
					SubmittedFrom: narsFnol1.SubmittedFrom,
				},
				{
					Id:            snapsheetFnol1.Id,
					PolicyNumber:  *snapsheetFnol1.PolicyNumber,
					NoticeType:    *snapsheetFnol1.NoticeType,
					LossState:     *snapsheetFnol1.LossState,
					LossDatetime:  *snapsheetFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      snapsheetFnol1.Source,
					SubmittedFrom: snapsheetFnol1.SubmittedFrom,
				},
				{
					Id:            narsFnol2.Id,
					PolicyNumber:  *narsFnol2.PolicyNumber,
					NoticeType:    *narsFnol2.NoticeType,
					LossState:     *narsFnol2.LossState,
					LossDatetime:  *narsFnol2.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      narsFnol2.Source,
					SubmittedFrom: narsFnol2.SubmittedFrom,
				},
				{
					Id:            snapsheetFnol2.Id,
					PolicyNumber:  *snapsheetFnol2.PolicyNumber,
					NoticeType:    *snapsheetFnol2.NoticeType,
					LossState:     *snapsheetFnol2.LossState,
					LossDatetime:  *snapsheetFnol2.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      snapsheetFnol2.Source,
					SubmittedFrom: snapsheetFnol2.SubmittedFrom,
				},
			},
			wantErr: false,
		},
		{
			name:      "With SuperUser & DotNumber",
			user:      f.usersFixture.Superuser.AuthzUser(),
			dotNumber: &dotNumber1,
			want: []gql.Fnol{
				{
					Id:            narsFnol1.Id,
					PolicyNumber:  *narsFnol1.PolicyNumber,
					NoticeType:    *narsFnol1.NoticeType,
					LossState:     *narsFnol1.LossState,
					LossDatetime:  *narsFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      narsFnol1.Source,
					SubmittedFrom: narsFnol1.SubmittedFrom,
				},
				{
					Id:            snapsheetFnol1.Id,
					PolicyNumber:  *snapsheetFnol1.PolicyNumber,
					NoticeType:    *snapsheetFnol1.NoticeType,
					LossState:     *snapsheetFnol1.LossState,
					LossDatetime:  *snapsheetFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      snapsheetFnol1.Source,
					SubmittedFrom: snapsheetFnol1.SubmittedFrom,
				},
			},
			wantErr: false,
		},
		{
			name: "With ClaimsAdminUser and no DotNumber",
			user: f.usersFixture.ClaimsAdmin.AuthzUser(),
			want: []gql.Fnol{
				{
					Id:            narsFnol1.Id,
					PolicyNumber:  *narsFnol1.PolicyNumber,
					NoticeType:    *narsFnol1.NoticeType,
					LossState:     *narsFnol1.LossState,
					LossDatetime:  *narsFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      narsFnol1.Source,
					SubmittedFrom: narsFnol1.SubmittedFrom,
				},
				{
					Id:            snapsheetFnol1.Id,
					PolicyNumber:  *snapsheetFnol1.PolicyNumber,
					NoticeType:    *snapsheetFnol1.NoticeType,
					LossState:     *snapsheetFnol1.LossState,
					LossDatetime:  *snapsheetFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      snapsheetFnol1.Source,
					SubmittedFrom: snapsheetFnol1.SubmittedFrom,
				},
				{
					Id:            narsFnol2.Id,
					PolicyNumber:  *narsFnol2.PolicyNumber,
					NoticeType:    *narsFnol2.NoticeType,
					LossState:     *narsFnol2.LossState,
					LossDatetime:  *narsFnol2.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      narsFnol2.Source,
					SubmittedFrom: narsFnol2.SubmittedFrom,
				},
				{
					Id:            snapsheetFnol2.Id,
					PolicyNumber:  *snapsheetFnol2.PolicyNumber,
					NoticeType:    *snapsheetFnol2.NoticeType,
					LossState:     *snapsheetFnol2.LossState,
					LossDatetime:  *snapsheetFnol2.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      snapsheetFnol2.Source,
					SubmittedFrom: snapsheetFnol2.SubmittedFrom,
				},
			},
			wantErr: false,
		},
		{
			name:    "With AgencyAdminUser and no DotNumber",
			user:    f.usersFixture.AgencyAdmin.AuthzUser(),
			want:    nil,
			wantErr: true,
		},
		{
			name:      "With AgencyAdminUser & DotNumber",
			user:      f.usersFixture.AgencyAdmin.AuthzUser(),
			dotNumber: pointer_utils.ToPointer(int64(123)),
			want:      nil,
			wantErr:   true,
		},
		{
			name:    "With AgencyProducerUser and no DotNumber",
			user:    f.usersFixture.AgencyProducer.AuthzUser(),
			want:    nil,
			wantErr: true,
		},
		{
			name:    "With AgencyProducerUser & DotNumber",
			user:    f.usersFixture.AgencyProducer.AuthzUser(),
			want:    nil,
			wantErr: true,
		},
		{
			name:    "With FleetAdminUser and no DotNumber",
			user:    f.usersFixture.FleetAdmin.AuthzUser(),
			want:    nil,
			wantErr: true,
		},
		{
			name:      "With FleetAdminUser & DotNumber they own",
			user:      test_utils.FleetAdminUser(fleet1.ID),
			dotNumber: &dotNumber1,
			want: []gql.Fnol{
				{
					Id:            narsFnol1.Id,
					PolicyNumber:  *narsFnol1.PolicyNumber,
					NoticeType:    *narsFnol1.NoticeType,
					LossState:     *narsFnol1.LossState,
					LossDatetime:  *narsFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      narsFnol1.Source,
					SubmittedFrom: narsFnol1.SubmittedFrom,
				},
				{
					Id:            snapsheetFnol1.Id,
					PolicyNumber:  *snapsheetFnol1.PolicyNumber,
					NoticeType:    *snapsheetFnol1.NoticeType,
					LossState:     *snapsheetFnol1.LossState,
					LossDatetime:  *snapsheetFnol1.LossDatetime,
					CreatedBy:     f.usersFixture.FleetAdmin.FullName(),
					Provider:      snapsheetFnol1.Source,
					SubmittedFrom: snapsheetFnol1.SubmittedFrom,
				},
			},
			wantErr: false,
		},
		{
			name:      "With FleetAdminUser & DotNumber they do not own",
			user:      test_utils.FleetAdminUser(fleet2.ID),
			dotNumber: &dotNumber1,
			want:      nil,
			wantErr:   true,
		},
		{
			name:    "With UnprivilegedUser",
			user:    test_utils.UnprivilegedUser(),
			want:    nil,
			wantErr: true,
		},
	}
	for _, tc := range testCases {
		f.Run(tc.name, func() {
			fnols, err := f.getFNOLs(ctx, tc.user, tc.dotNumber)
			if tc.wantErr {
				f.Error(err)
				f.Nil(tc.want)
				return
			}
			f.Require().NoError(err)
			f.Require().Equal(len(tc.want), len(fnols))

			slices.SortFunc(fnols, func(a, b gql.Fnol) int {
				return strings.Compare(a.Id.String(), b.Id.String())
			})
			slices.SortFunc(tc.want, func(a, b gql.Fnol) int {
				return strings.Compare(a.Id.String(), b.Id.String())
			})

			for i, want := range tc.want {
				got := fnols[i]

				f.Equal(want.Id.String(), got.Id.String())
				f.Equal(want.PolicyNumber, got.PolicyNumber)
				f.Equal(want.NoticeType, got.NoticeType)
				f.Equal(want.LossState, got.LossState)
				f.Equal(want.LossDatetime.UTC(), got.LossDatetime.UTC())
				f.Equal(want.CreatedBy, got.CreatedBy)
				f.Equal(want.Provider.String(), got.Provider.String())
				f.Equal(want.SubmittedFrom.String(), got.SubmittedFrom.String())
			}
		})
	}
}

func (f *fnolsResolverTestSuite) getFNOLs(
	ctx context.Context,
	user authz.User,
	dotNumber *int64,
) ([]gql.Fnol, error) {
	query := `query fetchFNOLs($dotNumber: Int) {
		fnols(dotNumber: $dotNumber) {
			id
			policyNumber
			noticeType
			lossState
			lossDatetime
			createdBy
			provider
			submittedFrom
		}
	}`

	var getFnolsOutput struct {
		Fnols []gql.Fnol
	}

	queryVars := map[string]interface{}{
		"dotNumber": dotNumber,
	}
	err := f.gqlClient.Query(ctx, user, query, queryVars).ResultAs(&getFnolsOutput)
	return getFnolsOutput.Fnols, err
}
