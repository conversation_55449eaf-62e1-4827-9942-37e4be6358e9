package api

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strconv"

	"github.com/cactus/go-statsd-client/v5/statsd"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/infra/config"
)

const unknownOperationName = "UnknownOperation"

// NarsInstrumentedTransport is a custom http.RoundTripper that instruments the NARS API requests with logging, metrics
// and traces.
type NarsInstrumentedTransport struct {
	statter   statsd.Statter
	transport http.RoundTripper
}

func NewInstrumentedTransport(statter statsd.Statter) *NarsInstrumentedTransport {
	return &NarsInstrumentedTransport{
		statter:   statter,
		transport: http.DefaultTransport,
	}
}

func (n NarsInstrumentedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	operationName := getOperationName(req.URL.Path)

	ctx := log.ContextWithFields(
		req.Context(),
		log.String("operation", operationName),
		log.String("method", req.Method),
		log.Stringer("url", req.URL),
	)

	ctx, span := tracing.Start(ctx, fmt.Sprintf("nars.%s", operationName))
	defer span.End()

	log.Info(ctx, "Requesting to NARS")

	resp, err := n.transport.RoundTrip(req)

	var statusCode int
	success := err == nil
	if resp != nil {
		statusCode = resp.StatusCode
		success = err == nil && statusCode >= 200 && statusCode < 300
	}
	ctx = log.ContextWithFields(ctx, log.Int("statusCode", statusCode), log.Bool("success", success))

	if err := n.statter.Inc("nars.request", 1, 1,
		statsd.Tag{"method", req.Method},
		statsd.Tag{"operation", operationName},
		statsd.Tag{"success", strconv.FormatBool(success)},
		statsd.Tag{"status", strconv.Itoa(statusCode)},
	); err != nil {
		log.Error(ctx, "failed to emit metric", log.Err(err))
	}

	if success {
		log.Info(ctx, "Request to NARS completed")
	} else {
		log.Error(ctx, "Request to NARS failed", log.Err(err))
	}
	return resp, err
}

// getOperationName returns the operation name based on the URL path. We do NOT want to use the URL path for metrics
// tagging purposes, as that would lead to cardinality explosion (because of URL & query params). By using operation
// names, we are able to reduce the cardinality to a small, finite number.
//
// TODO: ideally we should have an enum for this (i.e. no regex), and a wrapper on top of the NARS client (with client
// being private) such that every time someone wants to add a new request, they are forced to define a new method and
// add a new operation name to the enum. That way, we can ensure that no new API call is added without its corresponding
// operation name. Another improvement to make - differentiate not just by path, but also by HTTP verb. Right now this
// is not an issue though.
func getOperationName(url string) string {
	patterns := map[string]string{
		`^/claims$`:                     "ListClaims",
		`^/claim$`:                      "CreateClaim",
		`^/claim/[^/]+$`:                "GetClaim",
		`^/claim/[^/]+/reservesummary$`: "GetReserveSummary",
		`^/claim/[^/]+/reservedetail$`:  "GetReserveDetail",
		`^/claim/[^/]+/note$`:           "GetClaimNote",
		`^/clientclaim/[^/]+$`:          "GetClientClaim",
		`^/claim/[^/]+/party$`:          "GetClaimParties",
		`^/claim/[^/]+/posting$`:        "GetClaimPostings",
	}

	for pattern, operationName := range patterns {
		matched, err := regexp.MatchString(pattern, url)
		if err != nil {
			return unknownOperationName
		}
		if matched {
			return operationName
		}
	}
	return unknownOperationName
}

func newAPIClient(cfg *config.Config, statter statsd.Statter) (*ClientWithResponses, error) {
	return NewClientWithResponses(
		productionServerURL,
		WithHTTPClient(&http.Client{Transport: NewInstrumentedTransport(statter)}),
		WithRequestEditorFn(func(ctx context.Context, req *http.Request) error {
			req.Header.Set("x-api-key", cfg.GetProductTools().GetNarsApiKey())
			return nil
		}),
	)
}
