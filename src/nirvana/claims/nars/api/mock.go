package api

import (
	"context"
	_ "embed"
	"encoding/json"
	"io"
	"net/http"

	"nirvanatech.com/nirvana/claims/nars/api/components"

	"github.com/cockroachdb/errors"
)

const narsNotFoundResponse = "No Data Found"

//go:embed testdata/claims.json
var MockGetClaimsResponse []byte

//go:embed testdata/claim_by_claim_number.json
var MockGetClaimByClaimNumberResponses []byte

//go:embed testdata/claim_reserve_summary_by_claim_number.json
var MockGetClaimClaimNumberReserveSummaryResponses []byte

//go:embed testdata/claim_reserve_details_by_claim_number.json
var MockGetClaimClaimNumberReserveDetailResponses []byte

//go:embed testdata/claim_note_by_claim_number.json
var MockGetClaimClaimnumberNoteResponseResponses []byte

//go:embed testdata/claim_parties_by_claim.json
var MockGetClaimClaimNumberPartyResponses []byte

//go:embed testdata/claim_postings_by_claim_number.json
var MockGetClaimClaimNumberPostingResponses []byte

func newMockAPIClient() (*mockAPIClient, error) {
	claimsDetailsResponses := make(map[string]components.ClaimResponse)
	reserveSummaryResponses := make(map[string]any)
	reserveDetailResponses := make(map[string]any)
	noteResponses := make(map[string]any)
	partiesResponses := make(map[string]any)
	postingsResponses := make(map[string]map[string]any)

	if err := json.Unmarshal(MockGetClaimByClaimNumberResponses, &claimsDetailsResponses); err != nil {
		return nil, errors.Wrap(err, "failed to embed claims details response")
	}

	if err := json.Unmarshal(MockGetClaimClaimNumberReserveSummaryResponses, &reserveSummaryResponses); err != nil {
		return nil, errors.Wrap(err, "failed to embed claims details response")
	}

	if err := json.Unmarshal(MockGetClaimClaimNumberReserveDetailResponses, &reserveDetailResponses); err != nil {
		return nil, errors.Wrap(err, "failed to embed claims details response")
	}

	if err := json.Unmarshal(MockGetClaimClaimnumberNoteResponseResponses, &noteResponses); err != nil {
		return nil, errors.Wrap(err, "failed to embed claim note responses")
	}

	if err := json.Unmarshal(MockGetClaimClaimNumberPartyResponses, &partiesResponses); err != nil {
		return nil, errors.Wrap(err, "failed to embed claim parties responses")
	}

	if err := json.Unmarshal(MockGetClaimClaimNumberPostingResponses, &postingsResponses); err != nil {
		return nil, errors.Wrap(err, "failed to embed claim postings responses")
	}

	return &mockAPIClient{
		GetClaimByClaimNumberResponsesByClaimNumber:             claimsDetailsResponses,
		GetClaimClaimNumberReserveSummaryResponsesByClaimNumber: reserveSummaryResponses,
		GetClaimClaimNumberReserveDetailResponsesByClaimNumber:  reserveDetailResponses,
		GetClaimClaimNumberNoteResponsesByClaimNumber:           noteResponses,
		GetClaimClaimNumberPartyResponsesByClaimNumber:          partiesResponses,
		GetClaimClaimNumberPostingResponsesByClaimNumber:        postingsResponses,
	}, nil
}

type mockAPIClient struct {
	GetClaimByClaimNumberResponsesByClaimNumber             map[string]components.ClaimResponse
	GetClaimClaimNumberReserveSummaryResponsesByClaimNumber map[string]any
	GetClaimClaimNumberReserveDetailResponsesByClaimNumber  map[string]any
	GetClaimClaimNumberNoteResponsesByClaimNumber           map[string]any
	GetClaimClaimNumberPartyResponsesByClaimNumber          map[string]any
	GetClaimClaimNumberPostingResponsesByClaimNumber        map[string]map[string]any
}

func (c *mockAPIClient) GetClaimByClaimNumberWithResponse(
	_ context.Context,
	claimNumber string,
	_ *GetClaimByClaimNumberParams,
	_ ...RequestEditorFn,
) (*GetClaimByClaimNumberResponse, error) {
	obj, found := c.GetClaimByClaimNumberResponsesByClaimNumber[claimNumber]
	bResponse := mustMarshal(narsNotFoundResponse)
	if found {
		bResponse = mustMarshal(obj)
	}
	return &GetClaimByClaimNumberResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: bResponse,
	}, nil
}

func (c *mockAPIClient) GetClaimClaimNumberReservesummaryWithResponse(
	_ context.Context,
	claimNumber string,
	_ *GetClaimClaimNumberReservesummaryParams,
	_ ...RequestEditorFn,
) (*GetClaimClaimNumberReservesummaryResponse, error) {
	obj, found := c.GetClaimClaimNumberReserveSummaryResponsesByClaimNumber[claimNumber]
	bResponse := mustMarshal(narsNotFoundResponse)
	if found {
		bResponse = mustMarshal(obj)
	}
	return &GetClaimClaimNumberReservesummaryResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: bResponse,
	}, nil
}

func (c *mockAPIClient) GetClaimClaimNumberReservedetailWithResponse(
	_ context.Context,
	claimNumber string,
	_ *GetClaimClaimNumberReservedetailParams,
	_ ...RequestEditorFn,
) (*GetClaimClaimNumberReservedetailResponse, error) {
	obj, found := c.GetClaimClaimNumberReserveDetailResponsesByClaimNumber[claimNumber]
	bResponse := mustMarshal(narsNotFoundResponse)
	if found {
		bResponse = mustMarshal(obj)
	}
	return &GetClaimClaimNumberReservedetailResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: bResponse,
	}, nil
}

func (c *mockAPIClient) GetClaimsWithResponse(
	_ context.Context,
	_ *GetClaimsParams,
	_ ...RequestEditorFn,
) (*GetClaimsResponse, error) {
	return &GetClaimsResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: MockGetClaimsResponse,
	}, nil
}

func (c *mockAPIClient) GetClaimClaimNumberNoteWithResponse(
	_ context.Context,
	claimNumber string,
	_ *GetClaimClaimNumberNoteParams,
	_ ...RequestEditorFn,
) (*GetClaimClaimNumberNoteResponse, error) {
	obj, found := c.GetClaimClaimNumberNoteResponsesByClaimNumber[claimNumber]
	bResponse := mustMarshal(narsNotFoundResponse)
	if found {
		bResponse = mustMarshal(obj)
	}
	return &GetClaimClaimNumberNoteResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: bResponse,
	}, nil
}

func (c *mockAPIClient) GetClaimClaimNumberPartyWithResponse(
	_ context.Context,
	claimNumber string,
	_ *GetClaimClaimNumberPartyParams,
	_ ...RequestEditorFn,
) (*GetClaimClaimNumberPartyResponse, error) {
	obj, found := c.GetClaimClaimNumberPartyResponsesByClaimNumber[claimNumber]
	bResponse := mustMarshal(narsNotFoundResponse)
	if found {
		bResponse = mustMarshal(obj)
	}
	return &GetClaimClaimNumberPartyResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: bResponse,
	}, nil
}

func (c *mockAPIClient) GetClaimClaimNumberPostingWithResponse(
	_ context.Context,
	claimNumber string,
	params *GetClaimClaimNumberPostingParams,
	_ ...RequestEditorFn,
) (*GetClaimClaimNumberPostingResponse, error) {
	postingsByDate, found := c.GetClaimClaimNumberPostingResponsesByClaimNumber[claimNumber]
	bResponse := mustMarshal(narsNotFoundResponse)
	if found {
		obj, found := postingsByDate[params.Modifieddate]
		if found {
			bResponse = mustMarshal(obj)
		}
	}
	return &GetClaimClaimNumberPostingResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusOK,
		},
		Body: bResponse,
	}, nil
}

func (c *mockAPIClient) PostClaimWithBodyWithResponse(
	_ context.Context,
	_ *PostClaimParams,
	_ string,
	_ io.Reader,
	_ ...RequestEditorFn,
) (*PostClaimResponse, error) {
	return &PostClaimResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusCreated,
		},
		Body: mustMarshal(""),
	}, nil
}

func (c *mockAPIClient) PostClaimWithResponse(
	_ context.Context,
	_ *PostClaimParams,
	_ components.CreateClaimRequest,
	_ ...RequestEditorFn,
) (*PostClaimResponse, error) {
	return &PostClaimResponse{
		HTTPResponse: &http.Response{
			StatusCode: http.StatusCreated,
		},
		Body: mustMarshal(""),
	}, nil
}

var _ ClientWithResponsesInterface = &mockAPIClient{}

func mustMarshal(v any) []byte {
	r, err := json.Marshal(v)
	if err != nil {
		panic("failed to marshal mock")
	}
	return r
}
