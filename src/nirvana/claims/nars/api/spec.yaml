openapi: 3.0.1
info:
  title: NARS ClaimAPI
  version: "1.0"
servers:
- url: https://api.narisk.com
  description: NARS production environment
  x-amazon-apigateway-endpoint-configuration:
    disableExecuteApiEndpoint: true
tags:
- name: Claims
- name: Reserves
paths:
  /claims:
    get:
      tags:
      - Claims
      summary: Get list of claims.
      parameters:
        - name: detailsaremodified
          in: query
          schema:
            type: boolean
            default: false
          required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: './components/spec.yaml#/components/schemas/ClaimsResponse'
  /claim:
    post:
      tags:
      - Claims
      summary: Submit a claim.
      parameters:
        - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/spec.yaml#/components/schemas/CreateClaimRequest'
      responses:
        201:
          description: Processed
        400:
          description: Invalid Data
  /claim/{claimNumber}:
    get:
      operationId: GetClaimByClaimNumber
      tags:
      - Claims
      summary: Detailed information based on a 'claimNumber' provided.
      parameters:
      - name: claimNumber
        in: path
        required: true
        explode: false
        style: simple
        schema:
          type: string
        x-example: NITNI34060118
      - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: './components/spec.yaml#/components/schemas/ClaimResponse'
  /claim/{claimNumber}/reservesummary:
    get:
      tags:
      - Reserves
      parameters:
      - name: claimNumber
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
        x-example: ABCD17050010
      - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      responses:
        200:
          description: A JSON array of reserve summary data, or a string indicating no reserves were found.
          # No schema is defined here because the response can be either an array or a string, and
          # codegen tools may not handle this well, resulting in errors when attempting to unmarshal
          # the response.
  /claim/{claimNumber}/reservedetail:
    get:
      tags:
      - Reserves
      parameters:
      - name: claimNumber
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
        x-example: ABCD17050010
      - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      responses:
        200:
          description: A JSON array of reserve detail data, or a string indicating no reserve details were found. 
          # No schema is defined here because the response can be either an array or a string, and
          # codegen tools may not handle this well, resulting in errors when attempting to unmarshal
          # the response.
  /claim/{claimNumber}/note:
    get:
      tags:
        - Notes
      summary: Note Detail For All Lines of Business Based On ClaimNumber Parameter Provided
      parameters:
        - name: claimNumber
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          x-example: ABCD17050010
        - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      responses:
        200:
          description: A JSON array of note data, or a string indicating no notes were found.
          # No schema is defined here because the response can be either an array or a string, and
          # codegen tools may not handle this well, resulting in errors when attempting to unmarshal
          # the response.
  /claim/{claimNumber}/party:
    get:
      tags:
        - Parties
      summary: Detailed Party Information For All Lines of Business Based On ClaimNumber Parameter Provided
      parameters:
        - name: claimNumber
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          x-example: ABCD17050010
        - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      responses:
        200:
          description: A JSON array of party data, or a string indicating no parties were found.
          # No schema is defined here because the response can be either an array or a string, and
          # codegen tools may not handle this well, resulting in errors when attempting to unmarshal
          # the response.
  /claim/{claimNumber}/posting:
    get:
      tags:
        - Postings
      summary: All payment-related details will be available within this endpoint, including payments, recoveries, and adjustments to payments and recoveries.
      parameters:
        - name: claimNumber
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          x-example: ABCD17050010
        - name: modifieddate
          in: query
          required: true
          schema:
            type: string
            x-example: 20230101
        - $ref: './components/spec.yaml#/components/parameters/XApiKeyHeader'
      responses:
        200:
          description: A JSON array of posting data, or a string indicating no postings were found. 
          # No schema is defined here because the response can be either an array or a string, and
          # codegen tools may not handle this well, resulting in errors when attempting to unmarshal
          # the response.
