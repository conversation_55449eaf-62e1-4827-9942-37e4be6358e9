## Filenames and exports

- Use camelCase for defining hooks or functions: When creating files for hooks or functions, use camelCase naming convention. Also, in case of hooks, they should always start with useXX, for example useNavigation, useLocalStorage
- Use PascalCase for defining React components
- Ensure the filename matches the exported function/component name
- Prefer named exports over default exports

### Examples

**_Correct Way_**

1. Filename matches exported function
2. Named export
3. Hook starts with `use` prefix

```typescript
// Filename: useLocalStorage.ts

export function useLocalStorage(key: string, initialValue: string) {
  // Implementation
}
```

**_Incorrect Way_**

1. Default exports

```typescript
// Filename: useLocalStorage.ts

function useLocalStorage(key: string, initialValue: string) {
  // Implementation
}
export default useLocalStorage;
```

2. Filename does not match exported function

```typescript
// Filename: useNavigation.ts
export const useNavigateWithState = () => {
  // Implementation
};
```
