import { Select, MenuItem } from '@material-ui/core';

import { AgencyBDMapping, User } from '@nirvana/api/quoting';

type ProgramTypeFilterProps = {
  data: any;
  handleUpdateBD: (row: AgencyBDMapping) => void;
};

export default function UpdateBDFilter({
  data,
  handleUpdateBD,
}: ProgramTypeFilterProps) {
  return (
    <div>
      <Select
        variant="outlined"
        value={data.user.name}
        className="w-48"
        onChange={(e) => {
          const selected = data.userOptions.find(
            (user: User) => user.name === e.target.value,
          );
          data.user = selected;
          handleUpdateBD(data);
        }}
      >
        {data.userOptions.map((user: User) => (
          <MenuItem key={user.name} value={user.name}>
            {user.name}
          </MenuItem>
        ))}
      </Select>
    </div>
  );
}
