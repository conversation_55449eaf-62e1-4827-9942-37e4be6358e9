import * as React from 'react';
import { useLocation, Navigate } from 'react-router-dom';
import useAuthContext from 'src/hooks/use-auth-context';

type AuthLayoutProps = {
  component: React.ComponentType<any>;
};

export default function AuthLayout({ component: Component }: AuthLayoutProps) {
  const { user } = useAuthContext();
  const location = useLocation();

  if (!user) {
    return <Navigate to="/" state={{ from: location }} />;
  }

  return <Component />;
}
