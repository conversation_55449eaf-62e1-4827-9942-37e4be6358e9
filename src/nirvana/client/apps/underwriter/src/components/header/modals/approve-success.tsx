import { Button } from '@material-ui/core';
import { useNavigate } from 'react-router-dom';

import { Dialog } from '@nirvana/ui-kit';
import ApproveSuccessIcon from 'src/assets/icons/approve-success.svg?react';

type ApproveSuccessProps = {
  open: boolean;
  onClose: () => void;
  url: string;
  navigateTo?: string;
};

export default function ApproveSuccessModal({
  open,
  onClose,
  url,
  navigateTo = '/fleet/applications',
}: ApproveSuccessProps) {
  const navigate = useNavigate();
  const handleDismiss = () => {
    onClose();
    navigate(navigateTo);
  };

  return (
    <Dialog
      open={open}
      primaryAction={
        <Button
          variant="contained"
          onClick={handleDismiss}
          href={url}
          target="_blank"
          LinkComponent="a"
          className="ml-2"
        >
          Generate Signature Packet
        </Button>
      }
      secondaryAction={
        <Button variant="outlined" onClick={handleDismiss} className="flex-1">
          Dismiss
        </Button>
      }
      onClose={onClose}
    >
      <div className="flex flex-col items-center max-w-xl text-center w-80">
        <ApproveSuccessIcon />
        <p className="mt-5 text-xl font-semibold text-text-primary">
          Application Approved
        </p>
        <p className="mt-2 text-xs text-text-hint">
          Congratulations on approving the application! You may now generate and
          release the Signature Packet to the Agent
        </p>
      </div>
    </Dialog>
  );
}
