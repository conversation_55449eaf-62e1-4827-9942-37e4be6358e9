import clsx from 'clsx';
import type { MouseEvent } from 'react';
import { useQueries } from 'react-query';
import { useParams } from 'react-router-dom';
import { useRef, useMemo, useState } from 'react';

import { Chip, Show, Switch } from '@nirvana/ui-kit';
import { getFormattedDate } from '@nirvana/core/utils';

import colors from 'src/constants/colors';
import TRSShieldIcon from 'src/assets/icons/trs-shield.svg?react';
import CheckCircleIcon from 'src/assets/icons/check-circle-bgblack.svg?react';
import ExclamationCircleIcon from 'src/assets/icons/exclamation-circle.svg?react';
import { RecommendedActionNotification } from 'src/components/recommended-action-notification';
import {
  getColorSettings,
  getFormattedActionType,
  getFormattedRefreshReasons,
  getActionIcon,
  getTRSIcon,
  getFormattedReason,
} from 'src/utils/recommendation';
import {
  fetchRecommendationsData,
  fetchRecommendedActionTrail,
  fetchApplicationReviewById,
} from 'src/queries/applications';

import { RiskType, riskTypeIcons, riskLevelOrder } from './utils';
import RecommendationHistoryTrailModal from './recommendation-trail-modal';
import VinVisibility from './vin-visibility';

type RiskIconProps = {
  riskType: RiskType;
  className?: string;
};

type RiskCounts = {
  [key: string]: number;
};

function RiskIcon({ riskType, className = '' }: RiskIconProps) {
  const Icon = riskTypeIcons[riskType];
  return <Icon className={`w-6 h-6 ${className}`} />;
}

export default function PersistentSummary() {
  const topRef = useRef<HTMLDivElement>(null);
  const { appReviewId = '' } = useParams();
  const [{ data: cardData }, { data: trailData }, { data }] = useQueries([
    {
      queryKey: ['recommended-actions', appReviewId],
      queryFn: () => fetchRecommendationsData(appReviewId),
    },
    {
      queryKey: ['recommendationsActionTrail', appReviewId],
      queryFn: () => fetchRecommendedActionTrail(appReviewId),
    },
    {
      queryKey: ['applications', appReviewId],
      queryFn: () => fetchApplicationReviewById(appReviewId),
      enabled: !!appReviewId,
    },
  ]);

  const shouldRenderRecommendationHistoryModal =
    trailData?.trailItems && trailData?.trailItems?.length > 1;

  const actionType = cardData?.recommendedAction?.action;
  const telematicsRiskScore = cardData?.telematicsRiskScore?.actualScore;

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleOpenModal = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const getFormattedDateRange = (startDate: string, endDate: string) => {
    const startMonth = getFormattedDate(startDate, 'MMM');
    const endMonth = getFormattedDate(endDate, 'MMM');
    const year = getFormattedDate(endDate, 'yyyy');

    return `${startMonth}-${endMonth} ${year}`;
  };

  const scrollToTop = () => {
    topRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  const riskCounts = useMemo(
    () =>
      cardData?.traditionalRiskFactors?.reduce<RiskCounts>((acc, curr) => {
        const { appetiteFlag, riskFactors } = curr;
        acc[appetiteFlag] =
          (acc[appetiteFlag] || 0) + (riskFactors?.length ?? 0);
        return acc;
      }, {}),
    [cardData?.traditionalRiskFactors],
  );

  const riskLevels = useMemo(
    () =>
      Object.entries(riskCounts ?? {})
        .map(([type, count]) => ({ type, count }))
        // Sort based on predefined risk level order
        .sort((a, b) => {
          // Assert that a.type and b.type are keys of riskLevelOrder
          const orderA = riskLevelOrder[a.type as keyof typeof riskLevelOrder];
          const orderB = riskLevelOrder[b.type as keyof typeof riskLevelOrder];
          return orderA - orderB;
        }),
    [riskCounts],
  );

  if (!data?.panelWiseReviewInfo.overview || !cardData) {
    return null;
  }
  const { border, bg } = getColorSettings(actionType);
  const hasTelematicsRiskScore =
    cardData?.telematicsRiskScore?.actualScore !== undefined;

  return (
    <div
      ref={topRef}
      className={clsx(
        'flex flex-row overflow-hidden border-2 bg-white rounded-lg',
        border,
      )}
    >
      {/* Left container */}
      <div className={clsx('w-1/3 h-full p-6 rounded-lg', bg)}>
        <div className="flex items-start space-x-4">
          {getActionIcon(actionType)}
          <div>
            <p className="mb-1 text-xl font-bold">
              {getFormattedActionType(actionType)}
            </p>
            <p className="text-base font-semibold">
              {getFormattedReason(cardData.recommendedAction?.reason)}
            </p>
          </div>
        </div>
      </div>

      {/* Right container */}
      <div className="flex flex-col w-2/3">
        {/* New upper container - Adjusted for 2/3 height */}
        <div className="flex items-start p-2 pl-6 my-4 space-x-2 flex-grow-2">
          <div className="flex-1 space-y-2">
            <Switch>
              <Switch.Match when={cardData?.telematicsRiskScore?.scoreType}>
                <span className="text-text-primary">TRS</span>
              </Switch.Match>
              <Switch.Match when={!cardData?.telematicsRiskScore?.scoreType}>
                <span className="text-text-primary">Safety Score</span>
              </Switch.Match>
            </Switch>

            <Switch>
              <Switch.Match when={!hasTelematicsRiskScore}>
                <div className="flex items-center space-x-2">
                  <TRSShieldIcon
                    fill={colors.text.disabled}
                    stroke={colors.text.hint}
                  />
                  <p className="text-base">No TRS data</p>
                </div>
              </Switch.Match>

              <Switch.Match when={cardData.telematicsRiskScore?.isShortHaul}>
                <div className="flex items-center space-x-2">
                  <TRSShieldIcon
                    fill={colors.text.disabled}
                    stroke={colors.text.hint}
                  />
                  <p className="text-base">No Calculation</p>
                </div>
                <Chip
                  icon={<ExclamationCircleIcon fill={colors.info.main} />}
                  color="info"
                  label="Short Haul"
                />
              </Switch.Match>

              <Switch.Match when={hasTelematicsRiskScore}>
                <div className="flex items-center">
                  <span className="ml-1 mr-2">
                    {getTRSIcon(telematicsRiskScore)}
                  </span>
                  <p className="mr-2 text-base font-bold">
                    {cardData?.telematicsRiskScore?.actualScore}
                  </p>
                  <Show when={cardData?.isTspException}>
                    <Chip
                      icon={<ExclamationCircleIcon fill={colors.info.main} />}
                      color="info"
                      label="Exception"
                    />
                  </Show>
                </div>
              </Switch.Match>
            </Switch>

            {cardData?.telematicsRiskScore?.windowStartDate &&
              cardData?.telematicsRiskScore?.windowEndDate && (
                <p>
                  {cardData?.telematicsRiskScore?.vinCount && (
                    <>{cardData?.telematicsRiskScore?.vinCount} VINs </>
                  )}
                  {cardData?.telematicsRiskScore?.windowStartDate &&
                    cardData?.telematicsRiskScore?.windowEndDate && (
                      <>
                        {getFormattedDateRange(
                          cardData?.telematicsRiskScore?.windowStartDate,
                          cardData?.telematicsRiskScore?.windowEndDate,
                        )}
                      </>
                    )}
                </p>
              )}
            <p>{cardData?.telematicsRiskScore?.category}</p>
          </div>

          <VinVisibility data={cardData.vinVisibility} />

          <div className="flex-1 space-y-2">
            <p>Traditional risk factors</p>
            {riskLevels.map(({ type, count }) => (
              <div key={type} className="flex items-center">
                <RiskIcon riskType={type as RiskType} className="mr-2" />
                <p className="font-bold">
                  <span className="mr-1">{count}</span>
                </p>
                {type}
              </div>
            ))}
          </div>
        </div>

        {/* Existing lower container - Adjusted for 1/3 height */}
        <div className="flex-1">
          <div className="p-2 pl-6 border-t-2">
            <div className="flex items-center">
              <CheckCircleIcon className="text-black" />
              <span className="ml-2 font-bold text-gray-700">
                Computed on most updated data
              </span>
            </div>
            <Show
              when={!shouldRenderRecommendationHistoryModal}
              fallback={
                cardData?.recommendedAction?.refreshDate ? (
                  <div className="ml-8 text-xs">
                    refreshed{' '}
                    <span
                      className="font-bold underline cursor-pointer text-primary-main"
                      onClick={handleOpenModal}
                    >
                      {getFormattedDate(
                        cardData.recommendedAction.refreshDate,
                        'MMM d yyyy',
                      )}
                    </span>{' '}
                    <Show
                      when={
                        cardData?.recommendedAction?.refreshReason?.length &&
                        cardData?.recommendedAction?.refreshReason?.length > 0
                      }
                    >
                      <>
                        {' '}
                        due to change in{' '}
                        {getFormattedRefreshReasons(
                          cardData?.recommendedAction?.refreshReason ?? [],
                        )}
                      </>
                    </Show>
                  </div>
                ) : null
              }
            >
              <div className="ml-8 text-xs">
                recommendation created{' '}
                {getFormattedDate(
                  cardData?.recommendedAction?.refreshDate ?? '',
                  'MMM d, yyyy',
                )}
              </div>
            </Show>
          </div>
        </div>
      </div>
      <Show when={shouldRenderRecommendationHistoryModal}>
        <RecommendationHistoryTrailModal
          onClose={() => {
            setAnchorEl(null);
          }}
          anchorEl={anchorEl}
        />
      </Show>
      <RecommendedActionNotification onScrollToTop={scrollToTop} />
    </div>
  );
}
