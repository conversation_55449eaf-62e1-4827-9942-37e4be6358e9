import {
  ApplicationReviewAggregateCreditLimitsByCoverageData,
  ApplicationReviewCoverageInfoStateEnum,
  ApplicationReviewQuoteCombinedDeductibleCoveragesEnum,
  ApplicationReviewQuoteCoverageType,
  ApplicationReviewQuoteStatusEnum,
} from '@nirvana/api/uw';
import { Show } from '@nirvana/ui-kit';
import { useSnackbar } from 'notistack';
import { useQuery, useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';

import { useEffect } from 'react';
import useCoverageMutation from 'src/hooks/use-coverage-mutation';
import { usePanelReviewContext } from 'src/hooks/use-panel-review';
import useQuoteMutation from 'src/hooks/use-quote-mutation';
import { fetchNegotiatedRates } from 'src/pages/packages/queries';
import CoverageCard from './coverage-card';
import { isCreditLimitBreached } from './utils';

type AutoPhysicalDamageProps = {
  apdPremium: ApplicationReviewQuoteCoverageType | undefined;
  creditLimits?: ApplicationReviewAggregateCreditLimitsByCoverageData;
  credit: number | undefined;
  isLoading: boolean;
  isBadgesLoading: boolean;
  quoteStatus: ApplicationReviewQuoteStatusEnum | undefined;
  combinedCoverages?: ApplicationReviewQuoteCombinedDeductibleCoveragesEnum[];
  isNegotiatedRateApplied: boolean;
  usState?: string;
  isRecommendationRangeEnabled: boolean;
};

export default function AutoPhysicalDamage({
  apdPremium,
  credit,
  isLoading,
  isBadgesLoading,
  quoteStatus,
  combinedCoverages,
  isNegotiatedRateApplied,
  creditLimits,
  isRecommendationRangeEnabled,
}: AutoPhysicalDamageProps) {
  const { appReviewId = '' } = useParams();
  const { coverages } = usePanelReviewContext();
  const { enqueueSnackbar } = useSnackbar();
  const queryClient = useQueryClient();
  const { mutate: updateQuote } = useQuoteMutation();
  const { mutate, isLoading: isMutatingCoverage } = useCoverageMutation();

  const { data: negotiatedRates } = useQuery(
    ['negotiated-rates', appReviewId],
    () => fetchNegotiatedRates(appReviewId),
  );

  const { isCreditLimitBelowMinimum, isCreditLimitExceeded } =
    isCreditLimitBreached(credit, creditLimits);

  useEffect(() => {
    // scroll the component into view when isCreditLimitExceeded or isCreditLimitBelowMinimum is true
    if (isCreditLimitExceeded || isCreditLimitBelowMinimum) {
      const element = document.getElementById('auto-physical-damage');

      element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [isCreditLimitExceeded, isCreditLimitBelowMinimum]);

  function handleChange(checked: boolean) {
    mutate(
      {
        appReviewId,
        applicationCoverages: {
          autoPhysicalDamage: {
            state: checked
              ? ApplicationReviewCoverageInfoStateEnum.Approved
              : ApplicationReviewCoverageInfoStateEnum.Declined,
          },
        },
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries(['negotiated-rates', appReviewId]);
          if (negotiatedRates?.isNegotiatedRatesApplied) {
            enqueueSnackbar(
              'Negotiated Rate was automatically disabled. Please review the Negotiated Rates widget again before proceeding.',
              { variant: 'warning' },
            );
          }
          updateQuote({ appReviewId });
        },
      },
    );
  }

  const state = coverages?.autoPhysicalDamage?.state;

  return (
    <Show when={state}>
      <CoverageCard
        title="APD"
        id="auto-physical-damage"
        coverage={apdPremium}
        creditLimits={creditLimits}
        credit={credit}
        isLoading={isLoading}
        isBadgesLoading={isBadgesLoading}
        quoteStatus={quoteStatus}
        isMutatingCoverage={isMutatingCoverage}
        combinedCoverages={combinedCoverages}
        isNegotiatedRateApplied={isNegotiatedRateApplied}
        isRecommendationRangeEnabled={isRecommendationRangeEnabled}
        showToggle={true}
        toggleState={state}
        onToggleChange={handleChange}
        filterKey={
          ApplicationReviewQuoteCombinedDeductibleCoveragesEnum.AutoPhysicalDamage
        }
        additionalInfo={
          apdPremium?.tivPercentage
            ? { type: 'tiv', value: apdPremium.tivPercentage }
            : undefined
        }
      />
    </Show>
  );
}
