import apiService from 'src/utils/api-service';
import { ApplicationReviewTargetPriceOverride } from '@nirvana/api/uw';

export async function fetchApplicationTargetPrice(appReviewId: string) {
  const { data } = await apiService.getApplicationReviewTargetPrice(
    appReviewId,
  );
  return data;
}

export async function updateApplicationTargetPrice({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: ApplicationReviewTargetPriceOverride;
}) {
  const { data } = await apiService.updateApplicationReviewTargetPrice(
    appReviewId,
    payload,
  );

  return data;
}
