import {
  ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum,
  RecommendationInfo,
  RecommendationInfoStateEnum,
} from '@nirvana/api/uw';
import { Switch } from '@nirvana/ui-kit';
import { HiBan, HiChevronDoubleDown, HiChevronDoubleUp } from 'react-icons/hi';

export const getRecommendationIcon = (data: RecommendationInfo) => {
  return (
    <Switch>
      <Switch.Match
        when={
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.QuoteMore ||
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.BindMore ||
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.DecreaseMargin ||
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.ApplySubsidy ||
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.DelightAgent
        }
      >
        <HiChevronDoubleUp className="text-xl text-success-main" />
      </Switch.Match>

      <Switch.Match
        when={
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.QuoteLess ||
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.BindLess ||
          data.recommendedAction.primaryAction ===
            ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.IncreaseMargin
        }
      >
        <HiChevronDoubleDown className="text-xl text-error-main" />
      </Switch.Match>

      <Switch.Match
        when={
          data.recommendedAction.primaryAction ===
          ApplicationReviewRecommendationRecommendedActionPrimaryActionEnum.DontQuote
        }
      >
        <HiBan className="text-xl text-error-main" />
      </Switch.Match>
    </Switch>
  );
};

export const getSortedRecommendations = (data: RecommendationInfo[]) => {
  return data.sort((a, b) => {
    // Sort stale recommendations to the bottom and Pending recommendations to the top
    if (a.state === RecommendationInfoStateEnum.Stale) {
      return 1;
    }

    if (b.state === RecommendationInfoStateEnum.Stale) {
      return -1;
    }

    if (!a.conclusion) {
      return -1;
    }

    if (!b.conclusion) {
      return 1;
    }

    return 0;
  });
};
