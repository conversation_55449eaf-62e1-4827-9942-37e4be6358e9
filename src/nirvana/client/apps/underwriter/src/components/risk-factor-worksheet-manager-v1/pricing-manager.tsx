import { Category, CoverageType } from '@nirvana/api/uw';
import clsx from 'clsx';
import { useSnackbar } from 'notistack';
import React, { useMemo } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import CreditInput from './credit-input';
import { updateWorksheetPricing } from './queries';
import { coverageTypeMap, CreditValues } from './types';
import { createDebouncedUpdate } from './utils';

type PricingDetail = {
  coverage: CoverageType;
  credit: number;
};

type PricingDetails = {
  category: Category;
  coverage_pricing_detail: PricingDetail[];
};

type WorksheetData = {
  id: string;
  pricing_details: PricingDetails[];
};

type ApiError = {
  response?: {
    data?: {
      message?: string;
    };
  };
};

type MutationContext = {
  previousData: WorksheetData | null;
};

type CreditInputGroupProps = {
  category: Category;
  creditValues: CreditValues;
  onChange: (key: keyof CreditValues, value: number | null) => void;
  isLoading: boolean;
  localPricingState: Record<string, PricingDetail[]>;
  isDisabled: boolean;
};

const CreditInputGroup: React.FC<CreditInputGroupProps> = ({
  category,
  creditValues,
  onChange,
  isLoading,
  localPricingState,
  isDisabled,
}) => {
  const currentValues = useMemo(
    () =>
      localPricingState[category]
        ? (Object.fromEntries(
            localPricingState[category].map((detail) => [
              coverageTypeMap[detail.coverage],
              detail.credit,
            ]),
          ) as CreditValues)
        : creditValues,
    [category, creditValues, localPricingState],
  );

  return (
    <div className="grid grid-cols-3 gap-0">
      {(Object.values(coverageTypeMap) as (keyof CreditValues)[]).map(
        (key, index) => (
          <div
            key={key}
            className={clsx(
              'flex items-center border border-gray-200',
              index === 0 && 'border-r-0 rounded-l',
              index === 2 && 'border-l-0 rounded-r',
            )}
          >
            <span className="px-2 text-xs text-text-hint">{key}:</span>
            <CreditInput
              position={index === 0 ? 'left' : index === 1 ? 'middle' : 'right'}
              value={currentValues[key]}
              onChange={(value) => onChange(key, value)}
              isLoading={isLoading}
              disabled={isDisabled}
            />
          </div>
        ),
      )}
    </div>
  );
};

type PricingManagerProps = {
  worksheetId: string;
  category: Category;
  creditValues: CreditValues;
  isEditable: boolean;
  localPricingState: Record<string, PricingDetail[]>;
  setLocalPricingState: (
    value: React.SetStateAction<Record<string, PricingDetail[]>>,
  ) => void;
};

const PricingManager: React.FC<PricingManagerProps> = ({
  worksheetId,
  category,
  creditValues,
  isEditable,
  localPricingState,
  setLocalPricingState,
}) => {
  const queryClient = useQueryClient();
  const { enqueueSnackbar } = useSnackbar();

  const { mutate: updatePricing, isLoading: isUpdatingPricing } = useMutation<
    unknown,
    ApiError,
    {
      worksheetID: string;
      body: { category: Category; coverage_pricing_detail: PricingDetail[] };
    },
    MutationContext
  >(updateWorksheetPricing, {
    onMutate: async ({ body }) => {
      await queryClient.cancelQueries(['fetch-worksheet-factors']);

      const previousData = queryClient.getQueryData<WorksheetData>([
        'fetch-worksheet-factors',
      ]);
      if (!previousData) {
        return { previousData: null };
      }

      queryClient.invalidateQueries(['fetch-worksheet-factors']);

      setLocalPricingState((prev) => ({
        ...prev,
        [`${body.category}`]: body.coverage_pricing_detail,
      }));

      return { previousData };
    },

    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          ['fetch-worksheet-factors'],
          context.previousData,
        );
      }

      setLocalPricingState((prev) => {
        const newState = { ...prev };
        delete newState[variables.body.category];
        return newState;
      });

      enqueueSnackbar(
        err?.response?.data?.message || 'Failed to update pricing',
        { variant: 'error' },
      );
    },

    onSettled: () => {
      queryClient.invalidateQueries(['fetch-worksheet-factors']);
      queryClient.invalidateQueries(['applications']);
    },
  });

  const debouncedUpdatePricing = useMemo(
    () =>
      createDebouncedUpdate(
        ({
          category,
          creditValues,
        }: {
          category: Category;
          creditValues: CreditValues;
        }) => {
          if (!worksheetId) {
            return;
          }

          const coveragePricingDetail = (
            Object.entries(coverageTypeMap) as [
              CoverageType,
              keyof CreditValues,
            ][]
          ).map(([coverageType, key]) => ({
            coverage: coverageType,
            credit: creditValues[key] || 0,
          }));

          updatePricing({
            worksheetID: worksheetId,
            body: {
              category,
              coverage_pricing_detail: coveragePricingDetail,
            },
          });
        },
        1000,
      ),
    [updatePricing, worksheetId],
  );

  return (
    <div className="w-[320px]">
      <CreditInputGroup
        category={category}
        creditValues={creditValues}
        onChange={(key, newValue) =>
          debouncedUpdatePricing({
            category,
            creditValues: {
              ...creditValues,
              [key]: newValue,
            },
          })
        }
        isLoading={isUpdatingPricing}
        localPricingState={localPricingState}
        isDisabled={!isEditable}
      />
    </div>
  );
};

export default PricingManager;
