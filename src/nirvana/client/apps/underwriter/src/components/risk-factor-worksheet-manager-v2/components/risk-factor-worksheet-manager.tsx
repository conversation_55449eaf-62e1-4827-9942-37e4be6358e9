import { CircularProgress, TextField } from '@material-ui/core';
import { CoverageType } from '@nirvana/api/uw';
import { Show, Switch, Toggle, useLightTheme } from '@nirvana/ui-kit';
import { startCase } from 'lodash-es';
import React, { useCallback, useEffect, useState } from 'react';
import 'react-resizable/css/styles.css';
import { useParams } from 'react-router-dom';
import LogoutIcon from 'src/assets/icons/logout.svg?react';
import { useRiskFactorDisabledState } from 'src/hooks/use-risk-factor-disabled-state';
import {
  CATEGORY_ORDER,
  RiskFactorWorksheetManagerProps,
  WorksheetRiskFactor,
} from '../constants/types';
import { useCreditUpdates } from '../hooks/useCreditUpdates';
import { useRiskFactorInputs } from '../hooks/useRiskFactorInputs';
import { useWorksheetData } from '../hooks/useWorksheetData';
import AddRiskFactor from './add-risk-factor';
import EditableRiskFactor from './editable-risk-factor';
import ResizablePanel from './resizable-panel';

interface CreditTotals {
  AL: number;
  APD: number;
  MTC: number;
}

/**
 * RiskFactorWorksheetManager Component
 *
 * Main component for managing risk factors and their pricing in the worksheet.
 * Uses custom hooks for improved organization and separation of concerns.
 */
const RiskFactorWorksheetManager = ({
  open,
  onClose,
}: RiskFactorWorksheetManagerProps) => {
  const { appReviewId = '' } = useParams();
  const isDisabled = useRiskFactorDisabledState();
  const [worksheetNotes, setWorksheetNotes] = useState('');
  const [worksheetId, setWorksheetId] = useState('');
  const theme = useLightTheme();

  const [categoryCommentsVisible, setCategoryCommentsVisible] = useState<
    Record<string, boolean>
  >(
    CATEGORY_ORDER.reduce(
      (acc, category) => ({ ...acc, [category]: true }),
      {},
    ),
  );

  const {
    worksheetData,
    isLoading,
    groupedFactors,
    handleUpdateWorksheetNotes,
  } = useWorksheetData(appReviewId);

  const actualWorksheetId = worksheetData?.id || appReviewId;

  const { handleCreditChange, applyPendingCreditUpdates, isCreditLoading } =
    useCreditUpdates(actualWorksheetId);

  useEffect(() => {
    if (worksheetData?.id) {
      setWorksheetId(worksheetData.id);
    }
  }, [worksheetData?.id]);

  const {
    editingState,
    handleInputChange,
    handleInputBlur,
    handleSentimentChange,
  } = useRiskFactorInputs(appReviewId);

  const toggleCategoryComments = (category: string) => {
    setCategoryCommentsVisible((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  useEffect(() => {
    if (worksheetData?.notes) {
      setWorksheetNotes(worksheetData.notes);
    }
  }, [worksheetData?.notes]);

  const handleNotesUpdate = useCallback(() => {
    if (worksheetData?.id) {
      handleUpdateWorksheetNotes(worksheetNotes);
    }
  }, [worksheetData?.id, worksheetNotes, handleUpdateWorksheetNotes]);

  const calculateCategoryTotals = useCallback(
    (factors: WorksheetRiskFactor[]): CreditTotals => {
      const totals: CreditTotals = { AL: 0, APD: 0, MTC: 0 };

      // Apply Pending updates to each factor before calculating totals
      factors?.forEach((factor: WorksheetRiskFactor) => {
        const updatedFactor = applyPendingCreditUpdates(factor);

        if (updatedFactor.credits) {
          updatedFactor.credits.forEach((credit) => {
            if (credit.coverage_type === CoverageType.CoverageAutoLiability) {
              totals.AL += credit.credit || 0;
            } else if (
              credit.coverage_type === CoverageType.CoverageAutoPhysicalDamage
            ) {
              totals.APD += credit.credit || 0;
            } else if (
              credit.coverage_type === CoverageType.CoverageMotorTruckCargo
            ) {
              totals.MTC += credit.credit || 0;
            }
          });
        }
      });

      return totals;
    },
    [applyPendingCreditUpdates],
  );

  if (!open) {
    return null;
  }

  return (
    <ResizablePanel onClose={onClose}>
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-gray-200">
        <div className="px-6 pt-4 pb-2">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-base font-bold text-text-primary">
                Risk Review Worksheet
              </h1>
              <p className="mt-1 text-sm text-text-hint">
                Support your quote or decline reasons by tagging the data in the
                application or adding data from external sources. Add all the
                factors that impacted your decision.
              </p>
            </div>
            <div className="flex">
              <button
                className="p-2 transition-colors rounded hover:bg-gray-100"
                onClick={onClose}
                aria-label="Close worksheet"
              >
                <LogoutIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Add Risk Factor */}
          <Show when={!isDisabled}>
            <div className="mb-4">
              <AddRiskFactor
                worksheetId={worksheetId}
                isDisabled={isDisabled}
              />
            </div>
          </Show>

          {/* Table Header */}
          <div className="grid grid-cols-[45%_15%_13%_13%_14%] p-0 text-sm rounded text-text-hint bg-primary-extraLight divide-x divide-gray-200">
            <div className="flex items-center py-3 pl-3 font-medium">
              Factors
            </div>
            <div className="flex items-center justify-center px-3 py-3 font-medium">
              Value
            </div>
            <div className="flex items-center justify-center py-3 font-medium">
              AL
            </div>
            <div className="flex items-center justify-center py-3 font-medium">
              APD
            </div>
            <div className="flex items-center justify-center py-3 font-medium">
              MTC
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6 pb-24">
          <Switch>
            <Switch.Match when={isLoading}>
              <div className="flex items-center justify-center h-32">
                <CircularProgress size={24} />
              </div>
            </Switch.Match>

            <Switch.Match when={!isLoading}>
              <>
                {CATEGORY_ORDER?.map((category) => {
                  const data = groupedFactors[category] || {
                    factors: [],
                    creditValues: { AL: null, APD: null, MTC: null },
                    isEditable: true,
                  };
                  const categoryFactors = (data.factors || [])?.map((factor) =>
                    applyPendingCreditUpdates(factor),
                  );
                  const totals = calculateCategoryTotals(categoryFactors);

                  return (
                    <div key={category} className="mb-6">
                      {/* Category Header with Horizontal Divider */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-text-primary">
                          {startCase(category.toLowerCase())}
                        </span>
                        {/* Show Comments Toggle for this category */}
                        <Show when={data.factors.length > 0}>
                          <div className="flex items-center">
                            <span className="mr-2 text-xs font-medium text-text-primary">
                              Show comments
                            </span>
                            <Toggle
                              checked={categoryCommentsVisible[category]}
                              onChange={() => toggleCategoryComments(category)}
                              size="small"
                              backgroundTrackColor={
                                theme.palette.secondary.light
                              }
                              activeTrackColor={theme.palette.primary.main}
                            />
                          </div>
                        </Show>
                      </div>
                      <div className="h-[1px] bg-gray-200 mt-2" />

                      {/* Risk Factors List */}
                      <div>
                        {data?.factors?.map((factor, idx) => {
                          const updatedFactor =
                            applyPendingCreditUpdates(factor);
                          return (
                            <React.Fragment key={factor.id}>
                              <Show when={idx > 0}>
                                <div className="h-px bg-gray-200" />
                              </Show>
                              <EditableRiskFactor
                                factor={updatedFactor}
                                onSentimentChange={handleSentimentChange}
                                onValueChange={(
                                  factorId: string,
                                  value: string,
                                ) =>
                                  handleInputChange(factorId, 'value', value)
                                }
                                onNotesChange={(
                                  factorId: string,
                                  notes: string,
                                ) =>
                                  handleInputChange(factorId, 'notes', notes)
                                }
                                onCreditChange={(
                                  factorId,
                                  coverageType,
                                  value,
                                  existingCredits,
                                ) =>
                                  handleCreditChange(
                                    factorId,
                                    coverageType,
                                    value,
                                    factor.risk_factor.category,
                                    existingCredits,
                                  )
                                }
                                onInputBlur={handleInputBlur}
                                editingState={editingState}
                                isDisabled={isDisabled}
                                showComments={categoryCommentsVisible[category]}
                                isCreditLoading={isCreditLoading}
                              />
                            </React.Fragment>
                          );
                        })}
                      </div>

                      {/* Category totals - only show when there are factors */}
                      <Show when={data.factors.length > 0}>
                        <div className="grid grid-cols-[45%_15%_13%_13%_14%] bg-gray-50 py-2 px-1 mt-3 border-y border-gray-200 divide-x divide-gray-200">
                          <div className="pr-4 text-sm font-medium text-right">
                            Total
                          </div>
                          <div className="px-2" />
                          <div className="text-sm font-medium text-center">
                            <span
                              className={`px-2 py-1 ${totals.AL > 0 ? 'text-green-600' : totals.AL < 0 ? 'text-red-600' : 'text-gray-600'}`}
                            >
                              {totals.AL > 0 ? '+' : ''}
                              {totals.AL}%
                            </span>
                          </div>
                          <div className="text-sm font-medium text-center">
                            <span
                              className={`px-2 py-1 ${totals.APD > 0 ? 'text-green-600' : totals.APD < 0 ? 'text-red-600' : 'text-gray-600'}`}
                            >
                              {totals.APD > 0 ? '+' : ''}
                              {totals.APD}%
                            </span>
                          </div>
                          <div className="text-sm font-medium text-center">
                            <span
                              className={`px-2 py-1 ${totals.MTC > 0 ? 'text-green-600' : totals.MTC < 0 ? 'text-red-600' : 'text-gray-600'}`}
                            >
                              {totals.MTC > 0 ? '+' : ''}
                              {totals.MTC}%
                            </span>
                          </div>
                        </div>
                      </Show>

                      {/* Empty state for categories with no factors */}
                      <Show when={data.factors.length === 0}>
                        <div className="py-1 text-xs text-gray-500 text-start">
                          No {category.toLowerCase()} factors added. Unpriced
                          factors must be added to override pricing.
                        </div>
                      </Show>
                    </div>
                  );
                })}

                {/* Other Comments Section */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-primary-main">
                      Other comments
                    </span>
                  </div>
                  <div className="h-[1px] bg-gray-200 mb-4" />
                  <div className="p-1">
                    <TextField
                      multiline
                      fullWidth
                      minRows={4}
                      value={worksheetNotes}
                      onChange={(e) => setWorksheetNotes(e.target.value)}
                      onBlur={handleNotesUpdate}
                      placeholder="Add any additional comments..."
                      InputProps={{
                        className: 'text-sm rounded-md',
                        classes: {
                          root: 'border border-gray-200 rounded-md hover:border-gray-300 focus-within:border-primary-main transition-colors',
                          focused: 'border-primary-main',
                        },
                      }}
                      disabled={isDisabled}
                    />
                  </div>
                </div>
              </>
            </Switch.Match>
          </Switch>
        </div>
      </div>
    </ResizablePanel>
  );
};

export default RiskFactorWorksheetManager;
