import { SyntheticEvent } from 'react';
import {
  Autocomplete,
  Avatar,
  Box,
  InputBase,
  styled,
} from '@material-ui/core';
import { ApplicationReviewUser } from '@nirvana/api/uw';
import { ITheme } from '@nirvana/ui-kit';

interface IUWAssign {
  options?: ApplicationReviewUser[];
  value?: ApplicationReviewUser;
  onChange: (value: string) => void;
  disabled?: boolean;
}

const AvatarAssignee = styled(Avatar)(({ theme }: { theme: ITheme }) => ({
  width: 28,
  height: 28,
  backgroundColor: theme.palette.grey[500],
  marginRight: theme.spacing(1),
  fontSize: 14,
  paddingTop: '3px',
}));

const UWAssign = ({
  options = [],
  value = { id: '', name: '', email: '' },
  onChange,
  disabled = false,
}: IUWAssign) => {
  const handleChange = (
    _: SyntheticEvent,
    selectedValue: ApplicationReviewUser | null,
  ) => {
    if (selectedValue) {
      onChange(selectedValue.id);
    }
  };

  return (
    <div
      className="w-full"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <Autocomplete
        disablePortal
        value={value}
        disabled={disabled}
        onChange={handleChange}
        clearIcon={false}
        options={options}
        getOptionSelected={(option, value) => option.id === value.id}
        getOptionLabel={(record) => {
          return record?.name;
        }}
        renderOption={(props, option) => {
          return (
            <Box
              component="li"
              sx={{ '& > img': { mr: 2, flexShrink: 0 } }}
              {...props}
            >
              <AvatarAssignee src={option.iconUrl} alt={option.name}>
                {option.name.substring(1, 0)}
              </AvatarAssignee>
              {option.name}
            </Box>
          );
        }}
        renderInput={(params) => {
          return (
            <InputBase
              {...params.InputProps}
              startAdornment={
                <AvatarAssignee src={value?.iconUrl} alt={value?.name}>
                  {value?.name.substring(1, 0)}
                </AvatarAssignee>
              }
              inputProps={params.inputProps}
              fullWidth
              disabled={disabled}
            />
          );
        }}
      />
    </div>
  );
};

export default UWAssign;
