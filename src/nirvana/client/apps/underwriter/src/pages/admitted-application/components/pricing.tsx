import {
  Alert,
  IconButton,
  List,
  ListItem,
  ListItemText,
} from '@material-ui/core';
import { CoverageType } from '@nirvana/api/nfuw';
import { Show, Switch, constants } from '@nirvana/ui-kit';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import {
  HiOutlineChevronDown,
  HiOutlineChevronUp,
  HiOutlineCurrencyDollar,
  HiOutlineRefresh,
} from 'react-icons/hi';
import { useParams } from 'react-router-dom';
import Accordion from 'src/components/accordion';
import { useNFApplicationSummaryContext } from 'src/hooks/use-nfApplication-summary';
import { useNFPricingContext } from 'src/hooks/use-nfPricing';
import formatNumber from 'src/utils/format-number';
import { isWithinLimits, normalizeCoverageKeys } from '../utils';

const { Coverages } = constants;

export default function Pricing() {
  const { appReviewId = '' } = useParams();
  const { isDisabled, appSummary: data } = useNFApplicationSummaryContext();
  const { packageInfo, refetchQuote, isFetching } = useNFPricingContext();
  const [isIndicationOpen, setIsIndicationOpen] = useState(false);

  const packageDetails =
    packageInfo?.updatedPackage ?? packageInfo?.originalPackage;
  const sortedCoverages = useMemo(() => {
    if (!packageDetails?.primaryCovs) {
      return undefined;
    }

    return Coverages.getSortedCoverages({
      coverages: packageDetails?.primaryCovs,
      getCoverageType: (record) => record.coverageType,
    });
  }, [packageDetails?.primaryCovs]);

  const percentChange = useMemo(() => {
    return {
      [CoverageType.CoverageAutoLiability]: packageInfo?.alPercentage ?? 0,
      [CoverageType.CoverageAutoPhysicalDamage]: packageInfo?.apdPercentage,
      [CoverageType.CoverageMotorTruckCargo]: packageInfo?.mtcPercentage,
      [CoverageType.CoverageGeneralLiability]: packageInfo?.glPercentage,
    };
  }, [
    packageInfo?.alPercentage,
    packageInfo?.apdPercentage,
    packageInfo?.mtcPercentage,
    packageInfo?.glPercentage,
  ]);
  const safetyPercentChange = packageInfo?.safetyCredits;

  const creditLimits = normalizeCoverageKeys(
    packageInfo?.aggregateCreditLimitsByCoverage,
  );

  const creditsPctMap = useMemo(() => {
    return (
      sortedCoverages?.reduce((acc, { coverageType }) => {
        const value = percentChange[coverageType as keyof typeof percentChange];

        acc[coverageType] =
          (value || 0) +
          (coverageType === CoverageType.CoverageGeneralLiability
            ? 0
            : safetyPercentChange || 0);

        return acc;
      }, {} as Record<CoverageType, number>) ??
      ({} as Record<CoverageType, number>)
    );
  }, [sortedCoverages, percentChange, safetyPercentChange]);

  const isAnyCreditLimitOutOfRange =
    Object.keys(creditsPctMap).some(
      (coverageType) =>
        creditsPctMap[coverageType as CoverageType] &&
        !isWithinLimits(
          creditsPctMap[coverageType as CoverageType],
          creditLimits?.[coverageType],
        ),
    ) ?? false;

  const applicationState = useMemo(() => {
    return data?.fmcsaAddress?.state;
  }, [data?.fmcsaAddress?.state]);

  return (
    <>
      <Accordion expanded>
        <Accordion.Summary>
          <div className="flex items-center space-x-3">
            <div className="p-1 text-lg text-success-main bg-success-extraLight">
              <HiOutlineCurrencyDollar />
            </div>
            <span className="font-semibold">Pricing</span>
          </div>
        </Accordion.Summary>

        <Accordion.Details>
          <div className="flex items-center justify-between mb-4">
            <p className="font-semibold">TOTAL</p>
            <IconButton
              size="small"
              onClick={() => refetchQuote({ appReviewId })}
              className={clsx(isFetching ? 'animate-spin' : 'animate-none')}
              disabled={isDisabled}
            >
              <HiOutlineRefresh />
            </IconButton>
          </div>

          <div className="flex items-center justify-between mb-4">
            <p className="text-text-hint">Premium</p>
            <Switch>
              <Switch.Match when={packageInfo?.updatedPackage?.totalPremium}>
                {(value) => (
                  <>
                    <p className="flex items-center text-xl font-semibold">
                      <span>${formatNumber(value)}</span>
                    </p>
                  </>
                )}
              </Switch.Match>

              <Switch.Match when={packageInfo?.originalPackage?.totalPremium}>
                {(value) => (
                  <p className="text-xl font-semibold">
                    ${formatNumber(value)}
                  </p>
                )}
              </Switch.Match>
            </Switch>
          </div>

          <List
            className="bg-white shadow-md rounded-t-xl rounded-b-xl"
            style={{ borderBottom: '1px solid #D7D8DB' }}
          >
            <Show when={sortedCoverages}>
              {(primaryCovs) =>
                primaryCovs.map(
                  ({ label, premium, coverageType, premiumPerUnit }, index) => {
                    const isLastItem = index === primaryCovs.length - 1;
                    const creditValue = creditsPctMap?.[coverageType] ?? 0;
                    const isCreditWithinLimits = isWithinLimits(
                      creditValue,
                      creditLimits?.[coverageType],
                    );

                    return (
                      <ListItem
                        key={label}
                        className={clsx('border-b border-text-disabled', {
                          'border-error-tint3': !isCreditWithinLimits,
                          'border-b-0': isLastItem,
                          'bg-error-light': !isCreditWithinLimits,
                        })}
                      >
                        <ListItemText>
                          <div className="flex items-start w-full gap-2">
                            {/* Label */}
                            <p className="font-semibold break-words basis-1/2">
                              {label}
                            </p>

                            {/* Percent change */}
                            <Show
                              when={
                                percentChange[
                                  coverageType as keyof typeof percentChange
                                ]
                              }
                            >
                              {() => {
                                return (
                                  <span
                                    className={clsx(
                                      'flex-1 text-right font-semibold',
                                      {
                                        'text-success-main': creditValue > 0,
                                        'text-error-main': creditValue < 0,
                                        'text-text-primary': creditValue === 0,
                                      },
                                    )}
                                  >
                                    {creditValue}%
                                  </span>
                                );
                              }}
                            </Show>

                            {/* Premium */}
                            <div className="text-right basis-1/3">
                              <p className="font-semibold">
                                ${formatNumber(premium ?? 0)}
                              </p>

                              {label !== 'General Liability' && (
                                <p className="float-right text-text-hint">
                                  {label === 'Auto Physical Damage' &&
                                  packageInfo?.updatedPackage?.TIVPercentage
                                    ? `${packageInfo?.updatedPackage?.TIVPercentage}% / TIV`
                                    : `$${formatNumber(
                                        premiumPerUnit ?? 0,
                                      )} / unit`}
                                </p>
                              )}
                            </div>
                          </div>
                        </ListItemText>
                      </ListItem>
                    );
                  },
                )
              }
            </Show>
          </List>

          {isAnyCreditLimitOutOfRange ? (
            <Alert
              severity="error"
              color="error"
              variant="outlined"
              className="mt-4 rounded-lg text-error-main bg-error-light border-error-tint3"
            >
              <p>
                {applicationState} credit limit is{' '}
                {creditLimits?.[CoverageType.CoverageAutoLiability]?.maximum}%
                and debit limit is{' '}
                {creditLimits?.[CoverageType.CoverageAutoLiability]?.minimum}%
              </p>
            </Alert>
          ) : (
            <p className="mt-4 text-text-hint">
              {applicationState} credit limit is{' '}
              {creditLimits?.[CoverageType.CoverageAutoLiability]?.maximum}% and
              debit limit is{' '}
              {creditLimits?.[CoverageType.CoverageAutoLiability]?.minimum}%
            </p>
          )}

          <div
            className="flex items-center gap-2 mb-3 cursor-pointer"
            onClick={() => setIsIndicationOpen(!isIndicationOpen)}
          >
            <p className="mt-4 font-bold text-primary-main">Indication</p>
            {isIndicationOpen ? (
              <HiOutlineChevronUp className="mt-4 text-primary-main" />
            ) : (
              <HiOutlineChevronDown className="mt-4 text-primary-main" />
            )}
          </div>
          {isIndicationOpen && (
            <List
              className="bg-white rounded-xl"
              style={{ border: '1px solid #D7D8DB', padding: '0' }}
            >
              <ListItem>
                <ListItemText>
                  <div className="flex justify-between text-sm">
                    <p className="font-semibold">Total</p>
                    <p className="text-text-hint">
                      $
                      {formatNumber(
                        packageInfo?.originalPackage?.totalPremium ?? 0,
                      )}
                    </p>
                  </div>

                  <hr className="mt-2" />
                  <Show when={sortedCoverages}>
                    {() =>
                      packageInfo?.originalPackage?.primaryCovs.map(
                        ({ label, premium }, index) => {
                          return (
                            <>
                              <div
                                key={label}
                                className={clsx('pt-2 pb-0', {
                                  'pb-0':
                                    index ===
                                    (packageInfo?.originalPackage?.primaryCovs
                                      ?.length ?? 0) -
                                      1,
                                })}
                              >
                                <div className="flex justify-between text-sm">
                                  <p>{label}</p>
                                  <p className="text-text-hint">
                                    ${formatNumber(premium ?? 0)}
                                  </p>
                                </div>
                              </div>
                            </>
                          );
                        },
                      )
                    }
                  </Show>
                </ListItemText>
              </ListItem>
            </List>
          )}
        </Accordion.Details>
      </Accordion>
    </>
  );
}
