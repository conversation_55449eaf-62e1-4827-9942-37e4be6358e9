import { CoverageType } from '@nirvana/api/uw';

export const alAncilliaryCoverages = [
  { coverageType: CoverageType.CoveragePersonalInjuryProtection, label: 'PIP' },
  {
    coverageType: CoverageType.CoveragePersonalInjuryProtectionBasic,
    label: 'PIP Basic',
  },
  {
    coverageType: CoverageType.CoveragePersonalInjuryProtectionIncreased,
    label: 'PIP Increased',
  },
  {
    coverageType: CoverageType.CoveragePipExcessAttendantCare,
    label: 'PIP Excess Attendant Care',
  },
  { coverageType: CoverageType.CoverageUm, label: 'UM' },
  { coverageType: CoverageType.CoverageUim, label: 'UIM' },

  { coverageType: CoverageType.CoverageUmuim, label: 'UMUIM' },
  { coverageType: CoverageType.CoverageUmbiuimbi, label: 'UMBIUIMBI' },
  { coverageType: CoverageType.CoverageUmuimBodilyInjury, label: 'UMUIM<PERSON>' },
  {
    coverageType: CoverageType.CoverageUninsuredMotoristBodilyInjury,
    label: 'UMBI',
  },
  {
    coverageType: CoverageType.CoverageUnderinsuredMotoristBodilyInjury,
    label: 'UIMBI',
  },
  { coverageType: CoverageType.CoverageUmuimPropertyDamage, label: 'UMUIMPD' },
  {
    coverageType: CoverageType.CoverageUninsuredMotoristPropertyDamage,
    label: 'UMPD',
  },
  {
    coverageType: CoverageType.CoverageUnderinsuredMotoristPropertyDamage,
    label: 'UIMPD',
  },
  {
    coverageType: CoverageType.CoveragePropertyProtectionInsurance,
    label: 'PPI',
  },
  { coverageType: CoverageType.CoverageMedicalPayments, label: 'MedPay' },
  {
    coverageType: CoverageType.CoverageBroadenedPollution,
    label: 'Broaden Pollution',
  },
  {
    coverageType: CoverageType.CoverageBlanketAdditional,
    label: 'Blanket Additional Insured',
  },
  {
    coverageType: CoverageType.CoverageBlanketWaiverOfSubrogation,
    label: 'Blanket Waiver of Subrogation',
  },
  { coverageType: CoverageType.CoverageTerrorism, label: 'Terrorism' },
  {
    coverageType: CoverageType.CoverageUnattendedTruck,
    label: 'Unattended Truck',
  },
  {
    coverageType: CoverageType.CoverageHiredAuto,
    label: 'Hired Auto Liability',
  },
];

export const apdAncillaryCoverages = [
  {
    coverageType: CoverageType.CoverageTrailerInterchange,
    label: 'Trailer Interchange',
  },
  {
    coverageType: CoverageType.CoverageTowingLaborAndStorage,
    label: 'Towing, labor & storage',
  },
  {
    coverageType: CoverageType.CoverageRentalReimbursement,
    label: 'Rental Reimbursement',
  },
  { coverageType: CoverageType.CoverageUiia, label: 'UIIA' },
  { coverageType: CoverageType.CoverageApduiia, label: 'UIIA' },
  {
    coverageType: CoverageType.CoverageApdTrailerInterchange,
    label: 'Trailer Interchange',
  },
  {
    coverageType: CoverageType.CoverageApdNonOwnedTrailer,
    label: 'Non Owned Trailer',
  },
  {
    coverageType: CoverageType.CoverageNonOwnedTrailer,
    label: 'Non Owned Trailer',
  },
];

export const mtcAncillaryCoverages = [
  { coverageType: CoverageType.CoverageReefer, label: 'Reefer Breakdown' },
  {
    coverageType: CoverageType.CoverageReeferWithHumanError,
    label: 'Reefer Breakdown with Human Error',
  },
  { coverageType: CoverageType.CoverageDebrisRemoval, label: 'Debris Removal' },
  {
    coverageType: CoverageType.CoverageMtcBlanketWaiverOfSubrogation,
    label: 'Blanket Waiver Of Subrogation',
  },
  {
    coverageType: CoverageType.CoverageMtcBlanketAdditional,
    label: 'Blanket Additional',
  },
  { coverageType: CoverageType.CoverageMtcuiia, label: 'UIIA' },
  {
    coverageType: CoverageType.CoverageMtcTrailerInterchange,
    label: 'Trailer Interchange',
  },
  {
    coverageType: CoverageType.CoverageMtcNonOwnedTrailer,
    label: 'Non Owned Trailer',
  },
  { coverageType: CoverageType.CoverageEarnedFreight, label: 'Earned Freight' },
  {
    coverageType: CoverageType.CoverageCargoAtScheduledTerminals,
    label: 'Cargo at Terminals',
  },
];
