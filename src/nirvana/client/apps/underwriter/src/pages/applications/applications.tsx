import { useLocation } from 'react-router-dom';
import { flatMap, keyBy, mapValues } from 'lodash-es';
import SearchIcon from '@material-ui/icons/Search';
import { useEffect, useMemo, useState } from 'react';
import { useInfiniteQuery, useQuery } from 'react-query';
import { InputAdornment, OutlinedInput } from '@material-ui/core';

import { ApplicationReviewTab } from '@nirvana/api/uw';
import { Switch } from '@nirvana/ui-kit';
import { getFormattedDate } from '@nirvana/core/utils';

import ListLayout from 'src/components/list-layout';
import useLocalStorage from 'src/hooks/use-local-storage';
import { APPLICATIONS_LIST_FILTERS_STORAGE_KEY } from 'src/constants/storage';

import FleetTable from '../fleet-application-list/components/fleet-table';
import Filters from './components/filters';
import { getInitialFilters } from './utils';
import NonFleetTable from './components/non-fleet-table';
import { fetchApplicationCount, fetchPaginatedReviews } from './queries';

const PAGE_SIZE = 50;
const getPageSize = () => PAGE_SIZE;

export default function Applications() {
  const [searchText, setSearchText] = useState('');
  const [needsAttentionFlag, setNeedsAttentionFlag] = useState(false);

  const location = useLocation();
  const insuredType = location.pathname.includes('non-fleet')
    ? 'NON_FLEET'
    : 'FLEET';

  const [indexedCountState, setIndexedCountState] = useState<
    Record<string, number>
  >({});

  const [activeTab, setActiveTab] = useState(
    ApplicationReviewTab.ApplicationReviewTabReadyForReview,
  );

  const [
    {
      selectedUnderwriter,
      selectedEffectiveDateRange,
      selectedRecommendedAction,
    },
    setFilters,
  ] = useLocalStorage(
    APPLICATIONS_LIST_FILTERS_STORAGE_KEY,
    getInitialFilters(),
  );

  const applicationFilters = () => {
    const start = selectedEffectiveDateRange?.start;
    const end = selectedEffectiveDateRange?.end;

    return {
      effectiveDateRange: {
        start: start ? getFormattedDate(start, 'yyyy-MM-dd') : undefined,
        end: end ? getFormattedDate(end, 'yyyy-MM-dd') : undefined,
      },
      underwriterId: selectedUnderwriter,
      recommendedActions: selectedRecommendedAction,
    };
  };

  const filters = {
    tab: activeTab,
    q: searchText,
    pageSize: getPageSize(),
    ...applicationFilters(),
  };

  const countFilter = { q: searchText, ...applicationFilters() };

  const {
    data: paginatedData,
    hasNextPage,
    fetchNextPage,
    isLoading,
  } = useInfiniteQuery(
    ['applications-v2', filters],
    ({ pageParam, queryKey }) => {
      return fetchPaginatedReviews({
        cursor: pageParam,
        ...(typeof queryKey[1] === 'object' ? queryKey[1] : {}),
      });
    },
    { getNextPageParam: (lastPage) => lastPage.cursor },
  );

  const { data: countData, isLoading: isCountLoading } = useQuery(
    ['application-count', countFilter],
    () => fetchApplicationCount(countFilter),
  );

  const list = useMemo(() => {
    return flatMap(
      paginatedData?.pages || [],
      (page) => page?.appReviews || [],
    );
  }, [paginatedData?.pages]);

  // index countData by tab
  useEffect(() => {
    const indexedCountState = keyBy(countData?.counts || [], 'tab');

    // convert indexedCountState to form <ApplicationReviewTab, number>
    const indexedCountStateByTab = mapValues(indexedCountState, (value) => {
      return value.count;
    });
    setNeedsAttentionFlag((countData?.needsAttentionCount || 0) > 0);
    setIndexedCountState(indexedCountStateByTab);
  }, [countData]);

  return (
    <ListLayout title="Applications">
      <div className="flex items-center mb-4 space-x-4">
        <OutlinedInput
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="w-60"
          placeholder="Search..."
          endAdornment={
            <InputAdornment position="end">
              <SearchIcon color="primary" />
            </InputAdornment>
          }
        />

        <div className="flex-1" />
        <Filters
          setFilters={setFilters}
          selectedUnderwriter={selectedUnderwriter}
          selectedRecommendedAction={selectedRecommendedAction}
          selectedEffectiveDateRange={selectedEffectiveDateRange}
        />
      </div>

      <div className="flex flex-col flex-1 w-full px-6 py-4 overflow-hidden bg-white border shadow-md rounded-xl">
        <Switch>
          <Switch.Match when={insuredType === 'FLEET'}>
            <FleetTable
              list={list}
              isLoading={isLoading}
              activeTab={activeTab}
              hasNextPage={hasNextPage}
              onTabChange={setActiveTab}
              fetchNextPage={fetchNextPage}
              isCountLoading={isCountLoading}
              indexedCountState={indexedCountState}
              needsAttentionFlag={needsAttentionFlag}
            />
          </Switch.Match>
          <Switch.Match when={insuredType === 'NON_FLEET'}>
            <NonFleetTable
              searchText={searchText}
              applicationFilters={applicationFilters()}
            />
          </Switch.Match>
        </Switch>
      </div>
    </ListLayout>
  );
}
