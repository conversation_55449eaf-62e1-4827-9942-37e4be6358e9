import { Show, Table } from '@nirvana/ui-kit';
import { getFormattedDate } from '@nirvana/core/utils';
import {
  DriverViolation,
  DriverMVRDetails,
  MVRPullStatus,
} from '@nirvana/api/endorsements/';
import { Column } from 'react-table';

const violationColumns: Array<Column<DriverViolation>> = [
  { accessor: 'code', Header: 'Code', disableSortBy: true },
  { accessor: 'description', Header: 'Type', disableSortBy: true },
  {
    accessor: 'date',
    Header: 'Date',
    disableSortBy: true,
    Cell: ({ value }) => (value ? getFormattedDate(value) : 'N/A'),
  },
  { accessor: 'points', Header: 'Points', disableSortBy: true },
];

export default function MvrDetails(row: Partial<DriverMVRDetails>) {
  return (
    <div className="p-4 border rounded-md bg-secondary-tint2">
      <div className="max-w-4xl p-4 mx-auto my-6 bg-white rounded shadow">
        <Show
          when={row.mvrPullStatus === MVRPullStatus.Success}
          fallback={
            <p className="py-8 text-center text-text-hint">
              MVR data is not available
            </p>
          }
        >
          <p className="mb-4 font-semibold text-text-primary">MVR Details</p>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="p-3 border rounded border-primary-extraLight">
              <p className="mb-1 text-text-hint">Issue Date</p>
              <p className="text-base font-semibold">
                {row.issueDate ? getFormattedDate(row.issueDate) : 'N/A'}
              </p>
            </div>

            <div className="p-3 border rounded border-primary-extraLight">
              <p className="mb-1 text-text-hint">Expiration Date</p>
              <p className="text-base font-semibold">
                {row.expirationDate
                  ? getFormattedDate(row.expirationDate)
                  : 'N/A'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="p-3 border rounded border-primary-extraLight">
              <p className="mb-1 text-text-hint">License Status</p>
              <p className="text-base font-semibold">{row.cdlStatus}</p>
            </div>

            <div className="p-3 border rounded border-primary-extraLight">
              <p className="mb-1 text-text-hint">License Class</p>
              <p className="text-base font-semibold">{row.cdlClass}</p>
            </div>
          </div>

          <p className="mb-4 font-semibold text-text-primary">Violations</p>

          <Table columns={violationColumns} data={row.violations ?? []} />

          <hr className="my-4 -mx-4 border border-primary-extraLight" />
        </Show>
      </div>
    </div>
  );
}
