import { <PERSON><PERSON>, IconButton, Input<PERSON><PERSON>l, TextField } from '@material-ui/core';
import {
  EndorsementReview,
  EndorsementReviewState,
  PatchEndorsementReviewForm,
  PricingType,
} from '@nirvana/api/endorsements';
import { InputNumeric, Show, Toggle } from '@nirvana/ui-kit';
import { light } from '@nirvana/ui-kit/src/assets/themes/light';
import clsx from 'clsx';
import { useFormik } from 'formik';
import { useState } from 'react';
import { HiRefresh } from 'react-icons/hi';
import { useMutation, useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';
import formatNumber from 'src/utils/format-number';
import * as Yup from 'yup';
import { refreshPrice } from '../queries';
import { formatNumber as parseNumber } from '../utils';

type RatemlProps = {
  data: EndorsementReview | undefined;
  onSubmit: ({
    endorsementReviewId,
    payload,
  }: {
    endorsementReviewId: string;
    payload: PatchEndorsementReviewForm;
  }) => void;
};

export default function Rateml({ data, onSubmit }: RatemlProps) {
  const { endorsementReviewId = '' } = useParams();

  const { values, handleChange, handleSubmit, isValid, dirty } = useFormik({
    initialValues: {
      overriddenPrice: data?.overriddenPrice,
      overriddenPriceDetails: data?.overriddenPriceDetails,
    },
    validationSchema: Yup.object().shape({
      overriddenPrice: Yup.number().required('Required'),
      overriddenPriceDetails: Yup.string().required('Required'),
    }),
    onSubmit: ({ overriddenPrice, overriddenPriceDetails }) => {
      onSubmit({
        endorsementReviewId,
        payload: {
          pricingDetails: {
            isRateMLOutputOverridden: true,
            overriddenPrice: Number(overriddenPrice),
            overriddenPriceDetails,
            additionalFee: data?.additionalFee,
          },
        },
      });
    },
  });

  const queryClient = useQueryClient();
  const { mutate } = useMutation(refreshPrice, {
    onSuccess: () =>
      queryClient.invalidateQueries(['endorsement', endorsementReviewId]),
  });

  const [adjustManually, setAdjustManually] = useState(
    !!data?.isRateMLOutputOverridden,
  );

  const {
    additionalFee,
    approvable,
    rateMLOutput,
    isRateMLOutputOverridden,
    pricingType,
  } = data ?? {};

  const finalValue = (rateMLOutput ?? 0) + (additionalFee ?? 0);

  const premiumByCoverage = [];
  if (data?.alApdRateMLOutput?.al !== undefined) {
    premiumByCoverage.push({
      title: 'AL Premium',
      premium: data.alApdRateMLOutput.al,
    });
  }
  if (data?.alApdRateMLOutput?.apd !== undefined) {
    premiumByCoverage.push({
      title: 'APD Premium',
      premium: data.alApdRateMLOutput.apd,
    });
  }
  if (data?.alApdRateMLOutput?.stateSurcharge !== undefined) {
    premiumByCoverage.push({
      title: 'State Surcharge',
      premium: data.alApdRateMLOutput.stateSurcharge,
    });
  }

  return (
    <>
      <div className="w-full p-4 mb-8 rounded bg-primary-extraLight">
        <p className="font-bold text-center text-text-hint">
          <span>Rate ML</span>
          <Show
            when={additionalFee && pricingType === PricingType.PricingTypeMixed}
          >
            {' '}
            + additional
          </Show>
        </p>
        <Show
          when={
            data?.state !==
            EndorsementReviewState.EndorsementReviewStateRefreshingPrice
          }
          fallback={
            <div className="flex justify-center">
              <div className="w-20 h-8 my-2 bg-gray-200 rounded animate-pulse" />
            </div>
          }
        >
          <div className="flex flex-col items-center justify-center space-y-2">
            <div className="flex items-center justify-center space-x-2">
              <p
                className={clsx('text-center text-[28px] font-bold', {
                  'text-success-main -ml-5': finalValue > 0,
                  'text-error-main -ml-5': finalValue < 0,
                  'text-text-primary': finalValue === 0,
                  'line-through text-text-hint': isRateMLOutputOverridden,
                })}
              >
                <Show
                  when={finalValue > 0}
                  fallback={
                    <>
                      {finalValue !== 0 && (
                        <span className="font-normal">-</span>
                      )}{' '}
                      <span>${Math.abs(finalValue)}</span>
                    </>
                  }
                >
                  <span className="font-normal">+</span>{' '}
                  <span>${finalValue}</span>
                </Show>
              </p>
              <div
                className={clsx({
                  'cursor-not-allowed opacity-50': !approvable,
                })}
              >
                <IconButton
                  disabled={!approvable}
                  size="small"
                  onClick={() => mutate({ endorsementReviewId })}
                >
                  <HiRefresh className="text-primary-main" />
                </IconButton>
              </div>
            </div>
            <ul className="w-full px-2 space-y-2">
              {premiumByCoverage.map(({ title, premium }) => (
                <li key={title} className="flex items-center justify-between">
                  <p className="font-normal text-text-hint">{title}</p>
                  <Show
                    when={premium}
                    fallback={<p className="text-secondary-dark">-</p>}
                  >
                    {(premium) => (
                      <p className="font-semibold text-text-primary">
                        ${formatNumber(premium)}
                      </p>
                    )}
                  </Show>
                </li>
              ))}
            </ul>
          </div>
        </Show>
        <Show
          when={!adjustManually && pricingType === PricingType.PricingTypeMixed}
        >
          <p className="py-3 text-xs leading-5 tracking-tight text-text-hint">
            There are rated and unrated changes in this request. Enter an amount
            if you wish to add on adjustments for unrated changes.
          </p>
          <InputNumeric
            prefix="$"
            fullWidth
            allowNegative
            className="mt-3"
            value={additionalFee}
            disabled={!approvable}
            placeholder="Enter amount"
            onBlur={(e) => {
              onSubmit({
                endorsementReviewId,
                payload: {
                  pricingDetails: {
                    isRateMLOutputOverridden: false,
                    additionalFee: parseNumber(e.target.value),
                    overriddenPrice: data?.overriddenPrice,
                    overriddenPriceDetails: data?.overriddenPriceDetails,
                  },
                },
              });
            }}
          />
        </Show>
      </div>

      <div className="flex items-center justify-between w-full">
        <p className="text-text-primary">Adjust Manually</p>
        <Toggle
          disabled={!approvable}
          checked={adjustManually}
          backgroundTrackColor={light.palette.primary.track}
          onChange={(_, checked) => {
            setAdjustManually(checked);
            onSubmit({
              endorsementReviewId,
              payload: {
                pricingDetails: {
                  isRateMLOutputOverridden: checked,
                  additionalFee: data?.additionalFee,
                  overriddenPrice: data?.overriddenPrice,
                  overriddenPriceDetails: data?.overriddenPriceDetails,
                },
              },
            });
          }}
        />
      </div>

      <Show when={adjustManually}>
        <form className="w-full" onSubmit={handleSubmit}>
          <InputNumeric
            prefix="$"
            fullWidth
            allowNegative
            className="my-3"
            name="overriddenPrice"
            disabled={!approvable}
            onChange={handleChange}
            placeholder="Enter amount"
            value={values.overriddenPrice}
          />
          <InputLabel className="mb-0.5 text-xs font-normal text-primary-main transform-none">
            Details
          </InputLabel>
          <TextField
            rows={3}
            fullWidth
            multiline
            className="mb-3"
            disabled={!approvable}
            onChange={handleChange}
            name="overriddenPriceDetails"
            value={values.overriddenPriceDetails}
            placeholder="Enter reasons for manual adjustment"
          />
          <div className="flex justify-end">
            <Button
              type="submit"
              variant="contained"
              disabled={!(isValid && dirty)}
            >
              Save
            </Button>
          </div>
        </form>
      </Show>
    </>
  );
}
