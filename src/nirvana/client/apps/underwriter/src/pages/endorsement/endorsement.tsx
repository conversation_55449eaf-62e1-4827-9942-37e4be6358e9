import { useEffect } from 'react';
import { range } from 'lodash-es';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { Skeleton } from '@material-ui/core';
import { useLDClient } from 'launchdarkly-react-client-sdk';

import { Switch } from '@nirvana/ui-kit';
import {
  ChangeType,
  DeductibleChange,
  LimitChange,
  CoverageAddition as ECoverageAddition,
  CoverageRemoval as ECoverageRemoval,
  PolicyHolderInsuredDetailsUpdate,
  InsuredChange,
  NamedShipperLimitUpdate,
  OneTimeHaulLimitUpdate,
  NonFleetAdmittedScheduleChange,
  EndorsementReviewState,
  FormsModification,
  PolicyCancellation,
  PolicyReinstatement,
  SpecialRequest as ESpecialRequest,
} from '@nirvana/api/endorsements';

import Navbar from 'src/components/navbar';
import Spotlight from 'src/components/spotlight';
import useAuthContext from 'src/hooks/use-auth-context';
import { fetchEndorsementReviewById } from 'src/pages/endorsement/queries';
import Header from './components/header';
import LimitUpdate from './components/limit-update';
import FlatCharges from './components/flat-charges';
import DeductibleUpdate from './components/deductible-update';
import CoverageAddition from './components/coverage-addition';
import CoverageRemoval from './components/coverage-removal';
import InsuredUpdate from './components/insured-update';
import NamedShipperLimit from './components/named-shipper-limit';
import OneTimeHaulLimit from './components/one-time-haul';
import NonFleetAdmittedSchedule from './components/nonfleet-schedule';
import FormsModifications from './components/forms-modification';
import PolicyReinstatementCancellationDetails from './components/policy-reinstatement-cancellation';
import InsuredDetailsUpdate from './components/policy-holder-update';
import SpecialRequest from './components/special-request';

export default function Endorsement() {
  const client = useLDClient();
  const { user } = useAuthContext();
  const { endorsementReviewId = '' } = useParams();
  const { data, isLoading } = useQuery(
    ['endorsement', endorsementReviewId],
    () => fetchEndorsementReviewById(endorsementReviewId),
    {
      refetchInterval: (data) =>
        data?.state ===
        EndorsementReviewState.EndorsementReviewStateRefreshingPrice
          ? 5 * 1000
          : false,
    },
  );

  useEffect(() => {
    if (data && user) {
      client?.identify({
        kind: 'multi',
        endorsement: { key: data.id, programType: data.programType },
        user: {
          key: user.id,
          email: user.email,
          name: user.name,
          agencyId: user.defaultAgencyId ?? '',
        },
      });
    }
  }, [client, data, user]);

  return (
    <div className="flex flex-col w-screen h-screen">
      <Navbar />
      <Header
        policyType={data?.policyType}
        approvable={data?.approvable}
        programType={data?.programType}
        appReviewId={data?.applicationReviewID}
        policyChangeFormHandleId={data?.policyChangeForm?.handleID}
      />
      <Spotlight />
      <div className="flex items-start gap-4 px-8 pt-10 pb-4">
        <div className="flex-1 space-y-6">
          <Switch>
            <Switch.Match when={isLoading}>
              {range(5).map((val) => (
                <div
                  key={val}
                  className="p-4 bg-white border rounded-lg shadow-md border-primary-extraLight"
                >
                  <Skeleton width={128} />
                  <Skeleton width={320} />
                </div>
              ))}
            </Switch.Match>

            <Switch.Match when={data?.data?.changeReviews}>
              {(reviews) =>
                reviews.map(({ change }, index) => {
                  return (
                    <Switch key={`${index}-${change?.changeType}`}>
                      <Switch.Match
                        when={
                          change?.changeType ===
                            ChangeType.ChangeTypeAncillaryCoverageLimitUpdate ||
                          change?.changeType ===
                            ChangeType.ChangeTypePrimaryCoverageLimitUpdate
                        }
                      >
                        <LimitUpdate data={change?.data as LimitChange} />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                            ChangeType.ChangeTypePrimaryCoverageDeductibleUpdate ||
                          change?.changeType ===
                            ChangeType.ChangeTypeAncillaryCoverageDeductibleUpdate
                        }
                      >
                        <DeductibleUpdate
                          data={change?.data as DeductibleChange}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeAncillaryCoverageAddition
                        }
                      >
                        <CoverageAddition
                          data={change?.data as ECoverageAddition}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeAncillaryCoverageRemoval
                        }
                      >
                        <CoverageRemoval
                          data={change?.data as ECoverageRemoval}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypePolicyHolderInsuredDetailsUpdate
                        }
                      >
                        <InsuredDetailsUpdate
                          data={
                            change?.data as PolicyHolderInsuredDetailsUpdate
                          }
                        />
                      </Switch.Match>

                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeInsuredUpdate
                        }
                      >
                        <InsuredUpdate data={change?.data as InsuredChange} />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeNamedShipperLimitUpdate
                        }
                      >
                        <NamedShipperLimit
                          data={change?.data as NamedShipperLimitUpdate}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeOneTimeHaulLimitUpdate
                        }
                      >
                        <OneTimeHaulLimit
                          data={change?.data as OneTimeHaulLimitUpdate}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeNonFleetAdmittedScheduleChange
                        }
                      >
                        <NonFleetAdmittedSchedule
                          data={change?.data as NonFleetAdmittedScheduleChange}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeFormsModification
                        }
                      >
                        <FormsModifications
                          data={change?.data as FormsModification}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypePolicyCancellation
                        }
                      >
                        <PolicyReinstatementCancellationDetails
                          headerTitle="Policy Cancellation"
                          data={change?.data as PolicyCancellation}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypePolicyReinstatement
                        }
                      >
                        <PolicyReinstatementCancellationDetails
                          headerTitle="Policy Reinstated"
                          data={change?.data as PolicyReinstatement}
                        />
                      </Switch.Match>
                      <Switch.Match
                        when={
                          change?.changeType ===
                          ChangeType.ChangeTypeSpecialRequest
                        }
                      >
                        <SpecialRequest
                          data={change?.data as ESpecialRequest}
                        />
                      </Switch.Match>
                    </Switch>
                  );
                })
              }
            </Switch.Match>
          </Switch>
        </div>

        <FlatCharges data={data} isLoading={isLoading} />
      </div>
    </div>
  );
}
