import { Configuration, UnderwritingApi } from '@nirvana/api/nfuw';
import { cookieStorage } from '@nirvana/core/utils';
import axios from 'axios';

const configOptions = new Configuration();
const nfService = new UnderwritingApi(configOptions);

axios.interceptors.request.use((config) => {
  config.headers.JSESSIONID = cookieStorage.get({
    key: import.meta.env.VITE_AUTH_TOKEN,
  });
  return config;
});

export default nfService;
