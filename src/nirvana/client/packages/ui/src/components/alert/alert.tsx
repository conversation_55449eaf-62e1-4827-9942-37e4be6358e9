import * as React from 'react';
import {
  HiCheckCircle,
  HiExclamationCircle,
  HiInformationCircle,
  HiSparkles,
  HiX,
} from 'react-icons/hi';
import { cva, type VariantProps } from 'cva';
import { HiExclamationTriangle } from 'react-icons/hi2';

import { cn } from '@nirvana/core/utils';
import Show from '../show';
import Button, { ButtonProps } from '../button';

const alertVariants = cva(
  'relative p-4 w-full rounded-lg [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:w-4 [&>svg]:h-4 [&>svg~*]:pl-6 [&>svg+div]:translate-y-[-3px]',
  {
    variants: {
      severity: {
        info: 'bg-tw-blue-100 [&>svg]:text-tw-blue-700',
        warning: 'bg-tw-orange-100 [&>svg]:text-tw-orange-600',
        error: 'bg-tw-red-100 [&>svg]:text-tw-red-600',
        success: 'bg-tw-green-100 [&>svg]:text-tw-green-600',
        teal: 'bg-tw-teal-100 [&>svg]:text-tw-teal-600',
        gray: 'bg-tw-gray-100 [&>svg]:text-tw-gray-600',
      },
    },
    defaultVariants: {
      severity: 'info',
    },
  },
);

type AlertSeverity = VariantProps<typeof alertVariants>['severity'];

const getIcon = (severity: AlertSeverity) => {
  switch (severity) {
    case 'info':
      return <HiInformationCircle />;
    case 'warning':
    case 'gray':
      return <HiExclamationTriangle />;
    case 'error':
      return <HiExclamationCircle />;
    case 'success':
      return <HiCheckCircle />;
    case 'teal':
      return <HiSparkles />;
    default:
      return <HiInformationCircle />;
  }
};

type AlertProps = React.HTMLAttributes<HTMLDivElement> & {
  /** The severity of the alert.
   * Provided options are 'info' | 'warning' | 'error' | 'success' | 'teal' | 'gray'
   * @default 'info'
   */
  severity?: AlertSeverity;
  /** If true, the alert will have a close button */
  closeable?: boolean;
  /** Callback fired when the close button is clicked */
  onClose?: () => void;
};

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className, children, closeable, onClose, severity, ...props }, ref) => {
    const [isClosed, setIsClosed] = React.useState(false);

    return (
      <Show when={!isClosed}>
        <div
          ref={ref}
          role="alert"
          className={cn(alertVariants({ severity }), className)}
          {...props}
        >
          {getIcon(severity)}
          {children}
          <Show when={closeable}>
            <button
              className="absolute right-4 top-4"
              onClick={() => {
                onClose?.();
                setIsClosed(true);
              }}
            >
              <HiX />
            </button>
          </Show>
        </div>
      </Show>
    );
  },
);
Alert.displayName = 'Alert';

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => {
  return (
    <h5
      ref={ref}
      className={cn('mb-1 font-medium leading-none tracking-tight', className)}
      {...props}
    />
  );
});
AlertTitle.displayName = 'AlertTitle';

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('', className)} {...props} />
));
AlertDescription.displayName = 'AlertDescription';

const AlertActions = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn('flex space-x-2 absolute right-4 top-4', className)}
    {...props}
  />
);
AlertActions.displayName = 'AlertActions';

const AlertActionConfirm = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ ...props }, ref) => <Button ref={ref} variant="primary" {...props} />,
);
AlertActionConfirm.displayName = 'AlertActionConfirm';

const AlertActionCancel = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ ...props }, ref) => <Button ref={ref} variant="text" {...props} />,
);
AlertActionCancel.displayName = 'AlertActionCancel';

export default Object.assign(Alert, {
  Title: AlertTitle,
  Description: AlertDescription,
  Actions: AlertActions,
  ActionConfirm: AlertActionConfirm,
  ActionCancel: AlertActionCancel,
});
