package nflag

import (
	"time"

	"github.com/cockroachdb/errors"

	"github.com/spf13/pflag"
)

const timeValuePflagName = "time"

// timeValue adapts time.Time for use as a pflag
type timeValue struct {
	time.Time
}

var _ pflag.Value = (*timeValue)(nil)

func (t timeValue) String() string {
	return t.Time.Format(time.RFC3339Nano)
}

func (t *timeValue) Set(s string) error {
	parsed, err := time.Parse(time.RFC3339Nano, s)
	if err != nil {
		return err
	}
	*t = timeValue{Time: parsed}
	return nil
}

func (timeValue) Type() string {
	return timeValuePflagName
}

func newTimeValue(val time.Time, p *time.Time) *timeValue {
	*p = val
	return &timeValue{Time: *p}
}

// GetTime return the time.Time value of a flag with the given name
func GetTime(f *pflag.FlagSet, name string) (time.Time, error) {
	flag := f.Lookup(name)
	if flag == nil {
		return time.Time{}, errors.Newf("flag accessed but not defined: %s", name)
	}
	if flag.Value.Type() != timeValuePflagName {
		return time.Time{}, errors.Newf(
			"trying to get %s value of flag of type %s",
			timeValuePflagName,
			flag.Value.Type(),
		)
	}
	return time.Parse(time.RFC3339Nano, flag.Value.String())
}

// TimeVar defines a time.Time flag with specified name, default value, and usage string.
// The argument p points to a time.Time variable in which to store the value of the flag.
func TimeVar(f *pflag.FlagSet, p *time.Time, name string, value time.Time, usage string) {
	TimeVarP(f, p, name, "", value, usage)
}

// TimeVarP is like TimeVar, but accepts a shorthand letter that can be used after a single dash.
func TimeVarP(f *pflag.FlagSet, p *time.Time, name, shorthand string, value time.Time, usage string) {
	f.VarP(newTimeValue(value, p), name, shorthand, usage)
}

// Time defines a time.Time flag with specified name, default value, and usage string.
// The return value is the address of a time.Time variable that stores the value of the flag.
func Time(f *pflag.FlagSet, name string, value time.Time, usage string) *time.Time {
	return TimeP(f, name, "", value, usage)
}

// TimeP is like Time, but accepts a shorthand letter that can be used after a single dash.
func TimeP(f *pflag.FlagSet, name, shorthand string, value time.Time, usage string) *time.Time {
	p := new(time.Time)
	TimeVarP(f, p, name, shorthand, value, usage)
	return p
}
