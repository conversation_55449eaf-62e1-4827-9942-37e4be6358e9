package artifactory

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"nirvanatech.com/nirvana/common-go/postgres_utils"
)

type (
	SQLBackend = boil.ContextExecutor

	SQLArtifact TaskArtifact[SQLBackend]

	// SQLArtifactory represents the Artifactory to read/write SQLArtifact(s)
	SQLArtifactory[T SQLArtifact] Artifactory[SQLBackend, T]
)

// psqlArtifactory represents the PostgresSQL backed implementation of the
// TaskResultLogger interface.
type psqlArtifactory[T SQLArtifact] struct {
	db            *sql.DB
	newArtifactFn NewArtifactFn[SQLBackend, T]
}

func NewPSQLArtifactory[T SQLArtifact](
	db *sql.DB,
	newArtifactFn NewArtifactFn[SQLBackend, T],
) SQLArtifactory[T] {
	return &psqlArtifactory[T]{
		db:            db,
		newArtifactFn: newArtifactFn,
	}
}

var _ SQLArtifactory[SQLArtifact] = (*psqlArtifactory[SQLArtifact])(nil)

func (p *psqlArtifactory[T]) Upsert(
	ctx context.Context,
	key Key,
	updateFn ArtifactUpdateFn[SQLBackend, T],
) error {
	obj := p.newArtifactFn(key)
	return postgres_utils.WrapInTx(
		ctx, p.db,
		upsertExecutor(ctx, obj, updateFn),
		postgres_utils.IsolationReadCommitted,
	)
}

func (p *psqlArtifactory[T]) Get(
	ctx context.Context,
	key Key,
) (T, error) {
	obj := p.newArtifactFn(key)
	loadErr := obj.Load(ctx, p.db)
	if loadErr != nil {
		return obj, errors.Wrapf(
			loadErr,
			"failed to execute load query for result, key=%s",
			key.String(),
		)
	}
	return obj, nil
}

// upsertExecutor receives a SQLArtifact and its `updateFn` and returns a function
// that can execute the upsert operation (for the updateFn), given a sql.Tx
func upsertExecutor[T SQLArtifact](
	ctx context.Context,
	obj T,
	updateFn ArtifactUpdateFn[SQLBackend, T],
) func(tx *sql.Tx) error {
	return func(tx *sql.Tx) error {
		key := obj.Key()
		loadErr := obj.Load(ctx, tx)
		if loadErr != nil && !errors.Is(loadErr, sql.ErrNoRows) {
			return errors.Wrapf(
				loadErr,
				"failed to execute load query for result, key=%s",
				key.String(),
			)
		}
		updatedObj, err := updateFn(ctx, obj)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to update result, key=%s",
				key.String(),
			)
		}
		if upsertErr := updatedObj.Upsert(ctx, tx); upsertErr != nil {
			return errors.Wrapf(
				upsertErr,
				"failed to upsert result, key=%s",
				key.String(),
			)
		}
		return nil
	}
}
