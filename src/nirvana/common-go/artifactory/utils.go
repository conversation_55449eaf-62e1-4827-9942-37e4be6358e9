package artifactory

import (
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"

	"nirvanatech.com/nirvana/jobber/jtypes"
)

const (
	jobRunIdSeparator = ":-:-:"
)

var ErrInvalidJobRunId = errors.New("JobRunId is invalid")

// JobRunIdToString converts the JobRunId provided in to a string IFF the JobRun
// is a "valid" one.
// NOTE: We use this function (instead of JobRunId.String()) to retain serde logic
// (jobRunId separator) to this package.
func JobRunIdToString(src jtypes.JobRunId) (string, error) {
	if src == jtypes.InvalidJobRunId {
		return "", ErrInvalidJobRunId
	}
	if src.JobId == "" && src.RunId == 0 {
		return "", ErrInvalidJobRunId
	}
	return strings.Join([]string{
		string(src.JobId),
		strconv.Itoa(int(src.RunId)),
	}, jobRunIdSeparator), nil
}

// JobRunIdFromString parses the given string in to a valid jtypes.JobRunId if
// possible, returning an appropriate error otherwise.
func JobRunIdFromString(src string) (jtypes.JobRunId, error) {
	elems := strings.Split(src, jobRunIdSeparator)
	if len(elems) != 2 {
		return jtypes.InvalidJobRunId, errors.Newf(
			"failed to find JobId & RunId in input string %s",
			src,
		)
	}
	runId, err := strconv.Atoi(elems[1])
	if err != nil {
		return jtypes.InvalidJobRunId, errors.Wrapf(
			err,
			"failed to parse %s as runId",
			elems[1],
		)
	}
	return jtypes.JobRunId{
		JobId: jtypes.JobId(elems[0]),
		RunId: jtypes.RunId(runId),
	}, err
}

// AllNonNull returns true IFF all the `items` passed to the function are "not null"
func AllNonNull(items ...qmhelper.Nullable) bool {
	for idx := range items {
		if items[idx].IsZero() {
			return false
		}
	}
	return true
}

func PtrCopierFn[T any](t *T) *T {
	if t == nil {
		return nil
	}
	tCopy := *t
	return &tCopy
}
