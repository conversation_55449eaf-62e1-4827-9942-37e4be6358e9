package crypto_utils

import (
	"fmt"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
)

// KeyId represents a KMS key alias in AWS. These should be named like so:
// if key name on AWS is `{workspace}-{kebab_case_key_name}, the enum here
// should be `KeyId{PascalCaseKeyName}`. Eg: KeyIdFmcsaCredentials
//
//go:generate go run github.com/dmarkham/enumer -type=KeyId -trimprefix=KeyId -transform=kebab
type KeyId int

const (
	KeyIdTest KeyId = iota + 1
	KeyIdFmcsaCredentials
	KeyIdTruckercloudUserCredentials
	KeyIdTelematicsApiKeyCredentials
	KeyIdTerminalUserCredentials
	KeyIdTspWebhookSecretKeysCredentials
	KeyIdTelematicsConsentUserRequestMetadata
	KeyIdUserSSN
	KeyIdUserSSNLastFour
)

func (i *KeyId) alias(prefix string) (*string, error) {
	if !i.IsAKeyId() {
		return nil, errors.Newf("unsupported keyId %s", i.String())
	}
	return pointer_utils.String(
		fmt.Sprintf("alias/%s-%s", prefix, i.String()),
	), nil
}
