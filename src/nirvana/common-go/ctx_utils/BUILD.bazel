load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "ctx_utils",
    srcs = [
        "detached.go",
        "keys.go",
        "request_id.go",
    ],
    importpath = "nirvanatech.com/nirvana/common-go/ctx_utils",
    visibility = ["//visibility:public"],
)

go_test(
    name = "ctx_utils_test",
    srcs = ["detached_test.go"],
    embed = [":ctx_utils"],
    deps = ["@com_github_stretchr_testify//assert"],
)
