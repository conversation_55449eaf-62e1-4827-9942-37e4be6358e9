package db_migration_utils

import "github.com/volatiletech/null/v8"

type MigratorConfig interface {
	isMigrateConfig()
}

// MigrateUp up-migrates the database to the latest Version
type MigrateUp struct{}

func (m *MigrateUp) isMigrateConfig() {}

// MigrateDown down-migrates the DB, applying *all* down migrations.
// NOTE: This should never be needed in prod, so use this very carefully
type MigrateDown struct{}

func (m *MigrateDown) isMigrateConfig() {}

// MigrateTo migrates the database to the specified Version.
// NOTE: `Version` cannot be null or negative.
type MigrateTo struct{ Version null.Uint }

func (m *MigrateTo) isMigrateConfig() {}

var _ MigratorConfig = (*MigrateUp)(nil)

var _ MigratorConfig = (*MigrateDown)(nil)

var _ MigratorConfig = (*MigrateTo)(nil)
