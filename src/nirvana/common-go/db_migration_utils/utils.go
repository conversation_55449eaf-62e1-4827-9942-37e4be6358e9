package db_migration_utils

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/golang-migrate/migrate/v4/source"

	"nirvanatech.com/nirvana/common-go/path_utils"
	"nirvanatech.com/nirvana/db-api/postups"
	fmcsa_postups "nirvanatech.com/nirvana/fmcsa/postups"
)

const (
	NirvanaMigrationsSourceDir = "nirvana/db-api/migrations"
	FMCSAMigrationsSourceDir   = "nirvana/fmcsa/migrations"
	DSMigrationsSourceDir      = "nirvana/ds/migrations"
)

type MigrationDeps struct {
	NirvanaDB *sql.DB
	FMCSADB   *sql.DB
	DSDB      *sql.DB
}

type MigrationArgs struct {
	Direction       string
	Version         *int
	YesToAllPrompts bool
	WipeBeforeStart bool
}

type MigrationInfo struct {
	CurrentVersion int  `json:"current_version"`
	CurrentPostUp  int  `json:"current_postup"`
	Dirty          bool `json:"dirty"`
	PendingVersion uint `json:"pending_version"`
}

func NewMigrator(ctx context.Context, deps *MigrationDeps, dbname string) (*Migrator, error) {
	var cfg MigratorOptions
	var db *sql.DB
	switch dbname {
	case "nirvana":
		migPath, err := path_utils.WorkspacePath(NirvanaMigrationsSourceDir)
		if err != nil {
			return nil, err
		}
		cfg = NewMigratorOptions(
			migPath,
			true,
			postups.GetNirvanaDbPostupExecutorForVersion,
		)
		db = deps.NirvanaDB
	case "fmcsa":
		migPath, err := path_utils.WorkspacePath(FMCSAMigrationsSourceDir)
		if err != nil {
			return nil, err
		}
		cfg = NewMigratorOptions(
			migPath,
			true,
			fmcsa_postups.GetFmcsaDbPostupExecutorForVersion,
		)
		db = deps.FMCSADB
	case "ds":
		migPath, err := path_utils.WorkspacePath(DSMigrationsSourceDir)
		if err != nil {
			return nil, err
		}
		cfg = NewMigratorOptions(
			migPath,
			false,
			fmcsa_postups.GetFmcsaDbPostupExecutorForVersion,
		)
		db = deps.DSDB
	default:
		return nil, errors.New("unsupported dbname")
	}
	return InitMigrator(ctx, db, cfg, false)
}

func GetMigrationInfo(ctx context.Context, migrator *Migrator) (*MigrationInfo, error) {
	v, d, err := migrator.GetDbVersion()
	if err != nil {
		return nil, err
	}
	pending, err := migrator.GetHighestSourceVersion()
	if err != nil {
		return nil, err
	}
	postup, postupD, err := GetDBPostupVersion(ctx, migrator.db)
	if err != nil && err != ErrNoPostupState {
		return nil, err
	}
	return &MigrationInfo{
		CurrentVersion: v,
		CurrentPostUp:  postup,
		Dirty:          d || postupD,
		PendingVersion: pending,
	}, nil
}

func GetSourceOnlyMigrationInfo(dbname string) (*MigrationInfo, error) {
	var sourceDir string
	switch dbname {
	case "nirvana":
		sourceDir = NirvanaMigrationsSourceDir
	case "fmcsa":
		sourceDir = FMCSAMigrationsSourceDir
	case "ds":
		sourceDir = DSMigrationsSourceDir
	}
	s, err := source.Open("file://" + sourceDir)
	if err != nil {
		return nil, errors.Wrap(err, "failed to initialise migrations source directory")
	}
	v, err := GetHighestSourceVersion(s)
	if err != nil {
		return nil, err
	}
	return &MigrationInfo{PendingVersion: v}, nil
}
