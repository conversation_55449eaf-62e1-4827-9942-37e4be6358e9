package feature_flag_lib

import (
	"github.com/launchdarkly/go-sdk-common/v3/ldcontext"
	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"
)

type LDContexts []ldcontext.Context

// Client interface exposes all the methods used to access our feature flagging
// client. ldcontexts is a common argument across all the methods, and is essentially
// a list of map of attributes and their values. Each element of this list is ldContext, and is
// used to make a feature flag lookup until one of the resultant value is unequal to
// baseVal. Consider the following hypothetical example, assume flag's value for "agency-2" has been
// set to True at LD, and everyone else otherwise. In this case, there will only be two feature flag
// lookups and the resultant value will be True:
// ldContexts := [
//
//	{name: "Nirvana", agency: "agency-1"},
//	{name: "Nirvana", agency: "agency-2"},
//	{name: "Nirvana", agency: "agency-3"}]
//
// _, err := Client.BoolVariation(ldContexts, flag, False)
//
//go:generate go run go.uber.org/mock/mockgen -destination mock/mock_client.go -package mock_feature_flag_lib -source interfaces.go Client
type Client interface {
	// BoolVariation returns the value of a bool feature flag for a given context.
	BoolVariation(ldContexts LDContexts, flagKey Feature, baseVal bool) (bool, error)

	// IntVariation returns the value of an int feature flag for a given context.
	IntVariation(ldContexts LDContexts, flagKey Feature, baseVal int) (int, error)

	// StringVariation returns the value of a string feature flag for a given context.
	StringVariation(ldContexts LDContexts, flagKey Feature, baseVal string) (string, error)

	// JSONVariation returns the value of a json feature flag for a given context.
	JSONVariation(ldContexts LDContexts, flagKey Feature, baseVal ldvalue.Value) (ldvalue.Value, error)

	Close() error
}
