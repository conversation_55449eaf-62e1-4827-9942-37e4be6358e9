package file_upload_lib

import (
	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/config"
)

// GetBucketName returns the bucket name for a given file destination group
// based on the current environment.
// The S3 bucket name for a file destination group contains:
//   - A prefix, which can be used to represent the environment. For legacy
//     reasons, no prefix means **PRODUCTION**.
//   - The literal string "nirvana"
//   - The base name of the file destination group
//
// For example, the bucket name for `FileDestinationGroupQuoting` in the production
// environment is `nirvana-quoting`. In the staging environment, it'll be
// `staging-nirvana-quoting`.
func GetBucketName(cfg *config.Config, fdGroup enums.FileDestinationGroup) string {
	bucketCfg, err := getBucketConfiguration(fdGroup)
	if err != nil {
		// Log & Continue
		// TODO: Change the function's signature to return an error instead.
		log.Plain.Error(
			"Failed to get bucket configuration for file destination group",
			log.Err(err),
		)
		// For legacy reasons, we set the bucket name to `nirvana-quoting`, should
		// go away once we change the function's signature to return an error.
		bucketCfg = bucketConfiguration{
			baseName: "nirvana-quoting",
		}
	}
	// Early return if a custom prefix is found in the configuration.
	if customEnvPrefix := bucketCfg.customEnvPrefix[cfg.GetEnv()]; customEnvPrefix.Valid {
		return customEnvPrefix.String + bucketCfg.baseName
	}

	// Otherwise, use the default environment prefix (if found).
	defaultEnvPrefix, err := getDefaultPrefixForEnvironment(cfg.GetEnv())
	if err != nil {
		// Log & Continue
		// TODO: Change the function's signature to return an error instead.
		log.Plain.Error("Failed to get default prefix for environment", log.Err(err))
	}
	if defaultEnvPrefix.Valid {
		return defaultEnvPrefix.String + bucketCfg.baseName
	}

	// If no prefix is found, return the base name as the bucket name.
	return bucketCfg.baseName
}

// getDefaultPrefixForEnvironment returns the default prefix for a given environment.
func getDefaultPrefixForEnvironment(env config.Env) (null.String, error) {
	switch env {
	case config.Env_PROD:
		return null.String{ /* No prefix for production */ }, nil
	case config.Env_DEV, config.Env_TEST:
		// TODO: Use different buckets for dev & test environments.
		return getDefaultPrefixForEnvironment(config.Env_PROD)
	default:
		return null.String{}, errors.Newf("unknown environment: %s", env)
	}
}

// bucketConfiguration contains the configuration for a bucket. Only the
// base name is required. Custom environment prefixes are only needed if
// we need to override the bucket names automatically resolved by
// GetBucketName().
type bucketConfiguration struct {
	baseName        string
	customEnvPrefix map[config.Env]null.String
}

func getBucketConfiguration(fdGroup enums.FileDestinationGroup) (bucketConfiguration, error) {
	// nolint: exhaustive
	switch fdGroup {
	case enums.FileDestinationGroupQuoting:
		return bucketConfiguration{
			baseName: "nirvana-quoting",
		}, nil
	case enums.FileDestinationGroupMVRCache:
		return bucketConfiguration{
			baseName: "nirvana-mvr-cache",
		}, nil
	case enums.FileDestinationGroupPDFGen:
		return bucketConfiguration{
			baseName: "nirvana-pdfgen",
		}, nil
	case enums.FileDestinationGroupTelematics:
		return bucketConfiguration{
			baseName: "nirvana-telematics-default",
		}, nil
	case enums.FileDestinationGroupUnderwriting:
		return bucketConfiguration{
			baseName: "nirvana-underwriting",
		}, nil
	case enums.FileDestinationGroupForms:
		return bucketConfiguration{
			baseName: "nirvana-forms",
		}, nil
	case enums.FileDestinationGroupFLI:
		return bucketConfiguration{
			baseName: "nirvana-fli-data-feeds",
		}, nil
	case enums.FileDestinationGroupBlobStore:
		return bucketConfiguration{
			baseName: "nirvana-blob-store",
		}, nil
	case enums.FileDestinationGroupMVRLexisNexisAttractCache:
		return bucketConfiguration{
			baseName: "nirvana-lexisnexis-attract-cache",
		}, nil
	case enums.FileDestinationGroupScrapedQuotes:
		return bucketConfiguration{
			baseName: "nirvana-quote-scraper",
		}, nil
	case enums.FileDestinationGroupBilling:
		return bucketConfiguration{
			baseName: "nirvana-billing",
		}, nil
	case enums.FileDestinationGroupClaims:
		return bucketConfiguration{
			baseName: "nirvana-claims",
		}, nil
	case enums.FileDestinationGroupDatagov:
		return bucketConfiguration{
			baseName: "nirvana-datagov",
		}, nil
	case enums.FileDestinationGroupAPIArtifactsStore:
		return bucketConfiguration{
			baseName: "nirvana-api-artifacts-store",
		}, nil
	case enums.FileDestinationGroupReport:
		return bucketConfiguration{
			baseName: "nirvana-reports",
		}, nil
	case enums.FileDestinationGroupLexisNexisNationalCreditFileCache:
		return bucketConfiguration{
			baseName: "nirvana-lexisnexis-ncf-cache",
		}, nil
	case enums.FileDestinationGroupEmailDraftFNOLAttachments:
		return bucketConfiguration{
			baseName: "reportaclaim-email-attachments",
		}, nil
	case enums.FileDestinationGroupMstReferralTemplates:
		return bucketConfiguration{
			baseName: "nirvana-mst-referral-templates",
		}, nil
	default:
		return bucketConfiguration{}, errors.Newf("encountered unknown file destination group: %s", fdGroup)
	}
}
