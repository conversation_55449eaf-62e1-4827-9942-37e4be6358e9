package google_utils

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"golang.org/x/oauth2"

	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/oauth"
)

const (
	oauthGoogleUrlAPI = "https://www.googleapis.com/oauth2/v3/userinfo?access_token="
	redirectUrlAPI    = "https://underwriter.nirvanatech.com/oauth2/redirect/google"
)

func NewOAuthIntegrationImpl(
	cfg *config.Config,
	statSender statsd.StatSender,
	provider oauth.Provider,
) *OAuthIntegrationImpl {
	googleOauthHelper := OAuthIntegrationImpl{
		Config: &oauth2.Config{
			RedirectURL:  redirectUrlAPI,
			ClientID:     cfg.GetProductTools().GetGoogleAppConfig().GetClientId(),
			ClientSecret: cfg.GetProductTools().GetGoogleAppConfig().GetClientSecret(),
			Scopes:       []string{"https://www.googleapis.com/auth/gmail.modify"},
			Endpoint: oauth2.Endpoint{
				AuthURL:  "https://accounts.google.com/o/oauth2/auth",
				TokenURL: "https://oauth2.googleapis.com/token",
			},
		},
		statSender: statSender,
		provider:   provider,
	}
	return &googleOauthHelper
}

// More info on: https://developers.google.com/identity/protocols/oauth2
type OAuthIntegrationImpl struct {
	statSender statsd.StatSender
	provider   oauth.Provider
	Config     *oauth2.Config
}

func (o *OAuthIntegrationImpl) GenerateOAuthUrl(connectionState string) string {
	url := o.Config.AuthCodeURL(connectionState)
	params := []string{
		"approval_prompt=force",
		"access_type=offline",
	}
	// HACK To Query parameter to add to the URL.
	// This is to ensure that the refresh token is returned
	for _, param := range params {
		// Check if the URL already has a query string
		if strings.Contains(url, "?") {
			// Append the query parameter using "&"
			url += "&" + param
		} else {
			// Append the query parameter using "?"
			url += "?" + param
		}
	}
	return url
}

func (o *OAuthIntegrationImpl) ExchangeCodeForAccessToken(ctx context.Context, authorizationCode string) (*oauth.OAuthCredentials, error) {
	token, err := o.Config.Exchange(ctx, authorizationCode)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for access token: %v", err)
	}
	return o.bindGoogleCredentials(token), nil
}

func (o *OAuthIntegrationImpl) generateScope() string {
	scope := ""
	for _, s := range o.Config.Scopes {
		scope += s + " "
	}
	return scope
}

func (o *OAuthIntegrationImpl) bindGoogleCredentials(token *oauth2.Token) *oauth.OAuthCredentials {
	credentials := &oauth.OAuthCredentials{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		// generate how many seconds the token is valid for
		// from the time it is issued.
		ExpiresIn: int(time.Until(token.Expiry).Seconds()),
		TokenType: token.TokenType,
		Scope:     o.generateScope(),
	}
	return credentials
}

func (o *OAuthIntegrationImpl) RefreshOAuthCredentials(ctx context.Context, refreshToken string) (*oauth.OAuthCredentials, error) {
	token := &oauth2.Token{
		RefreshToken: refreshToken,
	}
	refreshedToken, err := o.Config.TokenSource(ctx, token).Token()
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %v", err)
	}
	return o.bindGoogleCredentials(refreshedToken), nil
}

func (o *OAuthIntegrationImpl) Provider() oauth.Provider {
	return o.provider
}

func GetUserInfo(ctx context.Context, token *oauth2.Token) (*UserInfo, error) {
	// Use code to get token and get user info from Google.
	response, err := http.Get(oauthGoogleUrlAPI + token.AccessToken)
	if err != nil {
		return nil, errors.Newf("failed getting user info: %s", err.Error())
	}
	defer response.Body.Close()
	contents, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, errors.Newf("failed read response: %s", err.Error())
	}
	var result UserInfo
	if err := json.Unmarshal(contents, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

var _ oauth.OAuthIntegration = &OAuthIntegrationImpl{}

type UserInfo struct {
	Sub           string `json:"sub"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Profile       string `json:"profile"`
	Picture       string `json:"picture"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Gender        string `json:"gender"`
}
