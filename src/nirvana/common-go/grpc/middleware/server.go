package middleware

import (
	"context"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors/grpc/middleware"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/infra/panicwrap"
)

func recoveryHandler(ctx context.Context, p interface{}) error {
	return panicwrap.HandlePanic(ctx, p)
}

func DefaultServerUnaryInterceptors(metricsClient statsd.Statter) []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{
		grpc_recovery.UnaryServerInterceptor(
			grpc_recovery.WithRecoveryHandlerContext(recoveryHandler),
		),
		otelgrpc.UnaryServerInterceptor(),
		// The cockroachdb-error's middleware.UnaryServerInterceptor must be put AFTER the metrics and logger
		// because the interceptors are applied in the reverse order while processing the response and the cockroachdb
		// errors interceptor needs to transform the errors into the universal grpc language before we apply the metrics
		// and logger interceptors on those errors.
		MetricsServerUnaryInterceptor(metricsClient),
		GetUnaryLoggerMiddleware(),
		middleware.UnaryServerInterceptor,
		ContextErrorUnaryInterceptor(),
	}
}

func DefaultServerStreamInterceptors(metricsClient statsd.Statter) []grpc.StreamServerInterceptor {
	return []grpc.StreamServerInterceptor{
		grpc_recovery.StreamServerInterceptor(
			grpc_recovery.WithRecoveryHandlerContext(recoveryHandler)),
		otelgrpc.StreamServerInterceptor(),
		MetricsServerStreamInterceptor(metricsClient),
		ContextErrorStreamInterceptor(),
	}
}

func DefaultServerOptions(metricsClient statsd.Statter) []grpc.ServerOption {
	return []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(DefaultServerUnaryInterceptors(metricsClient)...),
		grpc.ChainStreamInterceptor(DefaultServerStreamInterceptors(metricsClient)...),
	}
}
