package slice_encoding

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// JSONEncodeSliceOfProtoMessages encodes a slice of protobuf messages into JSON.
func JSONEncodeSliceOfProtoMessages[T proto.Message](protoMessages []T) ([]byte, error) {
	encodedSliceOfProtoMessages := make([]json.RawMessage, len(protoMessages))
	if len(protoMessages) == 0 {
		return json.Marshal(encodedSliceOfProtoMessages)
	}
	for i, p := range protoMessages {
		encodedProto, err := protojson.Marshal(p)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to marshal proto at index %d", i)
		}
		encodedSliceOfProtoMessages[i] = encodedProto
	}

	return json.Marshal(encodedSliceOfProtoMessages)
}
