package knock_client

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func Test_UserPreferencesToKnock(t *testing.T) {
	userPreferences := UserWorkflowPreferences{
		{
			Workflow: "send-violation-alerts",
			Email:    true,
			SMS:      false,
		},
	}

	expected := map[string]interface{}{
		"send-violation-alerts": map[string]interface{}{
			"channel_types": map[string]interface{}{
				"email": true,
				"sms":   false,
			},
		},
	}

	actual := userPreferences.toKnock()
	assert.Equal(t, expected, actual)
}

func TestInitialScoreNotificationToKnock(t *testing.T) {
	sessionId := uuid.New()
	initialScoreNotification := InitialScoreNotificationData{
		SafetyReportURL: "http://example.com",
		Score:           55,
		SessionId:       sessionId,
	}

	expected := map[string]interface{}{
		"url":       "http://example.com",
		"sessionId": sessionId.String(),
		"score":     "55",
	}

	actual := initialScoreNotification.toKnock()
	assert.Equal(t, expected, actual)
}
