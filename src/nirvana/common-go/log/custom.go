package log

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// ObjectEncoder is a wrapper around zapcore.ObjectEncoder except that
// ObjectEncoder.AddObject and ObjectEncoder.AddArray methods are not
// allowed, AddCustomArray and AddCustomObject should be used in
// their place.
// see zapcore.ObjectEncoder for more info.
type ObjectEncoder interface {
	zapcore.ObjectEncoder
	// Logging-specific marshalers.
	AddCustomObject(key string, marshaler ObjectMarshaler) error
	AddCustomArray(key string, marshaler ArrayMarshaler) error
}

type objectEncoderWrapper struct {
	zapcore.ObjectEncoder
}

func (o objectEncoderWrapper) AddCustomObject(key string, marshaler ObjectMarshaler) error {
	return o.AddObject(key, zapcore.ObjectMarshalerFunc(
		func(enc zapcore.ObjectEncoder) error {
			return marshaler.MarshalLogObject(objectEncoderWrapper{enc})
		},
	))
}

func (o objectEncoderWrapper) AddCustomArray(key string, marshaler ArrayMarshaler) error {
	return o.AddArray(key, zapcore.ArrayMarshalerFunc(
		func(enc zapcore.ArrayEncoder) error {
			return marshaler.MarshalLogArray(arrayEncoderWrapper{enc})
		},
	))
}

// ObjectMarshaler can be implemented by a type to provide
// custom marshaling while logging. It will only be
// applied when type is logged using log.Object.
// See zapcore.ObjectMarshaler for more info.
type ObjectMarshaler interface {
	MarshalLogObject(ObjectEncoder) error
}

// ObjectMarshalerFunc is a type adapter that turns a function into an
// ObjectMarshaler.
type ObjectMarshalerFunc func(ObjectEncoder) error

// MarshalLogObject calls the underlying function.
func (f ObjectMarshalerFunc) MarshalLogObject(enc ObjectEncoder) error {
	return f(enc)
}

// Object constructs a field with the given key and ObjectMarshaler.
// See zap.Object for more info.
func Object(key string, val ObjectMarshaler) zap.Field {
	return zap.Object(key, zapcore.ObjectMarshalerFunc(
		func(z zapcore.ObjectEncoder) error {
			return val.MarshalLogObject(objectEncoderWrapper{z})
		},
	))
}

// ArrayEncoder is a wrapper around zapcore.ArrayEncoder except that
// ArrayEncoder.AppendObject and ArrayEncoder.AppendArray methods are not
// allowed, AppendCustomArray and AppendCustomObject should be used in
// their place.
// see zapcore.ArrayEncoder for more info.
type ArrayEncoder interface {
	zapcore.ArrayEncoder
	// Logging-specific marshalers.
	AppendCustomArray(ArrayMarshaler) error
	AppendCustomObject(ObjectMarshaler) error
}

type arrayEncoderWrapper struct {
	zapcore.ArrayEncoder
}

func (o arrayEncoderWrapper) AppendCustomObject(marshaler ObjectMarshaler) error {
	return o.AppendObject(zapcore.ObjectMarshalerFunc(
		func(enc zapcore.ObjectEncoder) error {
			return marshaler.MarshalLogObject(objectEncoderWrapper{enc})
		},
	))
}

func (o arrayEncoderWrapper) AppendCustomArray(marshaler ArrayMarshaler) error {
	return o.AppendArray(zapcore.ArrayMarshalerFunc(
		func(enc zapcore.ArrayEncoder) error {
			return marshaler.MarshalLogArray(arrayEncoderWrapper{enc})
		},
	))
}

// ObjectEncoder can be implemented by an array type to provide
// custom marshaling while logging. It will only be
// applied when type is logged using log.Array.
// See zapcore.ArrayMarshaler for more info.
type ArrayMarshaler interface {
	MarshalLogArray(ArrayEncoder) error
}

// Array constructs a field with the given key and ArrayMarshaler.
// See zap.Array for more info.
func Array(key string, val ArrayMarshaler) zap.Field {
	return zap.Array(key, zapcore.ArrayMarshalerFunc(
		func(z zapcore.ArrayEncoder) error {
			return val.MarshalLogArray(arrayEncoderWrapper{z})
		},
	))
}

// ArrayMarshalerFunc is a type adapter that turns a function into an
// ArrayMarshaler.
type ArrayMarshalerFunc func(ArrayEncoder) error

// MarshalLogArray calls the underlying function.
func (f ArrayMarshalerFunc) MarshalLogArray(enc ArrayEncoder) error {
	return f(enc)
}
