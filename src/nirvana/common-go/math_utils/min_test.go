package math_utils

import "testing"

func TestMinNumber(t *testing.T) {
	testCases := []struct {
		a    float32
		b    float32
		want float32
	}{
		{1.23, 2.34, 1.23},
		{42, 42, 42},
		{100, 500, 100},
		{500, 500, 500},
		{700, 500, 500},
	}

	for _, tc := range testCases {
		got := Min(tc.a, tc.b)
		if got != tc.want {
			t.<PERSON>rf("Min(%v, %v) = %v, want %v", tc.a, tc.b, got, tc.want)
		}
	}
}
