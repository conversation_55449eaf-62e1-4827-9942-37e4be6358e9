package metering

import (
	"context"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/infra/config"

	"nirvanatech.com/nirvana/common-go/log"
)

const (
	rootMetricPrefix    = "openmeter"
	responseTimeMetric  = "response_time"
	responseCountMetric = "response_count"
	metricTagMethod     = "method"
	metricTagURL        = "url"
	metricTagStatusCode = "status_code"
	// we pass same subject for all the events to avoid specifying subject while creating meters and entitlements in openmeter.
	globalSubject = "global"
)

var errNotFound = errors.New("404 not found")

type Client struct {
	statsSender        statsd.StatSender
	openmeterAPIClient *openmeterAPIClient
}

type UsageEvent struct {
	Type   string // this maps to event-type in openmeter
	Source string // event origination source like mvr-server, data-platform-job-processor etc.

	// Though openmeter supports nested jsons, for simplicity we are using flat map.
	// We can extend this to nested jsons if needed in the future.
	// Openmeter supports aggregation on number values passed as string so map value can be string or number (converted to string).
	Data map[string]string
}

type event struct {
	ID          string `json:"id"`
	Source      string `json:"source"`
	Type        string `json:"type"`
	Subject     string `json:"subject"`
	Data        any    `json:"data"`
	SpecVersion string `json:"specversion"`
	Time        string `json:"time"`
}

type meter struct {
	ID        string `json:"id"`
	Slug      string `json:"slug"`
	EventType string `json:"eventType"`
}

type feature struct {
	ID                  string             `json:"id"`
	Key                 string             `json:"key"`
	Name                string             `json:"name"`
	MeterGroupByFilters *map[string]string `json:"meterGroupByFilters"`
}

type EntitlementValue struct {
	HasAccess bool `json:"hasAccess"`

	// Balance, Usage and Overage are available only for metered-entitlements
	Balance *int `json:"balance"`
	Usage   *int `json:"usage"`
	Overage *int `json:"overage"`

	// Config is available only for static-entitlements
	Config *string `json:"config"`
}

// TODO: instances should be accessed only through fx, make this private once the clients using this are migrated to fx
func NewClient(metricsClient statsd.Statter, cfg *config.Config) *Client {
	statsSender := metricsClient.NewSubStatter(rootMetricPrefix)
	openmeterClient := newOpenmeterAPIClient(statsSender, cfg)
	return &Client{
		statsSender:        statsSender,
		openmeterAPIClient: openmeterClient,
	}
}

// LogUsage ingests the usage event to openmeter.
func (c *Client) LogUsage(ctx context.Context, usage UsageEvent) error {
	evt := event{
		ID:          uuid.New().String(),
		Source:      usage.Source,
		Type:        usage.Type,
		Subject:     globalSubject,
		Data:        usage.Data,
		SpecVersion: "1.0",
		Time:        time.Now().Format(time.RFC3339),
	}
	return c.openmeterAPIClient.sendEvent(ctx, evt)
}

// IsUsageAllowed checks if the usage is allowed based on the entitlements and meters defined in openmeter.
// The method takes care of fetching and checking applicable entitlements based on the data in the UsageEvent.
// TODO: Many API calls can be parallelised in this method. Keeping things simple for POC.
func (c *Client) IsUsageAllowed(ctx context.Context, event UsageEvent) (bool, error) {
	log.Info(ctx, "Checking if usage is allowed", log.Any("usageEvent", event))
	meters, err := c.openmeterAPIClient.getMetersForEvent(ctx, event.Type)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get meters")
	}
	log.Info(ctx, "Numbers of meters fetched for the event", log.Int("metersCount", len(meters)))
	for _, meter := range meters {
		ctx = log.ContextWithFields(ctx, log.String("meterSlug", meter.Slug))
		log.Info(ctx, "Checking if access is allowed for the meter")
		canAccessMeterFeatures, err := c.canAccessMeterFeatures(ctx, event, meter.Slug)
		if err != nil {
			return false, errors.Wrapf(err, "failed to check if access is allowed for meter features")
		}
		if !canAccessMeterFeatures {
			log.Info(ctx, "Cannot access meter features", log.String("meterSlug", meter.Slug))
			return false, nil
		}
	}

	log.Info(ctx, "Usage is allowed")
	return true, nil
}

// canAccessMeterFeatures checks if the usage event can access the features defined for the meter.
// The method takes care of filtering out features that are not applicable for the UsageEvent
func (c *Client) canAccessMeterFeatures(ctx context.Context, event UsageEvent, meterSlug string) (bool, error) {
	features, err := c.openmeterAPIClient.getFeatures(ctx, meterSlug)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get features")
	}
	log.Info(ctx, "Numbers of features fetched for the meter", log.Int("featuresCount", len(features)))

	for _, feature := range features {
		ctx = log.ContextWithFields(ctx, log.String("featureKey", feature.Key))
		log.Info(ctx, "Checking if feature is applicable and access is allowed for the feature", log.Any("feature", feature))
		isFeatureApplicable, err := c.isFeatureApplicableForEvent(ctx, event, feature)
		if err != nil {
			return false, errors.Wrapf(err, "failed to check if feature is applicable")
		}

		if !isFeatureApplicable {
			log.Info(ctx, "Feature is not applicable for the usage event")
			continue // do nothing
		}

		// if feature is applicable then check if access is allowed
		hasAccessToFeature, err := c.hasAccessToFeature(ctx, feature)
		if err != nil {
			return false, errors.Wrapf(err, "failed to check if access is allowed for feature")
		}

		if !hasAccessToFeature {
			log.Info(ctx, "Cannot access the feature")
			return false, nil
		}
	}
	// if there are no features created on the meter or if all the features has access then return true
	return true, nil
}

// isFeatureApplicableForEvent checks if the feature is applicable for the given usage event.
// Applicability is checked by matching the dimension values in the UsageEvent with the filters defined in the feature.
func (c *Client) isFeatureApplicableForEvent(ctx context.Context, event UsageEvent, feature feature) (bool, error) {
	log.Info(ctx, "Checking if feature is applicable for given usage event")
	// check if the feature is applicable for the intended usage
	isFeatureApplicable := true
	if feature.MeterGroupByFilters != nil {
		for dataKey := range event.Data {
			_, isFilterDefinedInFeatureForDataKey := (*feature.MeterGroupByFilters)[dataKey]
			if !isFilterDefinedInFeatureForDataKey {
				log.Info(ctx, "Filter not defined in feature for the dataKey", log.String("dataKey", dataKey))
				continue // do nothing if the data key is not used in the feature
			}
			dataValue := event.Data[dataKey]
			filterValue := (*feature.MeterGroupByFilters)[dataKey]
			log.Info(ctx, "Comparing dataValue in usage event and filterValue defined in the feature",
				log.String("dataKey", dataKey),
				log.Any("dataValue", dataValue),
				log.String("filterValue", filterValue),
			)
			isFeatureApplicable = isFeatureApplicable && dataValue == filterValue
		}
	}
	return isFeatureApplicable, nil
}

// hasAccessToFeature checks if the usage is allowed based on the entitlements defined for the feature.
// If no entitlement is defined for the feature then access is allowed.
func (c *Client) hasAccessToFeature(ctx context.Context, feature feature) (bool, error) {
	entitlementValue, err := c.openmeterAPIClient.getEntitlementValue(ctx, feature.Key)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get entitlement value")
	}
	log.Info(ctx, "Entitlement value fetched for the feature", log.Any("entitlementValue", entitlementValue))
	if entitlementValue == nil {
		// if no entitlement is found then feature is accessible
		return true, nil
	}
	// if any of the entitlement is breached then return false
	if !entitlementValue.HasAccess {
		log.Info(ctx, "Usage is not allowed because entitlement does not have access")
		return false, nil
	}
	return true, nil
}
