load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "store",
    srcs = [
        "compressed.go",
        "simple.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/common-go/pgtskv/store",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pgtskv",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_models/tskv_example",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//queries/qmhelper",
    ],
)
