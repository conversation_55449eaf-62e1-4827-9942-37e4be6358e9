package tests

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/problem"
)

func TestCollectionOperations(t *testing.T) {
	problems := problem.New()
	foo1 := FooProblem{FooId: "foo"}
	require.NoError(t, problems.Add(&foo1))
	// Id cannot be empty
	require.Error(t, problems.Add(&FooProblem{FooId: ""}))

	foo2 := FooProblem{FooId: "foo2"}
	require.NoError(t, problems.Add(&foo2))
	require.Len(t, problems.AsArray(), 2)

	require.Len(t, problems.GetAllByTag(FooTag).AsArray(), 2)

	foo2.Fixes.Override = "override"
	// problems are added by value, so changing foo2 shouldn't change the collection
	require.Empty(t, problems.Get(FooTag, "foo2").(*FooProblem).Fixes.Override)

	require.NoError(t, problems.Add(&foo2))
	require.Len(t, problems.GetAllByTag(FooTag).AsArray(), 2)

	require.Equal(t, "override", problems.Get(FooTag, "foo2").(*FooProblem).Fixes.Override)

	require.NoError(t,
		problems.ReplaceForTag(
			FooTag,
			func(problems *problem.Problems) (*problem.Problems, error) {
				return nil, nil
			}))
	require.Len(t, problems.GetAllByTag(FooTag).AsArray(), 0)

	// Add foo1 back
	require.NoError(t,
		problems.ReplaceForTag(
			FooTag,
			func(problems *problem.Problems) (*problem.Problems, error) {
				probs := problem.New()
				require.NoError(t, probs.Add(&foo1))
				return probs, nil
			}))
	require.Len(t, problems.GetAllByTag(FooTag).AsArray(), 1)
}

func TestRemoveOperation(t *testing.T) {
	problems := problem.New()
	foo1 := FooProblem{FooId: "foo1"}
	require.NoError(t, problems.Add(&foo1))
	foo2 := FooProblem{FooId: "foo2"}
	require.NoError(t, problems.Add(&foo2))

	problems.Remove(FooTag, "foo1")
	require.Len(t, problems.AsArray(), 1)
	require.Nil(t, problems.Get(FooTag, "foo1"))
	require.NotNil(t, problems.Get(FooTag, "foo2"))
}

func TestCollectionSerde(t *testing.T) {
	problems := problem.New()
	require.NoError(t, problems.Add(&FooProblem{FooId: "foo"}))
	require.NoError(t, problems.Add(&BarProblem{BarId: "bar"}))

	serialized, err := json.Marshal(problems)
	require.NoError(t, err)

	var deser problem.Problems
	err = json.Unmarshal(serialized, &deser)
	require.NoError(t, err)

	byTag := deser.GetAllByTag(FooTag)
	arr := byTag.AsArray()
	require.Equal(t, FooTag, arr[0].Tag())
	foo := arr[0].(*FooProblem)
	require.Equal(t, "foo", foo.Id())

	byTag = deser.GetAllByTag(BarTag)
	arr = byTag.AsArray()
	require.Equal(t, BarTag, arr[0].Tag())
	bar := arr[0].(*BarProblem)
	require.Equal(t, "bar", bar.Id())
}
