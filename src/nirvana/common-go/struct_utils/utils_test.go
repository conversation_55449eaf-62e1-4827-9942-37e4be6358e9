package struct_utils

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
)

type Data struct {
	X            int
	Y            int
	Str          string
	ptr          *string
	structMember SampleImpl
	ifMember     SampleInterface
	mapMember    map[string]interface{}
	sliceMember  []string
}

type SampleInterface interface {
	DoIt()
}

type SampleImpl struct {
	implField map[string]string
}

func (i SampleImpl) DoIt() {}

func TestNoZeroValues(t *testing.T) {
	var d Data
	err := NoZeroValues(d)
	assert.Error(t, err)
	t.Log(err)
}

func TestNoZeroValuesValuesSet(t *testing.T) {
	emptyString := ""

	d := Data{
		X: 0,
		Y: 1,
		// empty string is a zero value
		Str: emptyString,
		// but a pointer to an empty string is not a zero value
		ptr: &emptyString,

		// struct is not zero if at least one field is set
		structMember: SampleImpl{implField: make(map[string]string)},
		// interface is not zero if initialized with an implementing struct
		ifMember: SampleImpl{},

		mapMember:   make(map[string]interface{}),
		sliceMember: make([]string, 0),
	}

	err := NoZeroValues(d)
	assert.Error(t, err)
	t.Log(err)
}

type Test struct {
	IntT         int
	StringT      string
	StructT      null.Int
	ValPtrT      *int
	StructPtrT   *null.Int
	ValSliceT    []int
	StructSliceT []null.Int
	ArrayT       uuid.UUID

	unexported string
}

func TestNoZeroValuesStrict(t *testing.T) {
	a := 1
	full := Test{
		IntT:    1,
		StringT: "foo",
		StructT: null.Int{
			Int:   1,
			Valid: true,
		},
		ValPtrT: &a,
		StructPtrT: &null.Int{
			Int:   1,
			Valid: true,
		},
		ValSliceT:    []int{1},
		StructSliceT: []null.Int{null.IntFrom(1)},
		ArrayT:       uuid.New(),
	}
	type testcase struct {
		name      string
		val       Test
		isAnyZero bool
	}
	testcases := []testcase{
		{
			name:      "zero",
			isAnyZero: true,
		},
		{
			name:      "full",
			isAnyZero: false,
			val:       full,
		},
		func() testcase {
			withPartialStruct := full
			withPartialStruct.StructT = null.Int{Int: 1}
			return testcase{
				name:      "with-partial-struct",
				val:       withPartialStruct,
				isAnyZero: true,
			}
		}(),
		func() testcase {
			withEmptySlice := full
			withEmptySlice.ValSliceT = make([]int, 0)
			return testcase{
				name:      "with-empty-slice",
				val:       withEmptySlice,
				isAnyZero: true,
			}
		}(),
	}
	// use reflection to generate rest of the test cases
	typ := reflect.TypeOf(full)
	for i := 0; i < typ.NumField(); i++ {
		if !typ.Field(i).IsExported() {
			continue
		}
		newval := full // shallow copy
		reflect.ValueOf(&newval).Elem().Field(i).SetZero()
		testcases = append(testcases, testcase{
			name:      fmt.Sprintf("%s is empty", typ.Field(i).Name),
			val:       newval,
			isAnyZero: true,
		})
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			err := NoZeroValuesStrict(tc.val)
			if tc.isAnyZero {
				t.Log(err) // for seeing the error message
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
