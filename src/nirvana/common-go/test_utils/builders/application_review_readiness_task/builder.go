package application_review_readiness_task_builder

import (
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
)

type ApplicationReviewReadinessTaskBuilder struct {
	tasks []uw.Task
}

func New() *ApplicationReviewReadinessTaskBuilder {
	return &ApplicationReviewReadinessTaskBuilder{}
}

func (b *ApplicationReviewReadinessTaskBuilder) Build() []uw.Task {
	return b.tasks
}

func (b *ApplicationReviewReadinessTaskBuilder) FromReview(review *uw.ApplicationReview) *ApplicationReviewReadinessTaskBuilder {
	tasks := []uw.Task{
		{
			ID:       1,
			ReviewID: review.Id,
			Name:     uw.TaskNameSuccessfulTelematicsConnection,
			Status:   uw.TaskStatusPending,
			Assignee: review.UnderwriterID.String(),
		},
	}
	b.tasks = tasks
	return b
}
