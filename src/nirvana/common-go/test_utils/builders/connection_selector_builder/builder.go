package connection_selector_builder

import (
	"context"
	"database/sql"
	random "math/rand"
	"time"

	proto "nirvanatech.com/nirvana/feature_store/grpc"
	"nirvanatech.com/nirvana/features"
	"nirvanatech.com/nirvana/telematics/connection_selector"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	vehicles_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/connection_selector_builder/vehicles"
	vehicles_data_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/connection_selector_builder/vehicles_data"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet_telematics"
	telematics_db_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
)

type Builder struct {
	FleetTelematicsConsent fleet_telematics.FleetTelematicsConsent
	ConnectionInfo         telematics.ConnectionInfo
	Vehicles               []*data_platform.Vehicle
	ConnectionStatusError  error
	Drivers                []data_platform.Driver
	Tags                   []*data_platform.VehicleGroup
	VehicleStatsMetadata   telematics_db_wrapper.NormFileMetaLog
	FleetTRSFeatureRecord  *features.ProtoFeature[*proto.NirvanaRiskScoreV3]
}

func New() *Builder {
	return &Builder{}
}

func (b *Builder) WithDefaults(clk clock.Clock, numVehicles int, rand *random.Rand) *Builder {
	handleID := uuid_utils.StableUUID("handleID")
	vehicles := vehicles_builder.New().WithDefaultData(numVehicles).Build()
	vehicleStatsMetadata := vehicles_data_builder.New().WithDefaultData(clk, handleID, vehicles, rand).Build()

	return b.WithHandleID(handleID).
		WithFleetID(uuid_utils.StableUUID("fleetID")).
		WithCreatedAt(constants.RefDate).
		WithCreatedBy(uuid_utils.StableUUID("createdBy")).
		WithQuotingConsent(true).
		WithConsentKind(telematics.ConsentKindOAuth).
		WithTSP(telematics.TSPSamsara).
		WithDataProvider(telematics.DataProviderNative).
		WithActivatedAt(null.TimeFrom(constants.RefDate.Add(time.Minute))).
		WithDataPullLegalLimit(null.TimeFrom(constants.RefDate.Add(time.Hour))).
		WithVehicles(vehicles...).
		WithVehicleStatsMetadata(vehicleStatsMetadata).
		WithStatus(telematics.ConnectionStatusConnected)
}

func (b *Builder) WithHandleID(handleID uuid.UUID) *Builder {
	b.FleetTelematicsConsent.HandleID = handleID
	b.ConnectionInfo.HandleId = handleID
	return b
}

func (b *Builder) WithFleetID(fleetID uuid.UUID) *Builder {
	b.FleetTelematicsConsent.FleetID = fleetID
	return b
}

func (b *Builder) WithCreatedBy(createdBy uuid.UUID) *Builder {
	b.FleetTelematicsConsent.CreatedBy = createdBy
	return b
}

func (b *Builder) WithCreatedAt(createdAt time.Time) *Builder {
	b.FleetTelematicsConsent.CreatedAt = createdAt
	return b
}

func (b *Builder) WithQuotingConsent(isQuoting bool) *Builder {
	b.FleetTelematicsConsent.QuotingConsent = isQuoting
	b.FleetTelematicsConsent.SafetyConsent = !isQuoting
	return b
}

func (b *Builder) WithConsentKind(kind telematics.ConsentKind) *Builder {
	b.ConnectionInfo.ConsentKind = kind
	return b
}

func (b *Builder) WithTSP(tsp telematics.TSP) *Builder {
	b.ConnectionInfo.TSP = tsp
	return b
}

func (b *Builder) WithDataProvider(dp telematics.DataProvider) *Builder {
	b.ConnectionInfo.DataProvider = dp
	return b
}

func (b *Builder) WithActivatedAt(activatedAt null.Time) *Builder {
	b.ConnectionInfo.ActivatedAt = activatedAt
	return b
}

func (b *Builder) WithDataPullLegalLimit(limit null.Time) *Builder {
	b.ConnectionInfo.DataPullLegalLimit = limit
	return b
}

func (b *Builder) WithStatus(status telematics.ConnectionStatus) *Builder {
	b.ConnectionInfo.Status = status
	return b
}

func (b *Builder) WithDataProviderHealthStatus(status null.Bool, failedTimes int) *Builder {
	b.ConnectionInfo.DataProviderHealthStatus = status
	b.ConnectionInfo.ConsecutiveFailedHealthChecks = failedTimes
	return b
}

func (b *Builder) WithVehicles(vehicles ...*data_platform.Vehicle) *Builder {
	b.Vehicles = vehicles
	return b
}

func (b *Builder) WithConnectionStatusError(err error) *Builder {
	b.ConnectionStatusError = err
	return b
}

func (b *Builder) WithVehicleStatsMetadata(stats telematics_db_wrapper.NormFileMetaLog) *Builder {
	b.VehicleStatsMetadata = stats
	return b
}

func (b *Builder) WithDrivers(drivers ...data_platform.Driver) *Builder {
	b.Drivers = drivers
	return b
}

func (b *Builder) Build() *connection_selector.DecoratedConnection {
	fetchConnectionInfo := func(ctx context.Context) (*telematics.ConnectionInfo, error) {
		var zeroConnectionInfo telematics.ConnectionInfo
		if b.ConnectionInfo == zeroConnectionInfo {
			return nil, errors.Wrap(sql.ErrNoRows, "no telematics ConnectionInfo found")
		}
		return &b.ConnectionInfo, b.ConnectionStatusError
	}
	fetchVehicles := func(ctx context.Context) ([]*data_platform.Vehicle, error) {
		return b.Vehicles, nil
	}
	fetchDrivers := func(ctx context.Context) ([]data_platform.Driver, error) {
		return b.Drivers, nil
	}
	fetchTags := func(ctx context.Context) ([]*data_platform.VehicleGroup, error) {
		return b.Tags, nil
	}
	fetchStats := func(ctx context.Context) (telematics_db_wrapper.NormFileMetaLog, error) {
		return b.VehicleStatsMetadata, nil
	}
	fetchFleetTRSFeatureRecord := func(ctx context.Context) (*features.ProtoFeature[*proto.NirvanaRiskScoreV3], error) {
		return b.FleetTRSFeatureRecord, nil
	}

	return connection_selector.NewDecoratedConnection(
		b.FleetTelematicsConsent,
		fetchConnectionInfo,
		fetchVehicles,
		fetchDrivers,
		fetchTags,
		fetchStats,
		fetchFleetTRSFeatureRecord,
	)
}
