load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "policy_set",
    srcs = ["builder.go"],
    importpath = "nirvanatech.com/nirvana/common-go/test_utils/builders/policy_set",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy_set",
        "//nirvana/db-api/db_wrappers/policy_set/enums",
        "@com_github_google_uuid//:uuid",
    ],
)
