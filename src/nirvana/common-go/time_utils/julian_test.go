package time_utils

import (
	"testing"
	"time"
)

func TestJulianToDate(test *testing.T) {
	j := DateToJulian(1, time.January, 1)

	for {
		y, m, d := JulianToDate(j)
		if y >= 4000 {
			break
		}
		if j != DateToJulian(y, m, d) {
			test.Errorf("mismatch on %04d-%02d-%02d", y, m, d)
		}
		j++
	}
}

func TestDateToJulian(test *testing.T) {
	t := time.Date(1, time.January, 1, 0, 0, 0, 0, time.UTC)

	for t.Year() < 4000 {
		j := DateToJulian(t.Date())

		y, m, d := JulianToDate(j)

		if y != t.Year() || m != t.Month() || d != t.Day() {
			test.Errorf("mismatch on %s", t.Format("2006-01-02"))
		}
		if j+1 != DateToJulian(y, m, d+1) {
			test.Errorf("mismatch 2 on %s", t.Format("2006-01-02"))
		}
		t = t.AddDate(0, 0, 1)
	}
}
