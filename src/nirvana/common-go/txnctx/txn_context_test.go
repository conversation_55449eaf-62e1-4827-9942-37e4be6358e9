package txnctx

import (
	"context"
	"database/sql"
	"testing"

	"github.com/cockroachdb/errors"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/volatiletech/sqlboiler/v4/boil"

	"github.com/stretchr/testify/assert"
)

func TestSetGetFromContext(t *testing.T) {
	type args struct {
		ctx        context.Context
		txn        boil.ContextExecutor
		defaultTxn boil.ContextExecutor
	}
	tests := []struct {
		name       string
		args       args
		setupFunc  func(args) boil.ContextExecutor
		assertFunc func(*testing.T, args, boil.ContextExecutor)
	}{
		{
			name: "Test GetSetContext with nil txn",
			args: args{
				ctx:        context.Background(),
				txn:        nil,
				defaultTxn: nil,
			},
			setupFunc: func(args args) boil.ContextExecutor {
				newCtx := newContext(args.ctx, args.txn)
				return FromContext(newCtx, args.defaultTxn)
			},
			assertFunc: func(t *testing.T, args args, resTxn boil.ContextExecutor) {
				if resTxn != nil {
					t.Errorf("Expected nil, got %v", resTxn)
				}
			},
		},
		{
			name: "Test GetSetContext with txn",
			args: args{
				ctx:        context.Background(),
				txn:        &sql.Tx{},
				defaultTxn: nil,
			},
			setupFunc: func(args args) boil.ContextExecutor {
				newCtx := newContext(args.ctx, args.txn)
				return FromContext(newCtx, args.defaultTxn)
			},
			assertFunc: func(t *testing.T, args args, resTxn boil.ContextExecutor) {
				assert.NotNil(t, resTxn)
				assert.Equal(t, args.txn, resTxn)
			},
		},
		{
			name: "Test GetSetContext with default txn",
			args: args{
				ctx:        context.Background(),
				txn:        nil,
				defaultTxn: &sql.Tx{},
			},
			setupFunc: func(args args) boil.ContextExecutor {
				newCtx := newContext(args.ctx, args.txn)
				return FromContext(newCtx, args.defaultTxn)
			},
			assertFunc: func(t *testing.T, args args, resTxn boil.ContextExecutor) {
				assert.NotNil(t, resTxn)
				assert.Equal(t, args.defaultTxn, resTxn)
			},
		},
		{
			name: "Test GetContext with no set call",
			args: args{
				ctx:        context.Background(),
				txn:        nil,
				defaultTxn: &sql.Tx{},
			},
			setupFunc: func(args args) boil.ContextExecutor {
				return FromContext(args.ctx, args.defaultTxn)
			},
			assertFunc: func(t *testing.T, args args, resTxn boil.ContextExecutor) {
				assert.NotNil(t, resTxn)
				assert.Equal(t, args.defaultTxn, resTxn)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resTxn := tt.setupFunc(tt.args)
			tt.assertFunc(t, tt.args, resTxn)
		})
	}
}

func TestNewContextWithTx(t *testing.T) {
	type args struct {
		ctx            context.Context
		isolationLevel sql.IsolationLevel
		txDB           boil.ContextBeginner
		txMock         sqlmock.Sqlmock
	}
	var tests = []struct {
		name               string
		setup              func(*args)
		args               args
		multipleBeginTxns  bool
		commitCount        int
		rollbackCount      int
		wantTxInCtx        bool
		wantErrStr         string
		wantCommitErrStr   string
		wantRollbackErrStr string
		wantTx             bool
	}{
		{
			name: "Test BeginTxContext with nil db",
			setup: func(args *args) {
			},
			args: args{
				ctx:  context.Background(),
				txDB: nil,
			},
			wantTxInCtx: false,
			wantErrStr:  "nil sql db",
			wantTx:      false,
		},
		{
			name: "Test BeginTxContext with db with commit",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(nil)
				args.txMock.ExpectCommit().WillReturnError(nil)
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			wantTxInCtx: true,
			commitCount: 1,
			wantTx:      true,
		},
		{
			name: "Test BeginTxContext with db with rollback",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(nil)
				args.txMock.ExpectRollback().WillReturnError(nil)
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			wantTxInCtx:   true,
			rollbackCount: 1,
			wantTx:        true,
		},
		{
			name: "Test BeginTxContext having error in creating transaction",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(errors.New("Dummy Error"))
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			wantTxInCtx: false,
			wantErrStr:  "Dummy Error",
			wantTx:      false,
		},
		{
			name: "Test BeginTxContext with db with multiple begin tx calls",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(nil)
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			multipleBeginTxns: true,
			wantTxInCtx:       true,
			wantErrStr:        "context already has a running transaction",
			wantTx:            false,
		},
		{
			name: "Test BeginTxContext with db with multiple commit calls",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(nil)
				args.txMock.ExpectCommit().WillReturnError(nil)
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			commitCount:      2,
			wantTxInCtx:      true,
			wantErrStr:       "",
			wantCommitErrStr: "sql: transaction has already been committed or rolled back",
			wantTx:           true,
		},
		{
			name: "Test BeginTxContext with db with multiple rollback calls",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(nil)
				args.txMock.ExpectRollback().WillReturnError(nil)
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			rollbackCount:      2,
			wantTxInCtx:        true,
			wantErrStr:         "",
			wantRollbackErrStr: "sql: transaction has already been committed or rolled back",
			wantTx:             true,
		},
		{
			name: "Test BeginTxContext with db with commit and rollback calls",
			setup: func(args *args) {
				args.txDB, args.txMock, _ = sqlmock.New()
				args.txMock.ExpectBegin().WillReturnError(nil)
				args.txMock.ExpectCommit().WillReturnError(nil)
				args.txMock.ExpectRollback().WillReturnError(nil)
			},
			args: args{
				ctx:            context.Background(),
				isolationLevel: sql.LevelSerializable,
			},
			commitCount:        1,
			rollbackCount:      1,
			wantTxInCtx:        true,
			wantErrStr:         "",
			wantRollbackErrStr: "sql: transaction has already been committed or rolled back",
			wantTx:             true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(&tt.args)
			got, tx, err := BeginTxContext(tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
			if tt.multipleBeginTxns {
				// Nested transaction
				got, tx, err = BeginTxContext(got, tt.args.txDB, tt.args.isolationLevel)
			}
			assert.Equalf(t, tt.wantTxInCtx, got.Value(ctxExecKey) != nil, "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
			checkError(t, err, tt.wantErrStr, "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
			assert.Equal(t, tt.wantTx, tx != nil, "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
			if tx != nil {
				commitOrRollbackCalled := false
				for i := 0; i < tt.commitCount; i++ {
					err := tx.Commit()
					if commitOrRollbackCalled {
						checkError(t, err, tt.wantCommitErrStr, "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
					} else {
						checkError(t, err, "", "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
					}
					commitOrRollbackCalled = true
				}
				for i := 0; i < tt.rollbackCount; i++ {
					err = tx.Rollback()
					if commitOrRollbackCalled {
						checkError(t, err, tt.wantRollbackErrStr, "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
					} else {
						checkError(t, err, "", "BeginTxContext(%v, %v, %v)", tt.args.ctx, tt.args.txDB, tt.args.isolationLevel)
					}
					commitOrRollbackCalled = true
				}
			}
		})
	}
}

func checkError(t *testing.T, err error, wantErrStr string, msgAndArgs ...interface{}) {
	if wantErrStr != "" {
		assert.EqualError(t, err, wantErrStr, msgAndArgs...)
	} else {
		assert.NoError(t, err, msgAndArgs...)
	}
}
