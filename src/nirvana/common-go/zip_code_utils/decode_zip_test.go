package zip_code_utils

import (
	"context"
	"reflect"
	"testing"
)

// TestD<PERSON>ode<PERSON><PERSON> tests the DecodeZip function by using a set of test cases.
// which includes valid and invalid zip codes.
func TestDecodeZip(t *testing.T) {
	tests := []struct {
		name    string
		zipCode string
		want    *DecodedZipCode
		wantErr bool
	}{
		{
			name:    "valid zip code",
			zipCode: "10001",
			want: &DecodedZipCode{
				State:      "New York",
				StateCode:  "NY",
				City:       "New York",
				CountyName: "New York",
			},
			wantErr: false,
		},
		{
			name:    "invalid zip code",
			zipCode: "1000",
			want:    nil,
			wantErr: true,
		},
		{
			name:    "valid zip code",
			zipCode: "40202",
			want: &DecodedZipCode{
				State:      "Kentucky",
				StateCode:  "KY",
				City:       "Louisville",
				CountyName: "Jefferson",
			},
			wantErr: false,
		},
		{
			name:    "desmoines zip code",
			zipCode: "50309",
			want: &DecodedZipCode{
				State:      "Iowa",
				StateCode:  "IA",
				City:       "Des Moines",
				CountyName: "Polk",
			},
			wantErr: false,
		},
		{
			name:    "invalid zip code",
			zipCode: "1009990",
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := DecodeZip(context.Background(), tt.zipCode)
			if (err != nil) != tt.wantErr {
				t.Errorf("DecodeZip() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DecodeZip() got = %v, want %v", got, tt.want)
			}
		})
	}
}
