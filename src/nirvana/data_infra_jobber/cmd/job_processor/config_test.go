package main

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/appfx"
)

func TestFxApp(t *testing.T) {
	for _, env := range []config.Env{config.Env_PROD /* config.Env_DEV,*/, config.Env_TEST} {
		cfg, err := config.GetForEnv(env)
		require.NoError(t, err)
		require.NoError(t, appfx.NewGRPCAppBuilderWithInjectedAddress(
			constants.DataInfraJobProcessor, cfg.Env, nil, options(cfg)...,
		).Validate())
	}
}
