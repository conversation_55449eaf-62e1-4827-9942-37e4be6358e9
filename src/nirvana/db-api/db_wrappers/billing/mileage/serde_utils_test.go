package mileage

import (
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/billing/bpid"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/billing/mileage/enums"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func TestMileageSummarySerde(t *testing.T) {
	ms := MileageSummary{
		Id:                 uuid.New(),
		MileageType:        enums.MileageTypeIFTA,
		BillingPolicyID:    bpid.NewBillingPolicyId("0100010", 2024),
		Mileage:            1_000,
		BillingPeriodStart: time_utils.NewDate(2024, time.April, 1),
		BillingPeriodEnd:   time_utils.NewDate(2024, time.April, 30),
		Notes:              NotesFromProjectedMileage,
		JobRunId:           pointer_utils.ToPointer(jtypes.NewJobRunId("SomeJobID::", 1)),
		CreatedBy:          pointer_utils.ToPointer(uuid.New()),
		CreatedAt:          time_utils.NewDate(2024, time.May, 5).ToTime(),
	}

	serialized, err := mileageSummaryToDB(ms)
	require.NoError(t, err)
	deserialized, err := mileageSummaryFromDB(serialized)
	require.NoError(t, err)

	assert.EqualValues(t, ms, *deserialized)
}

func TestMileageDetailSerde(t *testing.T) {
	vin := "Vin123"
	md := MileageDetail{
		Id:                 uuid.New(),
		MileageType:        enums.MileageTypeIFTA,
		BillingPolicyID:    bpid.NewBillingPolicyId("0100010", 2024),
		VIN:                vin,
		Mileage:            1_000,
		BillingPeriodStart: time_utils.NewDate(2024, time.April, 1),
		BillingPeriodEnd:   time_utils.NewDate(2024, time.April, 30),
		Notes:              "Random notes",
		MileageSummaryId:   uuid.New(),
		CreatedBy:          pointer_utils.ToPointer(uuid.New()),
		CreatedAt:          time_utils.NewDate(2024, time.May, 5).ToTime(),
	}

	serialized, err := mileageDetailToDB(md)
	require.NoError(t, err)
	deserialized, err := mileageDetailFromDB(serialized)
	require.NoError(t, err)

	// we explicitly assert that we're transforming VINs to uppercase
	md.VIN = strings.ToUpper(vin)
	assert.EqualValues(t, md, *deserialized)
}
