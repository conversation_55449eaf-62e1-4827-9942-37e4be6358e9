package pipeline_run

import (
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/billing/bpid"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type PipelineRun struct {
	Id               uuid.UUID
	JobRunId         jtypes.JobRunId
	TriggeredBy      uuid.UUID
	BillingPolicyIds []bpid.BillingPolicyId
	IntervalStart    time_utils.Date
	IntervalEnd      time_utils.Date

	// IsDryRun will be set to TRUE if the pipeline was run for non-authoritative purposes (e.g. debugging), and
	// FALSE otherwise.
	IsDryRun bool

	CreatedAt time.Time
}

// New returns a new instance of PipelineRun.
func New(
	jobRunId jtypes.JobRunId,
	triggeredBy uuid.UUID,
	billingPolicyIds []bpid.BillingPolicyId,
	intervalStart time_utils.Date,
	intervalEnd time_utils.Date,
) *PipelineRun {
	return &PipelineRun{
		Id:               uuid.New(),
		JobRunId:         jobRunId,
		TriggeredBy:      triggeredBy,
		BillingPolicyIds: billingPolicyIds,
		IntervalStart:    intervalStart,
		IntervalEnd:      intervalEnd,
		IsDryRun:         true,
		CreatedAt:        time.Now(),
	}
}

func (run *PipelineRun) WithDryRun(isDryRun bool) *PipelineRun {
	run.IsDryRun = isDryRun
	return run
}

// Validate returns an error if the PipelineRun is not valid.
func (run *PipelineRun) Validate() error {
	if run.Id == uuid.Nil {
		return errors.New("zero-value ID")
	}

	if run.JobRunId == jtypes.InvalidJobRunId {
		return errors.New("invalid JobRunId")
	}

	if run.TriggeredBy == uuid.Nil {
		return errors.New("zero-value JobRunId")
	}

	if len(run.BillingPolicyIds) == 0 {
		return errors.New("zero-value BillingPolicyIds")
	}

	emptyDate := time_utils.Date{}
	if run.IntervalStart == emptyDate {
		return errors.New("zero-value IntervalStart")
	}

	if run.IntervalEnd == emptyDate {
		return errors.New("zero-value IntervalEnd")
	}

	if run.IntervalStart.After(run.IntervalEnd) {
		return errors.New("intervalStart cannot be after intervalEnd")
	}
	return nil
}
