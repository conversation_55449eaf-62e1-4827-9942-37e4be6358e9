package pipeline_run_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/billing/bpid"
	pipeline_run_builder "nirvanatech.com/nirvana/billing/builders/pipeline_run"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/billing/pipeline_run"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func TestPipelinerunWrapperTestSuite(t *testing.T) {
	suite.Run(t, new(pipelineRunWrapperTestSuite))
}

type pipelineRunWrapperTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	fxapp *fxtest.App

	wrapper pipeline_run.DataWrapper
}

func (m *pipelineRunWrapperTestSuite) SetupTest() {
	var env struct {
		fx.In
		Wrapper pipeline_run.DataWrapper
	}

	m.fxapp = testloader.RequireStart(m.T(), &env)
	m.wrapper = env.Wrapper
}

func (m *pipelineRunWrapperTestSuite) TearDownTest() {
	m.fxapp.RequireStop()
}

func (m *pipelineRunWrapperTestSuite) TestInsertPipelineRun() {
	ctx := context.Background()
	jobRunId := jtypes.NewJobRunId(jtypes.JobId(uuid.New().String())+"::", 1)
	triggeredBy := uuid.New()
	billingPolicyIds := []bpid.BillingPolicyId{
		bpid.NewBillingPolicyId("1111111", int16(2024)),
		bpid.NewBillingPolicyId("2222222", int16(2024)),
	}
	intervalStart := time_utils.NewDate(2024, time.August, 15)
	intervalEnd := time_utils.NewDate(2024, time.September, 15)

	pipelineRun := pipeline_run_builder.New().
		WithDefaults().
		WithJobRunId(jobRunId).
		WithTriggeredBy(triggeredBy).
		WithBillingPolicyIds(billingPolicyIds).
		WithIntervalStart(intervalStart).
		WithIntervalEnd(intervalEnd).
		WithIsDryRun(false).
		Build()
	err := m.wrapper.InsertPipelineRun(ctx, *pipelineRun)
	m.Require().NoError(err)

	pipelineRun2 := pipeline_run_builder.New().
		WithDefaults().
		WithJobRunId(jobRunId).
		WithTriggeredBy(triggeredBy).
		WithBillingPolicyIds(billingPolicyIds).
		WithIntervalStart(intervalStart).
		WithIntervalEnd(intervalEnd).
		Build()
	err = m.wrapper.InsertPipelineRun(ctx, *pipelineRun2)
	// Check jobRunId uniqueness
	m.Require().Error(err)

	pipelineRuns, err := m.wrapper.GetPipelineRuns(ctx)
	m.Require().NoError(err)
	m.Require().Len(pipelineRuns, 1)
	m.assertPipelineRunsEqual(*pipelineRun, pipelineRuns[0])
}

func (m *pipelineRunWrapperTestSuite) TestGetPipelineRun() {
	ctx := context.Background()

	run := pipeline_run_builder.New().WithDefaults().Build()
	m.Require().NoError(m.wrapper.InsertPipelineRun(ctx, *run))

	got, err := m.wrapper.GetPipelineRun(ctx, run.Id)
	m.Require().NoError(err)
	m.assertPipelineRunsEqual(*run, *got)
}

func (m *pipelineRunWrapperTestSuite) TestGetPipelineRuns() {
	ctx := context.Background()

	createdAt := time_utils.NewDate(2024, time.August, 15)
	pipelineRun1 := pipeline_run_builder.New().WithDefaults().WithCreatedAt(createdAt.ToTime()).Build()
	err := m.wrapper.InsertPipelineRun(ctx, *pipelineRun1)
	m.Require().NoError(err)

	pipelineRun2 := pipeline_run_builder.New().WithDefaults().WithCreatedAt(createdAt.AddDate(0, 0, 1).ToTime()).Build()
	err = m.wrapper.InsertPipelineRun(ctx, *pipelineRun2)
	m.Require().NoError(err)

	pipelineRuns, err := m.wrapper.GetPipelineRuns(ctx, pipeline_run.OrderByCreatedAtDesc())
	m.Require().NoError(err)
	m.Require().Len(pipelineRuns, 2)

	wantPipelineRuns := []pipeline_run.PipelineRun{*pipelineRun2, *pipelineRun1}
	for i := range wantPipelineRuns {
		want := wantPipelineRuns[i]
		got := pipelineRuns[i]
		m.assertPipelineRunsEqual(want, got)
	}
}

func (m *pipelineRunWrapperTestSuite) assertPipelineRunsEqual(want, got pipeline_run.PipelineRun) {
	m.Equal(want.Id.String(), got.Id.String())
	m.Equal(want.JobRunId.String(), got.JobRunId.String())
	m.Equal(want.TriggeredBy.String(), got.TriggeredBy.String())
	m.ElementsMatch(want.BillingPolicyIds, got.BillingPolicyIds)
	m.Equal(want.IntervalStart, got.IntervalStart)
	m.Equal(want.IntervalEnd, got.IntervalEnd)
	m.Equal(want.IsDryRun, got.IsDryRun)
}
