load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "census_audit_log",
    srcs = [
        "fx.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/datagov/census_audit_log",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_wrappers/datagov",
        "//nirvana/fmcsa/db_models/datagov",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "census_audit_log_test",
    srcs = ["wrapper_test.go"],
    deps = [
        ":census_audit_log",
        "//nirvana/fmcsa/db_models/datagov",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/testloader",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
