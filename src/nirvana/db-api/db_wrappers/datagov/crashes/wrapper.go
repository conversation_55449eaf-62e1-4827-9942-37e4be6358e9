package crashes

import (
	"context"
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"nirvanatech.com/nirvana/common-go/postgres_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov"
	"nirvanatech.com/nirvana/fmcsa/datagov/models"
	datagov_models "nirvanatech.com/nirvana/fmcsa/db_models/datagov"
)

type DataWrapper struct {
	db db_api.FmcsaRW
}

var _ datagov.DBWrapper[*datagov_models.Crash, models.CrashID] = (*DataWrapper)(nil)

func New(db db_api.FmcsaRW) *DataWrapper {
	return &DataWrapper{
		db: db,
	}
}

// GetByDOTAndInterval retrieves all crash records associated with a given DOT number within the specified time interval.
// If includeDeleted is true, it includes records that have been marked as deleted.
// Duplicate records (based on DotNumber, ReportNumber, and ReportSeqNumber) are deduplicated,
// keeping only the most recent record by ReportDate.
// The results are limited to crashes with ReportDate between timeInterval.Start and timeInterval.End.
func (w *DataWrapper) GetByDOTAndInterval(ctx context.Context, dotNumber int64, includeDeleted bool, timeInterval time_utils.Interval) (models.Crashes, error) {
	columns := datagov_models.CrashColumns

	queryStr := fmt.Sprintf(`
SELECT DISTINCT ON (%[1]s, %[2]s, %[3]s) *
FROM %[5]s
WHERE %[1]s = $1
AND %[4]s BETWEEN $2 AND $3

`,
		columns.DotNumber,                            // [1]
		columns.ReportNumber,                         // [2]
		columns.ReportSeqNumber,                      // [3]
		columns.ReportDate,                           // [4]
		"datagov."+datagov_models.TableNames.Crashes, // [5]
	)
	if !includeDeleted {
		queryStr += fmt.Sprintf(" AND %s IS NULL", columns.DeletedAt)
	}

	// Ensure ORDER BY starts with DISTINCT ON columns
	queryStr += fmt.Sprintf(" ORDER BY %s, %s, %s, %s DESC;",
		columns.DotNumber, columns.ReportNumber, columns.ReportSeqNumber, columns.ReportDate)

	// Ensure valid time interval
	if !timeInterval.IsValid() {
		return nil, errors.New("invalid time interval")
	}

	var crashes []*datagov_models.Crash
	err := queries.Raw(queryStr, dotNumber, timeInterval.Start, timeInterval.End).
		Bind(ctx, w.db, &crashes)
	if err != nil {
		return nil, errors.Wrap(err, "database error fetching crash records")
	}

	return slice_utils.MapErr(crashes, models.CrashFromDB)
}

func (w *DataWrapper) GetRowMetadata(ctx context.Context) ([]datagov.Metadata[models.CrashID], error) {
	mods := []qm.QueryMod{
		qm.Select(datagov_models.CrashColumns.ID,
			datagov_models.CrashColumns.Checksum,
			datagov_models.CrashColumns.DeletedAt,
		),
		qm.OrderBy(datagov_models.CrashColumns.ID),
	}

	type crashMetadata struct {
		ID        int64     `boil:"id" json:"id" toml:"id" yaml:"id"`
		Checksum  string    `boil:"checksum" json:"checksum" toml:"checksum" yaml:"checksum"`
		DeletedAt null.Time `boil:"deleted_at" json:"deleted_at,omitempty" toml:"deleted_at" yaml:"deleted_at,omitempty"`
	}
	var allMetadata []*crashMetadata
	if err := datagov_models.Crashes(mods...).Bind(ctx, w.db, &allMetadata); err != nil {
		return nil, errors.Wrapf(err, "failed to get crash records")
	}
	return slice_utils.Map(allMetadata, func(crash *crashMetadata) datagov.Metadata[models.CrashID] {
		return datagov.Metadata[models.CrashID]{
			PrimaryKey: models.CrashID(crash.ID),
			Checksum:   crash.Checksum,
			DeletedAt:  crash.DeletedAt,
		}
	}), nil
}

func (w *DataWrapper) Upsert(ctx context.Context, crash *datagov_models.Crash) error {
	conflictColumns := []string{
		datagov_models.CrashColumns.ID,
	}
	updateCols := boil.Blacklist(append(conflictColumns, datagov_models.CrashColumns.CreatedAt)...)
	insertColumns := boil.Infer()
	if err := crash.Upsert(ctx, w.db, true, conflictColumns, updateCols, insertColumns); err != nil {
		return errors.Wrapf(err, "failed to upsert %s %v", datagov_models.TableNames.Crashes, crash.ID)
	}

	return nil
}

func (w *DataWrapper) SoftDelete(ctx context.Context, id models.CrashID, checksum string, deletedAt time.Time) error {
	crash, err := datagov_models.Crashes(
		datagov_models.CrashWhere.ID.EQ(int64(id)),
		datagov_models.CrashWhere.Checksum.EQ(checksum),
	).One(ctx, w.db)
	if err != nil {
		return errors.Wrapf(err, "failed to get crash record %v", id)
	}
	crash.DeletedAt = null.TimeFrom(deletedAt)
	return w.Upsert(ctx, crash)
}

func (w *DataWrapper) NewBatchWriter() (*postgres_utils.BatchWriterSQLBoiler[*datagov_models.Crash], error) {
	tableName := fmt.Sprintf("datagov.%s", datagov_models.TableNames.Crashes)
	return postgres_utils.NewBatchWriterSQLBoiler[*datagov_models.Crash](w.db.DB, datagov.BatchSize, tableName, false)
}
