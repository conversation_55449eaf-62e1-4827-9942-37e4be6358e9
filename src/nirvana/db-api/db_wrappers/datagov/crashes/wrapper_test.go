package crashes

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/time_utils"
	datagov_models "nirvanatech.com/nirvana/fmcsa/db_models/datagov"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestDataWrapper_GetByDOT(t *testing.T) {
	ctx := context.Background()
	const dotNumber = int64(123456)

	var env struct {
		fx.In

		DataWrapper *DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	require.NotNil(t, env.DataWrapper)

	now := time.Now()

	activeCrash := &datagov_models.Crash{
		ID:              1,
		DotNumber:       dotNumber,
		ReportDate:      now,
		ReportNumber:    "100",
		ReportSeqNumber: 1,
	}
	require.NoError(t, env.DataWrapper.Upsert(ctx, activeCrash))

	// Duplicate of activeCrash but older date (should be deduped)
	duplicateActiveCrash := &datagov_models.Crash{
		ID:              2,
		DotNumber:       dotNumber,
		ReportDate:      now.Add(-48 * time.Hour),
		ReportNumber:    "100",
		ReportSeqNumber: 1,
	}
	require.NoError(t, env.DataWrapper.Upsert(ctx, duplicateActiveCrash))

	deletedCrash := &datagov_models.Crash{
		ID:              3,
		DotNumber:       dotNumber,
		ReportDate:      now.Add(-24 * time.Hour),
		DeletedAt:       null.TimeFrom(now),
		ReportNumber:    "101",
		ReportSeqNumber: 1,
	}
	require.NoError(t, env.DataWrapper.Upsert(ctx, deletedCrash))

	otherDOTCrash := &datagov_models.Crash{
		ID:              4,
		DotNumber:       999999,
		ReportDate:      now.Add(-48 * time.Hour),
		ReportNumber:    "102",
		ReportSeqNumber: 1,
	}
	require.NoError(t, env.DataWrapper.Upsert(ctx, otherDOTCrash))

	tests := []struct {
		name           string
		dotNumber      int64
		includeDeleted bool
		interval       time_utils.Interval
		wantIDs        []int64
		wantErr        bool
	}{
		{
			name:      "only active crashes (deduping) - valid interval",
			dotNumber: dotNumber,
			interval: time_utils.Interval{
				Start: now.Add(-72 * time.Hour),
				End:   now,
			},
			includeDeleted: false,
			wantIDs:        []int64{1},
		},
		{
			name:      "include deleted crashes - valid interval",
			dotNumber: dotNumber,
			interval: time_utils.Interval{
				Start: now.Add(-72 * time.Hour),
				End:   now,
			},
			includeDeleted: true,
			wantIDs:        []int64{1, 3},
		},
		{
			name:      "no crashes for unknown DOT - valid interval",
			dotNumber: 111111,
			interval: time_utils.Interval{
				Start: now.Add(-72 * time.Hour),
				End:   now,
			},
			includeDeleted: false,
			wantIDs:        []int64{},
		},
		{
			name:      "no crashes - outside of interval",
			dotNumber: dotNumber,
			interval: time_utils.Interval{
				Start: now.Add(-120 * time.Hour),
				End:   now.Add(-100 * time.Hour),
			},
			includeDeleted: false,
			wantIDs:        []int64{},
		},
		{
			name:      "invalid interval",
			dotNumber: dotNumber,
			interval: time_utils.Interval{
				Start: now,
				End:   now.Add(-24 * time.Hour),
			},
			includeDeleted: false,
			wantIDs:        []int64{},
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			crshs, err := env.DataWrapper.GetByDOTAndInterval(ctx, tt.dotNumber, tt.includeDeleted, tt.interval)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)

			var gotIDs []int64
			for _, crash := range crshs {
				gotIDs = append(gotIDs, crash.ID)
			}
			assert.ElementsMatch(t, tt.wantIDs, gotIDs)
		})
	}
}
