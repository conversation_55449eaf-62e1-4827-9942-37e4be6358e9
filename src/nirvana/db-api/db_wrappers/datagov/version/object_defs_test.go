package version_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	builder "nirvanatech.com/nirvana/common-go/test_utils/builders/datagov/version"
)

func TestValidate(t *testing.T) {
	testCases := []struct {
		name    string
		version *builder.Builder
		wantErr bool
	}{
		{
			name:    "valid version with defaults",
			version: builder.New().WithDefaults(),
			wantErr: false,
		},
		{
			name:    "with zero-value Table Name",
			version: builder.New().WithDefaults().WithTableName(""),
			wantErr: true,
		},
		{
			name:    "with zero-value PulledAt",
			version: builder.New().WithDefaults().WithPulledAt(time.Time{}),
			wantErr: true,
		},
		{
			name:    "with invalid PulledAt",
			version: builder.New().WithDefaults().WithPulledAt(time.Now().AddDate(1, 1, 1)),
			wantErr: true,
		},
	}
	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.version.Build()
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
		})
	}
}
