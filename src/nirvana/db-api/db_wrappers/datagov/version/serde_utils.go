package version

import (
	datagov_models "nirvanatech.com/nirvana/fmcsa/db_models/datagov"
)

func VersionFromDb(dbVersion *datagov_models.Version) *Version {
	if dbVersion == nil {
		return nil
	}
	return NewVersion(dbVersion.ID, dbVersion.TableName, dbVersion.PulledAt, dbVersion.Valid)
}

func VersionToDb(version *Version) *datagov_models.Version {
	if version == nil {
		return nil
	}
	return &datagov_models.Version{
		ID:        version.Id,
		TableName: version.TableName,
		PulledAt:  version.PulledAt.UTC(),
		Valid:     version.Valid,
	}
}
