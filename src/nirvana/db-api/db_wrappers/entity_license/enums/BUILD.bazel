load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "enums",
    srcs = [
        "entitylicensestatus_enumer.go",
        "entitytype_enumer.go",
        "enums.go",
        "proto_converters.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/entity_license/enums",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/fleet/model",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "enums_test",
    srcs = ["proto_converters_test.go"],
    embed = [":enums"],
    deps = [
        "//nirvana/fleet/model",
        "@com_github_stretchr_testify//assert",
    ],
)
