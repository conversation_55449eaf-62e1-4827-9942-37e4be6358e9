package entity_license

import (
	"context"

	"nirvanatech.com/nirvana/common-go/us_states"

	"github.com/google/uuid"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock/mock_wrapper.go -package mock_entity_license -source interfaces.go Wrapper
type Wrapper interface {
	// GetByEntityIDAndUSState retrieves a EntityLicense object for the entity.
	GetByEntityIDAndUSState(ctx context.Context, entityID uuid.UUID, usState us_states.USState) (*EntityLicense, error)

	// Insert adds a new EntityLicense object to the data store.
	Insert(ctx context.Context, entityLicense *EntityLicense) error

	// UpdateForUSState modifies an existing EntityLicense object for the entity and state.
	UpdateForUSState(ctx context.Context, entityID uuid.UUID, usState us_states.USState, updaterFunc UpdaterFunc) error
}
