package fleet_telematics

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type FleetTelematicsWrapperTestSuite struct {
	suite.Suite
	fxapp             *fxtest.App
	Wrapper           Wrapper
	ctx               context.Context
	testApplicationId uuid.UUID
}

func TestHandleFleetTelematicsWrapperTestSuite(t *testing.T) {
	suite.Run(t, new(FleetTelematicsWrapperTestSuite))
}

func (s *FleetTelematicsWrapperTestSuite) SetupTest() {
	var env struct {
		fx.In

		DataWrapper Wrapper
	}
	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.Wrapper = env.DataWrapper
	s.ctx = context.Background()
}

func (s *FleetTelematicsWrapperTestSuite) TearDownTest() {
	defer s.fxapp.RequireStop()
}

func (s *FleetTelematicsWrapperTestSuite) TestFleetTelematicsConsentInsert() {
	mockFleetId := uuid.New()
	mockConsent := FleetTelematicsConsent{
		HandleID:       uuid.New(),
		FleetID:        mockFleetId,
		SafetyConsent:  true,
		QuotingConsent: true,
		CreatedBy:      uuid.New(),
	}
	err := s.Wrapper.InsertFleetTelematicsConsent(s.ctx, &mockConsent)
	s.NoError(err)
	consents, err := s.Wrapper.GetAllFleetTelematicsConsent(s.ctx, mockFleetId)
	s.NoError(err)
	s.Equal(1, len(consents))
}

func (s *FleetTelematicsWrapperTestSuite) TestFleetTelematicsConsentUpsert() {
	mockFleetId := uuid.New()
	initialCreatedBy := uuid.New()
	mockConsent := FleetTelematicsConsent{
		HandleID:       uuid.New(),
		FleetID:        mockFleetId,
		SafetyConsent:  true,
		QuotingConsent: true,
		CreatedBy:      initialCreatedBy,
	}

	// We test the insert operation of the upsert
	err := s.Wrapper.UpsertFleetTelematicsConsent(s.ctx, &mockConsent)
	s.NoError(err)
	consents, err := s.Wrapper.GetAllFleetTelematicsConsent(s.ctx, mockFleetId)
	s.NoError(err)
	s.Equal(1, len(consents))

	// We test the insert operation of the upsert
	mockConsent.SafetyConsent = false
	mockConsent.CreatedBy = uuid.New()
	err = s.Wrapper.UpsertFleetTelematicsConsent(s.ctx, &mockConsent)
	s.NoError(err)

	// Check that no new consents have been created
	consents, err = s.Wrapper.GetAllFleetTelematicsConsent(s.ctx, mockFleetId)
	s.NoError(err)
	s.Equal(1, len(consents))
	// Check that existing consent was updated
	updatedConsent := consents[0]
	s.NotNil(updatedConsent)
	s.Equal(false, updatedConsent.SafetyConsent)

	// Check field CreatedBy was not updated
	s.Equal(initialCreatedBy, updatedConsent.CreatedBy)

	mockConsent2 := FleetTelematicsConsent{
		HandleID:       uuid.New(),
		FleetID:        mockFleetId,
		SafetyConsent:  true,
		QuotingConsent: true,
		CreatedBy:      uuid.New(),
	}
	// We test the insert operation on same FleetId but different handleId
	err = s.Wrapper.UpsertFleetTelematicsConsent(s.ctx, &mockConsent2)
	s.NoError(err)
	consents, err = s.Wrapper.GetAllFleetTelematicsConsent(s.ctx, mockFleetId)
	s.NoError(err)
	s.Equal(2, len(consents))
}
