load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "fmcsa",
    srcs = [
        "client_mock.go",
        "doc.go",
        "enums.go",
        "errors.go",
        "fx.go",
        "identifiertype_enumer.go",
        "interfaces.go",
        "puller_enums.go",
        "pullername_enumer.go",
        "related_entities_utils.go",
        "serde_utils.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/sort_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/db-api",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/db_models/fmcsa",
        "//nirvana/fmcsa/enums",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/safety/scores/generate_percentiles/models",
        "//nirvana/safety/scores/objective_grade/enums",
        "//nirvana/safety/scores/objective_grade/models",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_jinzhu_copier//:copier",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//types",
        "@io_opentelemetry_go_otel//attribute",
        "@io_opentelemetry_go_otel_trace//:trace",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "fmcsa_test",
    srcs = [
        "export_test.go",
        "serde_utils_test.go",
        "wrapper_test.go",
    ],
    embed = [":fmcsa"],
    embedsrcs = [
        "data/mock_task_run_info_data.sql",
        "data/computed_percentiles_data.sql",
        "safer_scraper_response.json",
        "data/mock_safer_scraper_data.sql",
        "data/computed_measures_test_data.sql",
    ],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/db_models/fmcsa",
        "//nirvana/fmcsa/dot_details/enums",
        "//nirvana/fmcsa/models",
        "//nirvana/fmcsa/scraper",
        "//nirvana/infra/fx/testfixtures/common_fixture",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/safety/scores/generate_percentiles/models",
        "//nirvana/safety/scores/objective_grade/enums",
        "//nirvana/safety/scores/objective_grade/models",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//types",
        "@org_uber_go_fx//:fx",
    ],
)
