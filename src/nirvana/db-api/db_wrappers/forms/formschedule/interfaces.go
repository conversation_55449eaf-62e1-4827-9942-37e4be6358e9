package formschedule

import (
	"context"

	"nirvanatech.com/nirvana/forms/model"
)

type FormUpdateFn func(form *model.FormSchedule) (*model.FormSchedule, error)

//go:generate go run go.uber.org/mock/mockgen -destination mock_wrapper.go -package formschedule -typed nirvanatech.com/nirvana/db-api/db_wrappers/forms/formschedule DataWrapper
type DataWrapper interface {
	// InsertFormSchedule inserts a FormSchedule into the db.
	InsertFormSchedule(ctx context.Context, form *model.FormSchedule) error

	// GetFormSchedule fetches the forms from the DB
	GetFormSchedule(ctx context.Context, request *model.GetFormScheduleRequest) ([]model.FormSchedule, error)

	// UpdateFormSchedule updates a FormSchedule into the db.
	UpdateFormSchedule(ctx context.Context, fullFormCode string, updateFn FormUpdateFn) error

	// GetFormScheduleByFullFormCode fetches the form schedule by full form code
	GetFormScheduleByFullFormCode(ctx context.Context, code string) (*model.FormSchedule, error)
}
