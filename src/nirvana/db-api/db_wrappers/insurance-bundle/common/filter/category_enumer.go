// Code generated by "enumer -type=Category -trimprefix=Category"; DO NOT EDIT.

package filter

import (
	"fmt"
	"strings"
)

const _CategoryName = "InsuranceBundleFilterInsuranceBundleSegmentFilterPolicyFilter"

var _CategoryIndex = [...]uint8{0, 21, 49, 61}

const _CategoryLowerName = "insurancebundlefilterinsurancebundlesegmentfilterpolicyfilter"

func (i Category) String() string {
	i -= 1
	if i < 0 || i >= Category(len(_CategoryIndex)-1) {
		return fmt.Sprintf("Category(%d)", i+1)
	}
	return _CategoryName[_CategoryIndex[i]:_CategoryIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _CategoryNoOp() {
	var x [1]struct{}
	_ = x[CategoryInsuranceBundleFilter-(1)]
	_ = x[CategoryInsuranceBundleSegmentFilter-(2)]
	_ = x[CategoryPolicyFilter-(3)]
}

var _CategoryValues = []Category{CategoryInsuranceBundleFilter, CategoryInsuranceBundleSegmentFilter, CategoryPolicyFilter}

var _CategoryNameToValueMap = map[string]Category{
	_CategoryName[0:21]:       CategoryInsuranceBundleFilter,
	_CategoryLowerName[0:21]:  CategoryInsuranceBundleFilter,
	_CategoryName[21:49]:      CategoryInsuranceBundleSegmentFilter,
	_CategoryLowerName[21:49]: CategoryInsuranceBundleSegmentFilter,
	_CategoryName[49:61]:      CategoryPolicyFilter,
	_CategoryLowerName[49:61]: CategoryPolicyFilter,
}

var _CategoryNames = []string{
	_CategoryName[0:21],
	_CategoryName[21:49],
	_CategoryName[49:61],
}

// CategoryString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func CategoryString(s string) (Category, error) {
	if val, ok := _CategoryNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _CategoryNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to Category values", s)
}

// CategoryValues returns all values of the enum
func CategoryValues() []Category {
	return _CategoryValues
}

// CategoryStrings returns a slice of all String values of the enum
func CategoryStrings() []string {
	strs := make([]string, len(_CategoryNames))
	copy(strs, _CategoryNames)
	return strs
}

// IsACategory returns "true" if the value is listed in the enum definition. "false" otherwise
func (i Category) IsACategory() bool {
	for _, v := range _CategoryValues {
		if i == v {
			return true
		}
	}
	return false
}
