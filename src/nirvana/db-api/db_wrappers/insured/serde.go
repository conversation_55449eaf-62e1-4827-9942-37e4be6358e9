package insured

import (
	"google.golang.org/protobuf/encoding/protojson"

	"nirvanatech.com/nirvana/common-go/proto"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/slice_utils"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_models/insured"
	"nirvanatech.com/nirvana/insured/model"
)

func convertInsuredToDBRepresentation(i *model.Insured) (*insured.Insured, error) {
	marshalledAddress, err := protojson.Marshal(i.Address)
	if err != nil {
		return nil, errors.Wrap(err, "could not marshal address")
	}
	sliceCodec := slice_utils.NewStringSliceCodec[string]()
	encodedExternalIdentifierValue, err := sliceCodec.Encode(i.ExternalIdentifier.Value, "")
	if err != nil {
		return nil, errors.Wrap(err, "could not encode external identifier")
	}

	resp := &insured.Insured{
		ID:                             i.Id,
		Name:                           i.Name.BusinessName,
		Address:                        marshalledAddress,
		ExternalIdentifierType:         i.ExternalIdentifier.Type.HumanReadableString(),
		ExternalIdentifierEncodedValue: encodedExternalIdentifierValue,
	}
	if i.ContactInfo.Email != "" {
		resp.Email = null.StringFrom(i.ContactInfo.Email)
	}
	if i.ContactInfo.Phone != "" {
		resp.Phone = null.StringFrom(i.ContactInfo.Phone)
	}

	return resp, nil
}

//nolint:unused
func convertDBRepresentationToInsured(i *insured.Insured) (*model.Insured, error) {
	var address proto.Address
	if err := protojson.Unmarshal(i.Address, &address); err != nil {
		return nil, errors.Wrap(err, "could not unmarshal address")
	}
	sliceCodec := slice_utils.NewStringSliceCodec[string]()
	decodedExternalIdentifierValue := sliceCodec.Decode(i.ExternalIdentifierEncodedValue, "")

	resp := &model.Insured{
		Id: i.ID,
		Name: &insurancecoreproto.InsuredName{
			BusinessName: i.Name,
		},
		Address: &address,
		ExternalIdentifier: &insurancecoreproto.InsuredIdentifier{
			Type:  insurancecoreproto.InsuredIdentifierTypeFromHumanReadableString(i.ExternalIdentifierType),
			Value: decodedExternalIdentifierValue,
		},
		ContactInfo: &model.ContactInfo{},
	}
	if i.Email.Valid {
		resp.ContactInfo.Email = i.Email.String
	}
	if i.Phone.Valid {
		resp.ContactInfo.Phone = i.Phone.String
	}

	return resp, nil
}
