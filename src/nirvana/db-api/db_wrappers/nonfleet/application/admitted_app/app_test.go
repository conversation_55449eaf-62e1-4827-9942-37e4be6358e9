package admitted_app_test

import (
	"testing"
	"time"

	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	nfapp_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_app"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
)

func TestGetBusinessOwnerDetails(t *testing.T) {
	app := nfapp_builder.New().
		WithDefaultMockData().
		WithID(uuid.New()).
		WithCompanyInfo(admitted_app.CompanyInfo{
			DOTNumber: 333,
			Name:      "Nirvana Trucking Test",
			BusinessOwner: admitted_app.BusinessOwner{
				Firstname:      "<PERSON>",
				Lastname:       "<PERSON><PERSON>",
				DateOfBirth:    time_utils.NewDate(1992, time.April, 15).ToTime(),
				DriverOnPolicy: pointer_utils.ToPointer(true),
				Address: application.Address{
					City:    "Charleston",
					State:   "SC",
					ZipCode: "29401",
					Street:  "123 Main St",
				},
			},
		}).
		WithContactEmail("<EMAIL>").
		Build()

	want := nonfleet.AdmittedAppBusinessOwner{
		Address: nonfleet.Address{
			City:   "Charleston",
			State:  "SC",
			Zip:    "29401",
			Street: "123 Main St",
		},
		DateOfBirth:    time_utils.NewDate(1992, time.April, 15).String(),
		DriverOnPolicy: pointer_utils.ToPointer(true),
		Email:          pointer_utils.ToPointer("<EMAIL>"),
		FirstName:      "John",
		LastName:       "Doe",
	}

	got := app.Info.GetBusinessOwnerDetails()
	assert.Equal(t, want, *got)
}

func TestSetAncillaryCoverages(t *testing.T) {
	tests := []struct {
		name              string
		initialCoverages  []application.CoverageDetails
		inputCoverages    []application.CoverageDetails
		expectedCoverages []application.CoverageDetails
		expectedHALimit   *int
	}{
		{
			name: "Add new ancillary coverage",
			initialCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageUM,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(5000),
				},
			},
			inputCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageTrailerInterchange,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(5000),
				},
			},
			expectedCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageUM,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(5000),
				},
				{
					CoverageType: app_enums.CoverageTrailerInterchange,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(5000),
				},
			},
		},
		{
			name: "Update existing ancillary coverage",
			initialCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageTrailerInterchange,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(5000),
				},
			},
			inputCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageTrailerInterchange,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(10000),
				},
			},
			expectedCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageTrailerInterchange,
					Deductible:   pointer_utils.ToPointer(1000),
					Limit:        pointer_utils.ToPointer(10000),
				},
			},
		},
		{
			name: "Handle CoverageHiredAuto case",
			initialCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageHiredAuto,
					Deductible:   nil,
					Limit:        nil,
				},
			},
			inputCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageHiredAuto,
					Deductible:   nil,
					Limit:        pointer_utils.ToPointer(1000000),
				},
			},
			expectedCoverages: []application.CoverageDetails{
				{
					CoverageType: app_enums.CoverageHiredAuto,
					Deductible:   nil,
					Limit:        pointer_utils.ToPointer(1000000),
				},
			},
			expectedHALimit: pointer_utils.ToPointer(1000000),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set initial coverages in the app
			app := nfapp_builder.New().
				WithDefaultMockData().
				WithAncillaryCoverages(tt.initialCoverages).
				Build()

			// Set input coverages in the app
			app.Info.SetAncillaryCoverages(tt.inputCoverages)

			assert.Equal(t, tt.expectedCoverages, app.Info.GetCoverageInfo().AncillaryCovs)

			if tt.expectedHALimit != nil {
				for _, coverage := range app.Info.GetCoverageInfo().AncillaryCovs {
					if coverage.CoverageType == app_enums.CoverageHiredAuto {
						assert.Equal(t, *tt.expectedHALimit, *coverage.Limit)
					}
				}
			}
		})
	}
}
