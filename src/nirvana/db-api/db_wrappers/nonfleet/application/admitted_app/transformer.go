package admitted_app

import (
	"context"
	"sort"
	"time"

	app_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/policy_common/constants"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/nonfleet/coverages/admitted"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	applicationenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nfenums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	nf_common "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/common"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/nirvanaapp/enums"
	nirvanaapp "nirvanatech.com/nirvana/nirvanaapp/models/application"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/rating/models/models_release"
	"nirvanatech.com/nirvana/rating/rtypes"
)

// TODO: move this to a common place
var primaryCoverages = []string{
	string(common.CoverageAutoLiability),
	string(common.CoverageAutoPhysicalDamage),
	string(common.CoverageMotorTruckCargo),
	string(common.CoverageGeneralLiability),
}

func setCompany(app *AdmittedApp, c nirvanaapp.CompanyDetails) {
	companyInfo := CompanyInfo{
		DOTNumber:          int(*c.DOTNumber),
		Name:               c.Name,
		NumberOfPowerUnits: int(*c.NoOfPowerUnits),
		USState:            c.USState,
		BusinessOwner: BusinessOwner{
			Firstname:      c.BusinessOwner.FirstName,
			Lastname:       c.BusinessOwner.LastName,
			DateOfBirth:    c.BusinessOwner.DateOfBirth.ToTime(),
			DriverOnPolicy: &c.BusinessOwner.DriverOnPolicy,
			Address: application.Address{
				Street:  c.BusinessOwner.Address.Street,
				City:    c.BusinessOwner.Address.City,
				State:   c.BusinessOwner.Address.State,
				ZipCode: c.BusinessOwner.Address.Zip,
			},
		},
		TerminalLocation: &TerminalLocation{
			SameAsPhysicalAddress: false,
			Address: application.Address{
				Street:  c.GarageAddress.Street,
				City:    c.GarageAddress.City,
				State:   c.GarageAddress.State,
				ZipCode: c.GarageAddress.Zip,
			},
		},
		MailingAddress: application.Address{
			Street:  c.MailingAddress.Street,
			City:    c.MailingAddress.City,
			State:   c.MailingAddress.State,
			ZipCode: c.MailingAddress.Zip,
		},
		AnnualCostOfHire: c.AnnualCostOfHire,
	}
	app.CompanyInfo = companyInfo
}

func setEquipment(app *AdmittedApp, e nirvanaapp.EquipmentDetails) error {
	var vehiclesDetails []VehicleDetails
	for _, vd := range e.Vehicles {
		weightClass, err := getVehicleWeightClass(vd.WeightClass)
		if err != nil {
			return err
		}

		vehicleType, err := getVehicleType(vd.VehicleType)
		if err != nil {
			return err
		}

		vehicleClass, err := getVehicleClass(vd.VehicleClass)
		if err != nil {
			return err
		}

		var model string
		if vd.Model != nil {
			model = *vd.Model
		}

		var statedValue *int
		if vd.StatedValue != nil {
			sd := int(*vd.StatedValue)
			statedValue = &sd
		}

		vehicleDetails := VehicleDetails{
			VIN:         vd.VIN,
			Year:        int(vd.Year),
			Make:        vd.Make,
			Model:       model,
			StatedValue: statedValue,
			WeightClass: *weightClass,
			Type:        *vehicleType,
			Class:       *vehicleClass,
		}
		vehiclesDetails = append(vehiclesDetails, vehicleDetails)
	}
	app.EquipmentInfo.Vehicles = vehiclesDetails
	return nil
}

func setCoverages(app *AdmittedApp, nirvanaApp nirvanaapp.App) error {
	var primayCovs []application.CoverageDetails
	for _, pc := range nirvanaApp.CoverageDetails.Coverages {
		var limit int
		if pc.Limit != nil {
			limit = int(*pc.Limit)
		}
		var deductible int
		if pc.Deductible != nil {
			deductible = int(*pc.Deductible)
		}

		coverageType, err := getCoverageType(pc.CoverageType)
		if err != nil {
			return err
		}

		primaryCoverage := application.CoverageDetails{
			CoverageType: *coverageType,
			IsRequired:   true,
			Limit:        &limit,
			Deductible:   &deductible,
			Label:        str_utils.PrettyEnumString(coverageType.String(), "Coverage"),
		}

		if slice_utils.Contains(primaryCoverages, coverageType.String()) {
			primayCovs = append(primayCovs, primaryCoverage)
		}
	}

	for _, pc := range primaryCoverages {
		isPCPresent := false
		for _, p := range primayCovs {
			if pc == p.CoverageType.String() {
				isPCPresent = true
			}
		}

		if !isPCPresent {
			coverageType, err := applicationenums.CoverageString(pc)
			if err != nil {
				return err
			}
			primaryCoverage := application.CoverageDetails{
				CoverageType: coverageType,
				IsRequired:   false,
				Limit:        pointer_utils.ToPointer(admitted.DefaultNFLimitAmounts[coverageType]),
				Deductible:   pointer_utils.ToPointer(admitted.DefaultNFDeductibleAmounts[coverageType]),
				Label:        str_utils.PrettyEnumString(coverageType.String(), "Coverage"),
			}
			primayCovs = append(primayCovs, primaryCoverage)
		}
	}

	isAPDMTCDeductibleCombined := false
	if nirvanaApp.CoverageDetails.IsAPDMTCDeductibleCombined != nil {
		isAPDMTCDeductibleCombined = *nirvanaApp.CoverageDetails.IsAPDMTCDeductibleCombined
	}

	hasHiredAuto := app.CompanyInfo.AnnualCostOfHire != nil

	app.CoverageInfo = application.CoverageInfo{
		IsAPDMTCDeductibleCombined: isAPDMTCDeductibleCombined,
		HasUndesiredOperations:     nirvanaApp.HasUndesiredOperations,
		PrimaryCovs:                primayCovs,
		AncillaryCovs:              []application.CoverageDetails{},
		EffectiveDate:              nirvanaApp.EffectiveDate.ToTime(),
		EffectiveDateTo:            time_utils.AddYears(nirvanaApp.EffectiveDate.ToTime(), 1),
		HasHiredAuto:               hasHiredAuto,
	}
	return nil
}

func getCoverageType(coverageType enums.CoverageType) (*applicationenums.Coverage, error) {
	c := "Coverage" + coverageType.String()
	coverage, err := applicationenums.CoverageString(c)
	if err != nil {
		return nil, err
	}
	return &coverage, nil
}

func getVehicleWeightClass(vwc enums.WeightClass) (*nfenums.WeightClass, error) {
	w := "WeightClass" + vwc.String()
	weightClass, err := nfenums.WeightClassString(w)
	if err != nil {
		return nil, err
	}
	return &weightClass, nil
}

func getVehicleType(vt enums.VehicleType) (*nfenums.VehicleType, error) {
	v := "VehicleType" + vt.String()
	vehicleType, err := nfenums.VehicleTypeString(v)
	if err != nil {
		return nil, err
	}
	return &vehicleType, nil
}

func getVehicleClass(vc enums.VehicleClass) (*nfenums.VehicleClass, error) {
	v := "VehicleClass" + vc.String()
	vehicleClass, err := nfenums.VehicleClassString(v)
	if err != nil {
		return nil, err
	}
	return &vehicleClass, nil
}

func setCommodity(app *AdmittedApp, commodityDetails nirvanaapp.CommodityDetails) error {
	if len(commodityDetails.Commodities) == 0 {
		app.CommodityInfo = CommodityInfo{}
		return nil
	}
	var commodityRecords []CommodityRecord
	for _, r := range commodityDetails.Commodities {
		c := "AdmittedCommodity" + r.Name.String()
		category, err := nfenums.AdmittedCommodityString(c)
		if err != nil {
			return err
		}

		label, found := CommodityToLabelMap[category]
		if !found {
			return errors.New("label not found")
		}

		commodityRecord := CommodityRecord{
			Category:      category,
			Percentage:    int(*r.Percentage),
			Name:          r.Name.String(),
			CategoryLabel: label,
		}
		commodityRecords = append(commodityRecords, commodityRecord)
	}

	var primaryCommodity *nfenums.AdmittedCommodity
	sort.Slice(commodityRecords, func(i, j int) bool {
		return commodityRecords[i].Percentage > commodityRecords[j].Percentage
	})

	if len(commodityRecords) == 1 {
		primaryCommodity = &commodityRecords[0].Category
	} else {
		if commodityRecords[0].Percentage >= commodityRecords[1].Percentage {
			primaryCommodity = &commodityRecords[0].Category
		}
	}

	app.CommodityInfo = CommodityInfo{
		CommodityDistribution: commodityRecords,
		PrimaryCommodity:      primaryCommodity,
	}
	return nil
}

func setDriver(app *AdmittedApp, nirvanaApp nirvanaapp.App) error {
	var drivers []DriverDetails

	for i := range nirvanaApp.DriverDetails.Drivers {
		driver := nirvanaApp.DriverDetails.Drivers[i]
		isOutOfState := IsDriverOutOfState(driver.LicenseState, nirvanaApp.CompanyDetails.USState)
		hasViolatedInLast3Yrs := false
		if driver.ViolationDetails.HasViolationsInPast3Yrs != nil {
			hasViolatedInLast3Yrs = *driver.ViolationDetails.HasViolationsInPast3Yrs
		}

		driverDetails := DriverDetails{
			DriverBasicDetails: application.DriverBasicDetails{
				FirstName:     driver.FirstName,
				LastName:      driver.LastName,
				LicenseState:  driver.LicenseState,
				LicenseNumber: driver.LicenseNumber,
				DateOfBirth:   driver.DateOfBirth.ToTime(),
				DateOfHire:    driver.DateOfHire.ToTime(),
			},
			IsIncluded:                true,
			IsOutOfState:              isOutOfState,
			YearsOfExp:                int(*driver.YearsOfExperience),
			ViolationInLastThreeYears: hasViolatedInLast3Yrs,
		}
		drivers = append(drivers, driverDetails)
	}
	app.DriverInfo.Drivers = drivers
	return nil
}

func setClassInfo(app *AdmittedApp, radiusDetails nirvanaapp.RadiusDetails) {
	maxRadiusOfOperation := *radiusDetails.MaxRadiusOfOperationInMiles
	classInfoMaxRadius := nfenums.MaxRadiusOfOperationInvalid
	if maxRadiusOfOperation >= 0 && maxRadiusOfOperation <= 50 {
		classInfoMaxRadius = nfenums.MaxRadiusOfOperation50
	} else if maxRadiusOfOperation > 50 && maxRadiusOfOperation <= 100 {
		classInfoMaxRadius = nfenums.MaxRadiusOfOperation100
	} else if maxRadiusOfOperation > 100 && maxRadiusOfOperation <= 200 {
		classInfoMaxRadius = nfenums.MaxRadiusOfOperation200
	} else if maxRadiusOfOperation > 200 && maxRadiusOfOperation <= 300 {
		classInfoMaxRadius = nfenums.MaxRadiusOfOperation300
	} else if maxRadiusOfOperation > 300 && maxRadiusOfOperation <= 500 {
		classInfoMaxRadius = nfenums.MaxRadiusOfOperation500
	} else if maxRadiusOfOperation > 500 {
		classInfoMaxRadius = nfenums.MaxRadiusOfOperation999plus
	}
	app.ClassInfo = ClassInfo{
		MaxRadiusOfOperation: classInfoMaxRadius,
	}
}

func setTSPUserInfo(app *AdmittedApp, tspUser nirvanaapp.TSPUser) {
	app.TSPInfo = application.TSPInfo{
		InsuredInfo: application.InsuredInfo{
			Name:  tspUser.Name,
			Email: tspUser.Email,
		},
	}
}

func setModelPinConfigInfo(ctx context.Context, deps nirvanaapp.Deps, a *AdmittedApp, app nirvanaapp.App) error {
	usState := app.CompanyDetails.USState
	provider := rtypes.ProviderProgressive
	carrier := constants.GetCarrier(usState, policy_enums.ProgramTypeNonFleetAdmitted)
	// For TX, we are launching with a non-admitted program which uses provider surplus models.
	// Later, when we fully launch in TX, these will be changed with business line.
	if carrier == constants.InsuranceCarrierSiriusPointSpeciality {
		provider = rtypes.ProviderProgressiveSurplus
	}

	authUser := authz.UserFromContext(ctx)
	version, err := getRateMLVersionForPricing(ctx, deps.FFClient, authUser, provider, usState, app.EffectiveDate.ToTime())
	if err != nil {
		log.Error(ctx,
			"could not get rateml model version for application during creation of nf admitted app",
			log.Err(err),
			log.String("provider", provider.String()),
			log.String("appId", app.ID.String()),
			log.Any("state", app.CompanyDetails.USState),
		)
		// Not returning error because we still want to persist an invalid
		// rateml version.
	}

	a.ModelPinConfigInfo = application.NewModelPinConfigInfo(provider, app.CompanyDetails.USState, version)
	return nil
}

func getRateMLVersionForPricing(
	ctx context.Context,
	ffClient feature_flag_lib.Client,
	authUser authz.User,
	provider rtypes.RatemlModelProvider,
	usState us_states.USState,
	effectiveDate time.Time,
) (rtypes.RatemlModelVersion, error) {
	version, err := models_release.GetActiveVersion(
		ctx,
		ffClient,
		&authUser,
		provider,
		usState,
		effectiveDate,
	)
	if err != nil {
		return version, err
	}
	return version, nil
}

func setLossInfo(app *AdmittedApp, lossInfo nirvanaapp.AdditionalRiskDetails) {
	lossInfoFiles := make([]app_wrapper.FileMetadata, 0)
	if lossInfo.Files != nil {
		for _, file := range lossInfo.Files {
			lossInfoFiles = append(lossInfoFiles, app_wrapper.FileMetadata{
				Name:   file.Name,
				Handle: file.Handle,
			})
		}
	}

	app.LossInfo = LossInfo{
		AtFaultLosses: *lossInfo.AtFaultAccidents,
		Files:         lossInfoFiles,
	}
}

func setUnderwriterInfo(app *AdmittedApp) {
	app.UnderwriterInfo = UnderwriterInput{}
}

func setScraperInfo(app *AdmittedApp) {
	app.ScraperInfo = nf_common.ScraperInfo{}
}

func setTSPInfo(app *AdmittedApp, tspInfo *nirvanaapp.TSPInfo, tspUser nirvanaapp.TSPUser) {
	if tspInfo == nil {
		app.TSPInfo = application.TSPInfo{}
		return
	}

	app.TSPInfo = application.TSPInfo{
		TSPEnum: tspInfo.TSPEnum,
	}

	if tspInfo.TelematicsInfo != nil {
		app.TSPInfo.TelematicsInfo = &app_wrapper.TelematicsInfo{
			FirstPipelineSuccessAt:                tspInfo.TelematicsInfo.FirstPipelineSuccessAt,
			TelematicsDataStatus:                  tspInfo.TelematicsInfo.TelematicsDataStatus,
			TelematicsConnHandleId:                tspInfo.TelematicsInfo.TelematicsConnHandleId,
			TelematicsPipelineStartedAt:           tspInfo.TelematicsInfo.TelematicsPipelineStartedAt,
			FirstPipelineSuccessWithRaceCondition: tspInfo.TelematicsInfo.FirstPipelineSuccessWithRaceCondition,
		}

		if tspInfo.TelematicsInfo.TelematicsConsentRequestSMS != nil {
			app.TSPInfo.TelematicsInfo.TelematicsConsentRequestSMS = &app_wrapper.TelematicsConsentRequestSMS{
				CompletedAt: tspInfo.TelematicsInfo.TelematicsConsentRequestSMS.CompletedAt,
				SMSJobRunID: tspInfo.TelematicsInfo.TelematicsConsentRequestSMS.SMSJobRunID,
			}
		}

		if tspInfo.TelematicsInfo.TelematicsConsentRequestEmail != nil {
			app.TSPInfo.TelematicsInfo.TelematicsConsentRequestEmail = &app_wrapper.TelematicsConsentRequestEmail{
				CompletedAt:   tspInfo.TelematicsInfo.TelematicsConsentRequestEmail.CompletedAt,
				EmailJobRunID: tspInfo.TelematicsInfo.TelematicsConsentRequestEmail.EmailJobRunID,
			}
		}
	}

	app.TSPInfo.TSPConnHandleId = tspInfo.TSPConnHandleId

	app.TSPInfo.InsuredInfo = application.InsuredInfo{
		Name:  tspUser.Name,
		Email: tspUser.Email,
	}
}
