package quote_scraper

import (
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/log"
	nonFleetDb "nirvanatech.com/nirvana/db-api/db_models/non_fleet"
	"nirvanatech.com/nirvana/servers/quote_scraper/enums"
)

func QuoteScraperRecordFromDb(
	dbRecord *nonFleetDb.QuoteScraperRecord,
) (record *QuoteScraperRecord, err error) {
	record = &QuoteScraperRecord{}

	if dbRecord == nil {
		return record, errors.New("Can't deserialize nil record from db")
	}

	record.RequestID = uuid.MustParse(dbRecord.RequestID)
	record.SubmissionID = uuid.MustParse(dbRecord.SubmissionID)
	record.ApplicationID = uuid.MustParse(dbRecord.ApplicationID)
	record.ProcessorID = uuid.MustParse(dbRecord.ProcessorID)

	record.DOT = dbRecord.Dot
	record.UpdatedAt = dbRecord.UpdatedAt
	record.CreatedAt = dbRecord.CreatedAt

	record.RetryCount = dbRecord.RetryCount

	if dbRecord.S3Path.Valid {
		record.S3Path = &dbRecord.S3Path.String
	}

	if dbRecord.ErrorKey.Valid {
		scrapingError, err := enums.ScrapingErrorString(dbRecord.ErrorKey.String)
		if err != nil {
			return record, errors.Wrap(err, "Unable to parse error key")
		}
		record.ErrorKey = &scrapingError
	}

	executionStatus, err := enums.ScrapingStatusString(dbRecord.Status)
	if err != nil {
		return record, errors.Wrap(err, "Unable to parse execution status")
	}
	record.Status = executionStatus

	scrapeType, err := enums.ScrapeTypeString(dbRecord.ScrapeType)
	if err != nil {
		return record, errors.Wrap(err, "Unable to parse scrape type")
	}
	record.ScrapeType = scrapeType

	log.Plain.Debug("Successfully deserialized object from db record")

	return
}

func QuoteScraperRecordToDb(record *QuoteScraperRecord) (dbRecord *nonFleetDb.QuoteScraperRecord, err error) {
	dbRecord = &nonFleetDb.QuoteScraperRecord{}

	if record == nil {
		return dbRecord, errors.New("Can't serialize nil record")
	}

	dbRecord.RequestID = record.RequestID.String()
	dbRecord.SubmissionID = record.SubmissionID.String()
	dbRecord.ApplicationID = record.ApplicationID.String()
	dbRecord.ProcessorID = record.ProcessorID.String()

	dbRecord.Dot = record.DOT
	dbRecord.UpdatedAt = record.UpdatedAt
	dbRecord.CreatedAt = record.CreatedAt

	dbRecord.RetryCount = record.RetryCount

	dbRecord.S3Path = null.StringFromPtr(record.S3Path)

	if record.ErrorKey != nil {
		dbRecord.ErrorKey = null.StringFrom(record.ErrorKey.String())
	}

	dbRecord.Status = record.Status.String()
	dbRecord.ScrapeType = record.ScrapeType.String()

	log.Plain.Debug("Successfully serialized object to db record")

	return
}
