package rule_runs

//go:generate go run github.com/dmarkham/enumer -type=PrimaryDeclineReason -linecomment
type PrimaryDeclineReason int

const (
	PrimaryDeclineReasonOperations PrimaryDeclineReason = iota // Operations
	PrimaryDeclineReasonLosses                                 // Losses
)

//go:generate go run github.com/dmarkham/enumer -type=SecondaryDeclineReason -linecomment
type SecondaryDeclineReason int

const (
	SecondaryDeclineState              SecondaryDeclineReason = iota // State
	SecondaryDeclineOperationsAllOther                               // Operations - All Other
	SecondaryDeclineReasonFleetSize                                  // Fleet Size Changes
	SecondaryDeclineReasonYIB                                        // Years In Business
	SecondaryDeclineReasonLossHistory                                // Loss History
)
