package admitted

import (
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	coverage_enum "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/telematics"
)

var (
	tspEnum        = telematics.TSPSamsara
	telematicsInfo = app.TelematicsInfo{TelematicsDataStatus: coverage_enum.TelematicsDataStatusNotEnoughData}
	tspConnId      = uuid.New()
)

func MockProgramData(id uuid.UUID) *ProgramData {
	timeToUse := time.Date(2023, 3, 3, 0, 0, 0, 0, time.UTC)
	dobTime := time.Date(1990, 3, 3, 0, 0, 0, 0, time.UTC)
	return &ProgramData{
		ID: id,
		CompanyInfo: &CompanyInfo{
			Name:      "Nirvana Fleet",
			DOTNumber: 2081158,
			USState:   us_states.IL,
			BusinessOwner: BusinessOwner{
				Firstname:   "John",
				Lastname:    "Dow",
				DateOfBirth: dobTime,
				Address: Address{
					Street:  "Street 2",
					City:    "Chicago",
					State:   "Illinois",
					ZipCode: "60004",
				},
				DriverOnPolicy: false,
			},
			MailingAddress: application.Address{
				Street:  "Street 2",
				City:    "Chicago",
				State:   "IL",
				ZipCode: "60004",
			},
			NumberOfPowerUnits: 2,
			TerminalLocation: &TerminalLocation{
				SameAsPhysicalAddress: false,
				Address: Address{
					Street:  "Street 3",
					City:    "Chicago",
					State:   "Illinois",
					ZipCode: "60013",
				},
			},
		},
		InsuredInfo: &InsuredInfo{
			Email: "<EMAIL>",
			Name:  "John",
		},
		EquipmentInfo: &EquipmentInfo{
			Vehicles: []VehicleDetails{
				{
					VIN:         "4V4NC9EH0FN183920",
					Year:        2015,
					Make:        "CG Make",
					Model:       "CG Model",
					WeightClass: enums.WeightClass6,
					Type:        enums.VehicleTypeTruck,
					Class:       enums.VehicleClassDump,
					StatedValue: pointer_utils.Int(12000),
				},
				{
					VIN:         "4V4NC9EH0FN183921",
					Year:        2019,
					Make:        "UP Make",
					Model:       "UP Model",
					WeightClass: enums.WeightClass6,
					Type:        enums.VehicleTypeTruck,
					Class:       enums.VehicleClassAgricultural,
					StatedValue: pointer_utils.Int(12000),
				},
			},
		},
		CoverageInfo: &CoverageInfo{
			HasUndesiredOperations: false,
			PrimaryCovs: []CoverageDetails{
				{
					CoverageType:   coverage_enum.CoverageAutoLiability,
					Label:          coverage_enum.GetCoverageLabel(coverage_enum.CoverageAutoLiability),
					Limit:          pointer_utils.Int(1000000),
					Deductible:     pointer_utils.Int(0),
					Premium:        pointer_utils.Int(500000),
					PremiumPerUnit: pointer_utils.Int(50000),
				},
			},
			AncillaryCovs: []CoverageDetails{
				{
					CoverageType:   coverage_enum.CoverageUM,
					Label:          coverage_enum.GetCoverageLabel(coverage_enum.CoverageUM),
					Limit:          pointer_utils.Int(100000),
					Deductible:     pointer_utils.Int(2000),
					Premium:        pointer_utils.Int(50000),
					PremiumPerUnit: pointer_utils.Int(5000),
				},
			},
		},
		DriverInfo: &DriverInfo{
			Drivers: []DriverDetails{
				{
					LicenseNumber:             "DL1234",
					FirstName:                 "John",
					LastName:                  "Dow",
					LicenseState:              "CA",
					DateOfBirth:               dobTime,
					IsIncluded:                true,
					IsOutOfState:              false,
					YearsOfExp:                5,
					ViolationInLastThreeYears: true,
				},
				{
					LicenseNumber:             "DL5678",
					FirstName:                 "Cillian",
					LastName:                  "Murphy",
					LicenseState:              "NM",
					DateOfBirth:               dobTime,
					IsIncluded:                true,
					IsOutOfState:              false,
					YearsOfExp:                24,
					ViolationInLastThreeYears: false,
				},
			},
		},
		AgencyInfo: &AgencyInfo{
			Name: "John",
		},
		LossInfo: &admitted_app.LossInfo{
			AtFaultLosses: 0,
			Files:         nil,
		},
		ClassInfo: &admitted_app.ClassInfo{
			MaxRadiusOfOperation: enums.MaxRadiusOfOperation50,
		},
		CommodityInfo: &admitted_app.CommodityInfo{
			CommodityDistribution: []admitted_app.CommodityRecord{
				{
					Category:      enums.AdmittedCommodityBakedGoods,
					CategoryLabel: enums.AdmittedCommodityBakedGoods.String(),
					Name:          "Boxes",
					Percentage:    100,
				},
			},
			PrimaryCommodity: pointer_utils.ToPointer(enums.AdmittedCommodityBakedGoods),
			PrimaryCategory:  "Auto Parts",
		},
		FinancialInfo:   &admitted_app.FinancialInfo{},
		UnderwriterInfo: &admitted_app.UnderwriterInput{},
		TSPInfo: &application.TSPInfo{
			TSPEnum:         &tspEnum,
			TSPConnHandleId: tspConnId,
			TelematicsInfo:  &telematicsInfo,
			InsuredInfo: application.InsuredInfo{
				Name:  "John",
				Email: "<EMAIL>",
			},
		},
		CreatedAt: timeToUse.Add(2 * time.Hour),
		UpdatedAt: timeToUse.Add(3 * time.Hour),
	}
}

func MockProgramDataWithNilValues(id uuid.UUID) *ProgramData {
	timeToUse := time.Date(2023, 3, 3, 0, 0, 0, 0, time.UTC)
	return &ProgramData{
		ID: id,
		CompanyInfo: &CompanyInfo{
			Name:      "Nirvana Fleet",
			DOTNumber: 12345,
			USState:   us_states.IL,
			BusinessOwner: BusinessOwner{
				Firstname:   "John",
				Lastname:    "Dow",
				DateOfBirth: timeToUse,
				Address: Address{
					Street:  "Street 2",
					City:    "Chicago",
					State:   "Illinois",
					ZipCode: "60012",
				},
				DriverOnPolicy: false,
			},
			AnnualCostOfHire:   pointer_utils.Int64(10000),
			NumberOfPowerUnits: 2,
		},
		InsuredInfo: &InsuredInfo{
			Email: "<EMAIL>",
			Name:  "John",
		},
		EquipmentInfo: &EquipmentInfo{
			Vehicles: []VehicleDetails{
				{
					VIN:         "CG 16B 1234",
					Year:        2019,
					Make:        "Make",
					Model:       "Model",
					WeightClass: enums.WeightClass6,
					Type:        enums.VehicleTypeTruck,
					Class:       enums.VehicleClassUnidentified,
				},
			},
		},
		CoverageInfo: &CoverageInfo{
			HasUndesiredOperations: false,
			PrimaryCovs: []CoverageDetails{
				{
					CoverageType: coverage_enum.CoverageAutoLiability,
					Label:        coverage_enum.GetCoverageLabel(coverage_enum.CoverageAutoLiability),
				},
			},
			AncillaryCovs: []CoverageDetails{
				{
					CoverageType: coverage_enum.CoverageUM,
					Label:        coverage_enum.GetCoverageLabel(coverage_enum.CoverageUM),
				},
			},
		},
		DriverInfo: &DriverInfo{
			Drivers: []DriverDetails{
				{
					LicenseNumber:             "DL1234",
					FirstName:                 "John",
					LastName:                  "Dow",
					LicenseState:              "CA",
					DateOfBirth:               timeToUse,
					IsIncluded:                true,
					IsOutOfState:              false,
					YearsOfExp:                5,
					ViolationInLastThreeYears: false,
				},
			},
		},
		AgencyInfo: &AgencyInfo{
			Name: "John",
		},
		LossInfo:        &admitted_app.LossInfo{},
		ClassInfo:       &admitted_app.ClassInfo{},
		CommodityInfo:   &admitted_app.CommodityInfo{},
		FinancialInfo:   &admitted_app.FinancialInfo{},
		UnderwriterInfo: &admitted_app.UnderwriterInput{},
		TSPInfo:         &application.TSPInfo{},
		CreatedAt:       timeToUse.Add(2 * time.Hour),
		UpdatedAt:       timeToUse.Add(3 * time.Hour),
	}
}
