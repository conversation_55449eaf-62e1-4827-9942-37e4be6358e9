package report_schedules

import (
	"encoding/json"
	"strings"

	"github.com/benbjohnson/clock"
	"github.com/robfig/cron/v3"

	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/reporting/enums"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/db-api/db_models/reporting"
	oapi_end "nirvanatech.com/nirvana/openapi-specs/components/reports"
)

func scheduleFromDb(dbObj *reporting.Schedule) (*Schedule, error) {
	if dbObj == nil {
		return nil, errors.New("dbObj i.e db_models.Schedule is nil")
	}
	id, err := uuid.Parse(dbObj.ID)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to parse scheduleId: %s", dbObj.ID)
	}
	if !dbObj.CreatedBy.Valid {
		return nil, errors.New("CreatedBy field is null")
	}
	createdBy, err := uuid.Parse(dbObj.CreatedBy.String)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to parse createdBy: %s", dbObj.CreatedBy)
	}
	reportParams, err := json.Marshal(dbObj.ReportParams)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to marshal reportParams: %s", dbObj.ReportParams)
	}
	reportType, err := enums.ReportTypeString(dbObj.ReportType.String)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to parse reportType: %s", dbObj.ReportType)
	}

	jobRunId, err := jtypes.JobRunIdFromString(dbObj.JobRunID.String)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to parse jobRunId: %s", dbObj.JobRunID)
	}
	schedule := &Schedule{
		ID:           id,
		CronSchedule: dbObj.CronSchedule.String,
		ReportParams: reportParams,
		ReportType:   reportType,
		CreatedBy:    createdBy,
		CreatedAt:    dbObj.CreatedAt.Time,
		JobRunId:     jobRunId,
	}
	if dbObj.DeletedAt.Valid {
		deletedAt := dbObj.DeletedAt.Time
		schedule.DeletedAt = &deletedAt
	}
	return schedule, nil
}

func scheduleToDb(obj *Schedule) (*reporting.Schedule, error) {
	id := obj.ID.String()
	createdBy := obj.CreatedBy.String()
	createdAt := null.TimeFrom(obj.CreatedAt)
	jobRunID := null.StringFrom(obj.JobRunId.String())
	cronSchedule := null.StringFrom(obj.CronSchedule)
	schedule := &reporting.Schedule{
		ID:           id,
		CreatedBy:    null.StringFrom(createdBy),
		ReportParams: null.JSONFrom(obj.ReportParams),
		ReportType:   null.StringFrom(obj.ReportType.String()),
		CreatedAt:    createdAt,
		JobRunID:     jobRunID,
		CronSchedule: cronSchedule,
	}
	if obj.DeletedAt != nil {
		deletedAt := null.TimeFrom(*obj.DeletedAt)
		schedule.DeletedAt = deletedAt
	}
	return schedule, nil
}

func ToOapiSchedule(obj *Schedule) (*oapi_end.ScheduleData, error) {
	clk := clock.New()
	scheduleId := obj.ID.String()
	reportType := obj.ReportType.String()
	status := oapi_end.Active
	if obj.DeletedAt != nil {
		status = oapi_end.Deleted
	}
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	schedule, err := parser.Parse(obj.CronSchedule)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to parse cron schedule: %s", obj.CronSchedule)
	}
	now := clk.Now().UTC()
	nextRun := schedule.Next(now)
	scheduleType, err := getScheduleTypeFromCronExpression(obj.CronSchedule)
	if err != nil {
		return nil, errors.Wrapf(
			err, "unable to get schedule type from cron expression: %s", obj.CronSchedule)
	}
	scheduleTypeStr := scheduleType.String()
	scheduleData := &oapi_end.ScheduleData{
		ScheduleId:   &scheduleId,
		ReportType:   &reportType,
		NextTime:     &nextRun,
		ScheduleType: &scheduleTypeStr,
		Status:       &status,
	}
	return scheduleData, nil
}

func getScheduleTypeFromCronExpression(cronExpression string) (enums.ScheduleType, error) {
	parts := strings.Split(cronExpression, " ")
	if len(parts) < 6 {
		return 0, errors.New("invalid cron expression")
	}

	if parts[1] != "*" && parts[2] != "*" && parts[3] == "*" && parts[4] == "*" && parts[5] == "*" {
		return enums.Daily, nil
	} else if parts[1] != "*" && parts[2] != "*" && parts[3] == "*" && parts[4] == "*" && parts[5] != "*" {
		return enums.Weekly, nil
	} else if parts[1] != "*" && parts[2] != "*" && parts[3] != "*" && parts[4] == "*" && parts[5] == "*" {
		return enums.Monthly, nil
	} else if parts[1] != "*" && parts[2] != "*" && parts[3] != "*" && parts[4] != "*" && parts[5] == "*" {
		return enums.Yearly, nil
	} else {
		return 0, errors.New("unknown schedule type")
	}
}
