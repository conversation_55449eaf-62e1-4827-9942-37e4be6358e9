package reporting_utils

import (
	"nirvanatech.com/nirvana/db-api/db_models"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
)

type MSTReportPolicyDataDB struct {
	P db_models.Policy           `boil:"P,bind"`
	S db_models.Submission       `boil:"S,bind"`
	A db_models.Application      `boil:"A,bind"`
	I db_models.IndicationOption `boil:"I,bind"`
}

type MSTReportPolicyData struct {
	Policy      policy.Policy
	Application application.Application
}

// RateMLArtefact represents a row in the `RATEML_ARTIFACTS` table in
// Data Science Snowflake DB.
type RateMLArtefact struct {
	Filename     string `boil:"FILENAME"`
	SubmissionID string `boil:"SUBMISSION_ID"`
	Entities     string `boil:"ENTITIES"`
}
