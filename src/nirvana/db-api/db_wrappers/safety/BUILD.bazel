load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "safety",
    srcs = [
        "authz.go",
        "fx.go",
        "interfaces.go",
        "object_defs.go",
        "safety_report_user_metadata.go",
        "serde.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/safety",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/safety",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/telematics",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_jinzhu_copier//:copier",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "safety_test",
    srcs = [
        "export_test.go",
        "serde_test.go",
    ],
    embed = [":safety"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/db-api/db_models/safety",
        "//nirvana/telematics",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
    ],
)
