load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "samsara",
    srcs = [
        "data_wrapper_impl.go",
        "fx.go",
        "interfaces.go",
        "object_defs.go",
        "serde_utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/samsara",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/samsara",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)
