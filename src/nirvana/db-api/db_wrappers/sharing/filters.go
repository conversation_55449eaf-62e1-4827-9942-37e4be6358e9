package sharing

import (
	"time"

	"github.com/google/uuid"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"nirvanatech.com/nirvana/db-api/db_models/sharing"
)

type Filter interface {
	queryMods() []qm.QueryMod
}

type singleQMFilter struct {
	queryMod qm.QueryMod
}

func (s *singleQMFilter) queryMods() []qm.QueryMod {
	return []qm.QueryMod{s.queryMod}
}

var _ Filter = (*singleQMFilter)(nil)

func Limit(size int) Filter {
	return &singleQMFilter{queryMod: qm.Limit(size)}
}

func ShareableLinkTypeIs(linkType SharedLinkType) Filter {
	return &singleQMFilter{queryMod: sharing.ShareableLinkWhere.LinkType.EQ(linkType.String())}
}

func ShareableLinkResourceIdIs(resourceId uuid.UUID) Filter {
	return &singleQMFilter{queryMod: sharing.ShareableLinkWhere.ResourceID.EQ(resourceId.String())}
}

func ShareableLinkExpiresAtAfter(target time.Time) Filter {
	return &singleQMFilter{queryMod: qm.Where("expires_at is null OR expires_at > ?", target)}
}

func ShareableLinkOrderByCreatedAtDesc() Filter {
	return &singleQMFilter{queryMod: qm.OrderBy(sharing.ShareableLinkColumns.CreatedAt + " desc")}
}
