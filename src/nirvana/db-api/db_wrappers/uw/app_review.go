package uw

import (
	"time"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/mst_referral"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/pricing_experiments"

	"github.com/volatiletech/sqlboiler/v4/types"

	"nirvanatech.com/nirvana/quoting/clearance/enums"

	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	file_upload_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/geo_utils"
	"nirvanatech.com/nirvana/db-api/db_models"
	"nirvanatech.com/nirvana/db-api/db_wrappers/app_review_widget"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/forms"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/quoting/ancillary_coverages"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/underwriting/app_review/actions/reasons"
	"nirvanatech.com/nirvana/underwriting/rule-engine/authorities/fact"
)

// ID fields in both Application & Application Review are clashing
// nolint
type ApplicationReviewAndApplication struct {
	db_models.ApplicationReview `boil:",bind"`
	db_models.Application       `boil:",bind"`
}

// DBApplicationReviewForList is DB model for fields needed in displaying application review list in UW panel
type DBApplicationReviewForList struct {
	db_models.ApplicationReview `boil:",bind"`
	RecommendedAction           null.String `boil:"recommended_action"`
}

// DBApplicationReviewForListV2 is DB model for fields needed in displaying application review list in UW panel
type DBApplicationReviewForListV2 struct {
	ReviewID                  string      `boil:"review_id" json:"review_id" toml:"review_id" yaml:"review_id"`
	ApplicationID             string      `boil:"application_id" json:"application_id" toml:"application_id" yaml:"application_id"`
	Overrides                 null.JSON   `boil:"overrides" json:"overrides" toml:"overrides" yaml:"overrides"`
	UnderwriterID             string      `boil:"underwriter_id" json:"underwriter_id" toml:"underwriter_id" yaml:"underwriter_id"`
	EffectiveDate             null.Time   `boil:"effective_date" json:"effective_date" toml:"effective_date" yaml:"effective_date"`
	State                     string      `boil:"state" json:"state" toml:"state" yaml:"state"`
	ReviewReadinessState      string      `boil:"review_readiness_state" json:"review_readiness_state" toml:"review_readiness_state" yaml:"review_readiness_state"`
	AgencyID                  null.String `boil:"agency_id" json:"agency_id" toml:"agency_id" yaml:"agency_id"`
	TSPEnum                   null.String `boil:"tsp_enum" json:"tsp_enum" toml:"tsp_enum" yaml:"tsp_enum"`
	ClearanceStatus           null.String `boil:"clearance_status" json:"clearance_status" toml:"clearance_status" yaml:"clearance_status"`
	ApplicationCompanyInfo    types.JSON  `boil:"application_company_info" json:"application_company_info" toml:"application_company_info" yaml:"application_company_info"`
	ApplicationTelematicsInfo null.JSON   `boil:"application_telematics_info" json:"application_telematics_info" toml:"application_telematics_info" yaml:"application_telematics_info"`
	RenewalMetadata           null.JSON   `boil:"renewal_metadata" json:"renewal_metadata" toml:"renewal_metadata" yaml:"renewal_metadata"`
	SubmissionCompanyInfo     types.JSON  `boil:"submission_company_info" json:"submission_company_info" toml:"submission_company_info" yaml:"submission_company_info"`
	RecommendedAction         null.String `boil:"recommended_action"`
	TelematicsInfo            null.JSON   `boil:"telematics_info" json:"telematics_info" toml:"telematics_info" yaml:"telematics_info"`
	AgencyName                string      `boil:"agency_name" json:"agency_name" toml:"agency_name" yaml:"agency_name"`
	CurrentStatus             null.String `boil:"current_status" json:"current_status" toml:"current_status" yaml:"current_status"`
}

// DBApplicationReviewForDataCompletion this data model contains the data fetched from DB for data completion app reviews list
type DBApplicationReviewForDataCompletion struct {
	ReviewID                string      `boil:"review_id" json:"review_id" toml:"review_id" yaml:"review_id"`
	ApplicationID           string      `boil:"application_id" json:"application_id" toml:"application_id" yaml:"application_id"`
	EffectiveDate           time.Time   `boil:"effective_date" json:"effective_date" toml:"effective_date" yaml:"effective_date"`
	EffectiveMonth          string      `boil:"effective_month" json:"effective_month" toml:"effective_month" yaml:"effective_month"`
	DOTNumber               int32       `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	State                   string      `boil:"state" json:"state" toml:"state" yaml:"state"`
	ReviewReadinessState    string      `boil:"review_readiness_state" json:"review_readiness_state" toml:"review_readiness_state" yaml:"review_readiness_state"`
	AgencyID                string      `boil:"agency_id" json:"agency_id" toml:"agency_id" yaml:"agency_id"`
	AgencyName              string      `boil:"agency_name" json:"agency_name" toml:"agency_name" yaml:"agency_name"`
	TSPEnum                 null.String `boil:"tsp_enum" json:"tsp_enum" toml:"tsp_enum" yaml:"tsp_enum"`
	TelematicsConnectedDays null.Int32  `boil:"telematics_connected_days" json:"telematics_connected_days" toml:"telematics_connected_days" yaml:"telematics_connected_days"`
	ProducerID              string      `boil:"producer_id" json:"producer_id" toml:"producer_id" yaml:"producer_id"`
	TelematicsStatus        null.String `boil:"telematics_status" json:"telematics_status" toml:"telematics_status" yaml:"telematics_status"`
	ClearanceStatus         null.String `boil:"clearance_status" json:"clearance_status" toml:"clearance_status" yaml:"clearance_status"`
	SubmissionCompanyInfo   types.JSON  `boil:"submission_company_info" json:"submission_company_info" toml:"submission_company_info" yaml:"submission_company_info"`
	UnderwriterID           string      `boil:"underwriter_id" json:"underwriter_id" toml:"underwriter_id" yaml:"underwriter_id"`
}

// TaskDataForDataCompletionReview - task info needed for data completion app review list
type TaskDataForDataCompletionReview struct {
	ReviewID       string `boil:"review_id" json:"review_id" toml:"review_id" yaml:"review_id"`
	Assignee       string `boil:"assignee" json:"assignee" toml:"assignee" yaml:"assignee"`
	TotalTasks     int32  `boil:"total_tasks" json:"total_tasks" toml:"total_tasks" yaml:"total_tasks"`
	CompletedTasks int32  `boil:"completed_tasks" json:"completed_tasks" toml:"completed_tasks" yaml:"completed_tasks"`
}

// TODO: Remove after App Review State Machine is created
type ApplicationReviewCondensed struct {
	ID                   string
	ApplicationID        string
	AgencyID             string
	State                ApplicationReviewState
	TSPConnHandleId      *string
	TSPEnum              *telematics.TSP
	AdditionalEmailInfo  *application.AdditionalEmailInfo
	ReviewReadinessState AppReviewReadinessState
}

type ApplicationReview struct {
	Id            string
	ApplicationID string

	Application          application.Application
	Submission           application.SubmissionObject
	LatestUwSubmission   application.SubmissionObject
	ReviewReadinessSubID *string

	AccountGrade string
	Overrides    Overrides
	Notes        string

	ReviewInfo     ApplicationReviewInfo
	CoverageReview CoverageReview

	BoardsInfo *BoardsInfo

	QuoteInfo       QuoteInfo
	BindableSubInfo BindableSubInfo

	EndStateReasons *EndStateReasons

	State                     ApplicationReviewState
	ReviewReadinessState      AppReviewReadinessState
	TelematicsConnectionState TelematicsConnectionState

	UnderwriterID       uuid.UUID
	CreatedAt           time.Time
	UpdatedAt           time.Time
	EffectiveDate       time.Time
	archivedAt          *time.Time
	AdditionalDocuments *AdditionalDocuments
	PreBindInfo         *PreBindInfo
	PriorityScore       *float64
	PanelsInfo          *PanelsInfo
	NumDeclineCriteria  *int
	AppetiteScore       *AppetiteScore

	CameraSubsidyDetails   *CameraSubsidyDetails
	AuthorityInfo          *AuthorityInfo
	RecommendedActionInfo  *RecommendedActionInfo
	PricingExperimentsInfo *pricing_experiments.PricingExperimentsInfo
	MstReferralReviewInfo  *mst_referral.ReferralAssessment
	CurrentStatus          *ApplicationReviewCurrentStatus
}

type VinVisibility struct {
	ChecklistStatus VinVisibilityChecklistStatus
	Tasks           []VinVisibilityTask
}

func (v *VinVisibility) IsChecklistCompleted() bool {
	if v == nil {
		return false
	}
	return v.ChecklistStatus.CompletedTasks == v.ChecklistStatus.TotalTasks
}

var eligibleVinVisibilityTasks = map[int32]bool{
	1: true,
	2: true,
	3: true,
}

func (v *VinVisibility) GetOverriddenVisibleVinCount() int32 {
	if v == nil {
		return 0
	}
	// ensure that the tasks are completed
	if !v.IsChecklistCompleted() {
		return 0
	}

	var visibleVinCount int32
	for _, task := range v.Tasks {
		// the integer questions have id <= 3
		if task.IsCompleted && eligibleVinVisibilityTasks[task.Id] && task.Value.IntValue != nil {
			visibleVinCount += int32(*task.Value.IntValue)
		}
	}

	return visibleVinCount
}

type VinVisibilityChecklistStatus struct {
	CompletedTasks int32
	TotalTasks     int32
}

//go:generate go run github.com/dmarkham/enumer -type=ValueType -json -trimprefix=Type
type ValueType int

const (
	TypeString ValueType = iota
	TypeBool
	TypeInt
)

type VinVisibilityTask struct {
	Id          int32
	Description string
	IsCompleted bool
	Value       VinVisibilityTaskValue
	ValueType   ValueType
}

type VinVisibilityTaskValue struct {
	StringValue *string
	BoolValue   *bool
	IntValue    *int
}

func (v *VinVisibilityTask) TaskValue() interface{} {
	switch v.ValueType {
	case TypeString:
		return v.Value.StringValue
	case TypeBool:
		return v.Value.BoolValue
	case TypeInt:
		return v.Value.IntValue
	}
	return nil
}

func (v *VinVisibilityTask) IsValuePresent() bool {
	switch v.ValueType {
	case TypeString:
		return v.Value.StringValue != nil
	case TypeBool:
		return v.Value.BoolValue != nil
	case TypeInt:
		return v.Value.IntValue != nil
	}
	return false
}

type ApplicationReviewForList struct {
	ApplicationReview
	RecommendedAction *appetite_factor.RecommendedAction
}

type ApplicationReviewForListV2 struct {
	// from ApplicationReview table
	ReviewID             string
	ApplicationID        string
	Overrides            Overrides
	UnderwriterID        uuid.UUID
	EffectiveDate        time.Time
	State                ApplicationReviewState
	ReviewReadinessState AppReviewReadinessState
	CurrentStatus        *ApplicationReviewCurrentStatus

	// from Application table
	AgencyID                  uuid.UUID
	TSPEnum                   *telematics.TSP
	ClearanceStatus           *enums.ClearanceState
	ApplicationCompanyInfo    *application.CompanyInfo
	ApplicationTelematicsInfo *application.TelematicsInfo
	RenewalMetadata           *application.RenewalMetadata

	// from ApplicationSubmission table
	SubmissionCompanyInfo *application.CompanyInfo

	RecommendedAction *appetite_factor.RecommendedAction
	TelematicsInfo    *appetite_factor.TelematicsInfo

	// from Agency table
	AgencyName string
}

type ApplicationReviewForDataCompletion struct {
	ReviewID                string
	ApplicationID           string
	DOTNumber               int32
	EffectiveDate           time.Time
	EffectiveMonth          string
	State                   ApplicationReviewState
	ReviewReadinessState    AppReviewReadinessState
	AgencyID                uuid.UUID
	AgencyName              string
	TSPEnum                 *telematics.TSP
	TelematicsConnectedDays *int32
	TelematicsStatus        *TelematicsConnectionState
	ClearanceStatus         *enums.ClearanceState
	SubmissionCompanyInfo   *application.CompanyInfo
	AssigneeID              uuid.UUID
	ProducerID              uuid.UUID
	TotalTasks              int32
	CompletedTasks          int32
}

type AuthorityInfo struct {
	Fact        fact.Fact
	GeneratedAt *time.Time
	Metadata    *AuthorityMetadata
}

type AuthorityMetadata struct {
	ApprovedBy             uuid.UUID
	ApprovedAt             time.Time
	ApprovedAtLevel        int32
	HighestApprovedAtLevel int32
	HighestApprovedBy      uuid.UUID
	HighestApprovedAt      time.Time
}

type AdditionalDocuments struct {
	Files   []FileMetadata
	Comment *string
}

func NewUwFiles(appReviewId, appId string, agencyId uuid.UUID) *UwFiles {
	return &UwFiles{
		AppReviewId: appId,
		AppId:       appReviewId,
		AgencyId:    agencyId,
	}
}

// UwDocuments store all additional documents of an app review. Mainly used for authz.
type UwFiles struct {
	AppReviewId string
	AppId       string
	AgencyId    uuid.UUID
}

// TODO: Refractor into common package
type FileMetadata struct {
	Name   string
	Handle *uuid.UUID
	Type   *file_upload_enums.FileType
}

// IsArchived checks if the app review is archived
func (a *ApplicationReview) IsArchived() bool {
	return a.archivedAt != nil
}

// Archive archives an app review using a given timestamp
func (a *ApplicationReview) Archive(t *time.Time) error {
	if t == nil || t.IsZero() {
		return errors.New("unable to archive app with null time")
	}
	if a.archivedAt != nil {
		return errors.New("app already archived")
	}
	a.archivedAt = t
	return nil
}

type CoverageReview struct {
	AutoLiability      *PerCoverageReview
	AutoPhysicalDamage *PerCoverageReview
	GeneralLiability   *PerCoverageReview
	MotorTruckCargo    *PerCoverageReview
}

// nolint:exhaustive
func (cr CoverageReview) GetCoverageReview(coverageType app_enums.Coverage) *PerCoverageReview {
	switch coverageType {
	case app_enums.CoverageAutoLiability:
		return cr.AutoLiability
	case app_enums.CoverageAutoPhysicalDamage:
		return cr.AutoPhysicalDamage
	case app_enums.CoverageGeneralLiability:
		return cr.GeneralLiability
	case app_enums.CoverageMotorTruckCargo:
		return cr.MotorTruckCargo
	default:
		return nil
	}
}

func (cr CoverageReview) IsCoverageSelected(coverageType app_enums.Coverage) bool {
	coverageReview := cr.GetCoverageReview(coverageType)
	return coverageReview != nil && coverageReview.State == ApplicationReviewCoverageStateApproved
}

type PerCoverageReview struct {
	ReviewInfo ApplicationReviewInfo
	State      ApplicationReviewCoverageState
}

type TargetPriceRange struct {
	Min int32
	Max int32
}

type TargetPrice struct {
	Range               *TargetPriceRange
	RatePerHundredMiles *float32
}

type TargetPriceAPD struct {
	Range        *TargetPriceRange
	PercentOfTIV *float32
}

type TargetPriceOverride struct {
	IsTargetPriceAvailable bool
	Total                  *TargetPrice
	AL                     *TargetPrice
	APD                    *TargetPriceAPD
	MTC                    *TargetPrice
}

type Overrides struct {
	Locations         *[]*Location
	RadiusOfOperation *[]*application.MileageRadiusRecord
	ProjectedMileage  *int
	// UnitCount is the overridden projected unit information that gets updated on the 'Operations' tab
	UnitCount                    *int
	UnitList                     []EquipmentListRecord
	DriverList                   *[]*DriverListRecord
	LossSummary                  *[]*application.LossRunSummaryPerCoverage
	LargeLosses                  *[]*LargeLoss
	PackageType                  *app_enums.IndicationOptionTag
	CommodityDistribution        *application.CommodityDistribution
	EffectiveDate                *time.Time
	VehicleZoneDistribution      *[]application.VehicleZoneRecord
	CoverageInfo                 *CoverageInfo
	AncillaryCoverages           map[app_enums.Coverage]AncillaryCoverage
	NegotiatedRates              *application.NegotiatedRates
	RatingAddress                *application.TerminalLocation
	ModelPinConfig               *application.ModelPinConfig
	MileageEstimateOverride      *MileageEstimateOverride
	RiskScoreOverride            *RiskScoreOverride
	NumberOfVehicleCameras       *int
	ALCollateral                 *int
	PremiumTaxRecords            []application.PremiumTaxRecord
	SafetyScoreSelectedForRating *float32                      // deprecated
	SafetyScoreSelectedForReview *SafetyScoreSelectedForReview // decorated safety score selected by UW for review
	VinVisibility                *VinVisibility
	TargetPrice                  *TargetPriceOverride
	MinimumMileageGuarantee      *MinimumMileageGuaranteeOverride
	MstReferralRulesReview       []mst_referral.ReferralRuleReview
	TerminalLocations            []application.TerminalLocation
}

type MinimumMileageGuaranteeOverride struct {
	IsMinimumMileageGuaranteed bool
}

type RiskScoreOverride struct {
	Version app_review_widget.AppReviewWidgetVersion
}

//go:generate go run github.com/dmarkham/enumer -type=MileageEstimateCategory -json -trimprefix=MileageEstimateCategory
type MileageEstimateCategory int

const (
	MileageEstimateCategoryHigher MileageEstimateCategory = iota
	MileageEstimateCategoryLower
	MileageEstimateCategorySame
	MileageEstimateCategoryNoDataAvailable
)

type MileageEstimateOverride struct {
	Version           app_review_widget.AppReviewWidgetVersion
	Category          *MileageEstimateCategory
	Reason            *string
	AdditionalDetails *string
}

type CoverageInfo struct {
	Coverages                        []CoverageDetails
	CoveragesWithCombinedDeductibles *CombinedDeductibleCoverages
}

type AncillaryCoverage struct {
	CoverageType    app_enums.Coverage
	Deductible      int32
	Limit           *int32
	CoverageDetails ancillary_coverages.AncillaryCoverage
	IsEnabled       bool
}

func (ac AncillaryCoverage) GetIntLimit() *int {
	if ac.Limit != nil {
		limit := int(*ac.Limit)
		return &limit
	}
	return nil
}

func (ac AncillaryCoverage) GetIntDeductible() int {
	return int(ac.Deductible)
}

// GetSymbolsAndDefinitions is a helper function to ensure we get all the Symbols data from the correct place
func (ac AncillaryCoverage) GetSymbolsAndDefinitions() *[]application.SymbolAndDefinition {
	return ac.CoverageDetails.SymbolsAndDefinitions
}

type CoverageDetails struct {
	CoverageType app_enums.Coverage
	Deductible   *int32
	Limit        *int32
}

type CombinedDeductibleCoverages struct {
	CombinedCoveragesList []application.CombinedCoverages
}

type CombinedCoverages map[app_enums.Coverage]bool

type DriverListRecord struct {
	application.DriverListRecord
	ExperienceStartDate null.Time
	IsDeletedByUW       null.Bool
	IsCreatedByUW       null.Bool // true means that driver was added by underwriter in UW panel
}

type EquipmentListRecord struct {
	application.EquipmentListRecord
	IsDeletedByUW bool
	IsCreatedByUW bool
}

type LargeLoss struct {
	Date         time.Time
	CoverageType app_enums.Coverage
	LossIncurred int32
	Description  string
}

type Location struct {
	Type        string
	Address     string
	Coordinates geo_utils.Location
}

type PanelReviewInfo struct {
	IsReviewed bool
	Credit     float32
	Merit      int32
	Comments   string
}

type GlobalPanelReviewInfo struct {
	PanelReviewInfo
	Coverages WidgetReviewInfo
}

type OperationsPanelReviewInfo struct {
	PanelReviewInfo
	YearsInBusiness      WidgetReviewInfo
	ProjectedInformation WidgetReviewInfo
	GaragingLocation     WidgetReviewInfo
	RadiusOfOperation    WidgetReviewInfo
	HazardZones          WidgetReviewInfo
	OperatingClasses     WidgetReviewInfo
	Routes               WidgetReviewInfo
	FleetHistory         WidgetReviewInfo
	Commodities          WidgetReviewInfo
}

type EquipmentsPanelReviewInfo struct {
	PanelReviewInfo
	Units WidgetReviewInfo
}

type DriversPanelReviewInfo struct {
	PanelReviewInfo
	List WidgetReviewInfo
}

type SafetyPanelReviewInfo struct {
	PanelReviewInfo
	SafetyScore            WidgetReviewInfo
	BasicScoreThreshold    WidgetReviewInfo
	BasicScoreTrend        WidgetReviewInfo
	SevereViolations       WidgetReviewInfo
	OutOfServiceViolations WidgetReviewInfo
	DotRating              WidgetReviewInfo
	ISSScore               WidgetReviewInfo
	CrashRecord            WidgetReviewInfo
}

type FinancialsPanelReviewInfo struct {
	PanelReviewInfo
	Data WidgetReviewInfo
}

type LossesPanelReviewInfo struct {
	PanelReviewInfo
	LossSummary  WidgetReviewInfo
	LargeLosses  WidgetReviewInfo
	LossAverages WidgetReviewInfo
}

type OverviewPanelReviewInfo struct {
	PanelReviewInfo
}

type WidgetReviewInfo struct {
	Credit float32
	Merit  int32
}

type ApplicationReviewInfo struct {
	ApplicationReviewId string

	AggregateCredit float32
	AggregateMerit  int32

	Global     GlobalPanelReviewInfo
	Operations OperationsPanelReviewInfo
	Equipments EquipmentsPanelReviewInfo
	Drivers    DriversPanelReviewInfo
	Safety     SafetyPanelReviewInfo
	Financials FinancialsPanelReviewInfo
	Losses     LossesPanelReviewInfo
	// omitting nil Overview from resultant json for a cleaner backward compatibility
	Overview *OverviewPanelReviewInfo `json:"Overview,omitempty"`
}

type QuoteInfo struct {
	IndicationOptionsIDs []string
	SelectedIndicationID string
	JobRunId             *jtypes.JobRunId
	PullMvrs             *bool
	FetchAttractScore    *bool
}

type BindableSubInfo struct {
	SubmissionID string
	JobRunId     *jtypes.JobRunId
}

type BoardsInfo struct {
	Version         *string
	CommitTimestamp *time.Time
}

// Cursor denotes the delimiter used for pagination
type Cursor struct {
	EffectiveDate time.Time
	AppReviewID   string
}

// Options are used to pass filtering options & page delimiters
// on the `GetPaginatedReviews` method
type Options struct {
	AgencyId                    *uuid.UUID
	PageSize                    *int
	Query                       *string
	Tab                         *underwriting.ApplicationReviewTab
	UnderwriterID               *string
	EffectiveDateOnOrAfter      *time.Time
	EffectiveDateOnOrBefore     *time.Time
	RecommendedActions          []appetite_factor.RecommendedAction
	SortBy                      *underwriting.PaginatedListSortBy
	SortDirection               *underwriting.PaginatedListSortDirection
	FilterNonTerminalStatesFlag *bool
}

// DataCompletionPaginatedReviewsOptions - query options for fetching data completion app reviews list
type DataCompletionPaginatedReviewsOptions struct {
	AgencyId uuid.UUID
	PageSize int
	Query    *string
	Tab      underwriting.ApplicationReviewDataCompletionTab
}

type CountOptions struct {
	Query                   *string
	UnderwriterID           *string
	EffectiveDateOnOrAfter  *time.Time
	EffectiveDateOnOrBefore *time.Time
	AgencyID                *uuid.UUID
	RecommendedActions      []appetite_factor.RecommendedAction
}

type CloseReasonsStruct struct {
	Reasons          []reasons.ReasonStruct
	WinCarrier       *string
	Comments         string
	IntentionToQuote *bool
}

type DeclineReasonsStruct struct {
	Reasons  []reasons.ReasonStruct
	Comments string
}

type BindStateReasonsStruct struct {
	PrimaryWinReason     string
	PrimaryWinReasonId   string
	SecondaryWinReason   string
	SecondaryWinReasonId string
	ExternalNote         string
}

type RollbackReasonsStruct struct {
	Comments string
}

type EndStateReasons struct {
	CloseReasons     *CloseReasonsStruct
	DeclineReasons   *DeclineReasonsStruct
	BindStateReasons *BindStateReasonsStruct
	RollbackReasons  *RollbackReasonsStruct
}

type BillingInfo struct {
	CommissionRate       float64
	DepositAmount        float64
	PaymentMethod        oapi_common.PaymentMethod
	ExternalNote         *string
	DepositPaymentMethod *forms.DepositPaymentMethod
}

type PreBindInfo struct {
	BillingInfo *BillingInfo
}

type CameraSubsidyDetails struct {
	NumberOfCameras    int
	SubsidyAmount      *float64
	CameraProvider     telematics.TSP // deprecated
	AreCamerasRequired *bool          // deprecated
}

type RecommendedActionInfo struct {
	Feedback string // feedback provided by UW on the recommended action
	// IsApplicationAgainstUwGuidelines is used to indicate if the application is against UW guidelines
	// provided by UW in the app review
	IsApplicationAgainstUwGuidelines *bool                     `json:"IsApplicationAgainstUwGuidelines,omitempty"`
	Version                          *appetite_factors.Version `json:"Version,omitempty"` // Version for the appetite score use-case
}

// SafetyScoreSelectedForReview represents the safety score selected by UW for review (primarily used by data science
// folks for analysis)
type SafetyScoreSelectedForReview struct {
	IsShortHaul            *bool
	Score                  *float32
	Timestamp              time.Time
	VinCount               *float32
	WindowEnd              time.Time
	WindowStart            time.Time
	WindowType             string
	ScoreType              string
	ScoreVersion           string
	ExposeScoreTrend       *bool
	ExposeScoreTrendReason []string // multiple reasons can be present
	IsConfidentScore       *bool
	LowConfidentReason     *string
	Discount               *float32
	MarketCategory         *string
	IsBackfilled           *bool
	UWRubricVersion        *string // is always available from DS, ptr for backwards compatibility
	LatestChangeReason     *string
	AutoSelected           *bool
}
