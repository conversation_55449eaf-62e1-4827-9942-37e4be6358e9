// Code generated by "enumer -type=MileageEstimateCategory -json -trimprefix=MileageEstimateCategory"; DO NOT EDIT.

package uw

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _MileageEstimateCategoryName = "HigherLowerSameNoDataAvailable"

var _MileageEstimateCategoryIndex = [...]uint8{0, 6, 11, 15, 30}

const _MileageEstimateCategoryLowerName = "higherlowersamenodataavailable"

func (i MileageEstimateCategory) String() string {
	if i < 0 || i >= MileageEstimateCategory(len(_MileageEstimateCategoryIndex)-1) {
		return fmt.Sprintf("MileageEstimateCategory(%d)", i)
	}
	return _MileageEstimateCategoryName[_MileageEstimateCategoryIndex[i]:_MileageEstimateCategoryIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _MileageEstimateCategoryNoOp() {
	var x [1]struct{}
	_ = x[MileageEstimateCategoryHigher-(0)]
	_ = x[MileageEstimateCategoryLower-(1)]
	_ = x[MileageEstimateCategorySame-(2)]
	_ = x[MileageEstimateCategoryNoDataAvailable-(3)]
}

var _MileageEstimateCategoryValues = []MileageEstimateCategory{MileageEstimateCategoryHigher, MileageEstimateCategoryLower, MileageEstimateCategorySame, MileageEstimateCategoryNoDataAvailable}

var _MileageEstimateCategoryNameToValueMap = map[string]MileageEstimateCategory{
	_MileageEstimateCategoryName[0:6]:        MileageEstimateCategoryHigher,
	_MileageEstimateCategoryLowerName[0:6]:   MileageEstimateCategoryHigher,
	_MileageEstimateCategoryName[6:11]:       MileageEstimateCategoryLower,
	_MileageEstimateCategoryLowerName[6:11]:  MileageEstimateCategoryLower,
	_MileageEstimateCategoryName[11:15]:      MileageEstimateCategorySame,
	_MileageEstimateCategoryLowerName[11:15]: MileageEstimateCategorySame,
	_MileageEstimateCategoryName[15:30]:      MileageEstimateCategoryNoDataAvailable,
	_MileageEstimateCategoryLowerName[15:30]: MileageEstimateCategoryNoDataAvailable,
}

var _MileageEstimateCategoryNames = []string{
	_MileageEstimateCategoryName[0:6],
	_MileageEstimateCategoryName[6:11],
	_MileageEstimateCategoryName[11:15],
	_MileageEstimateCategoryName[15:30],
}

// MileageEstimateCategoryString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func MileageEstimateCategoryString(s string) (MileageEstimateCategory, error) {
	if val, ok := _MileageEstimateCategoryNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _MileageEstimateCategoryNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to MileageEstimateCategory values", s)
}

// MileageEstimateCategoryValues returns all values of the enum
func MileageEstimateCategoryValues() []MileageEstimateCategory {
	return _MileageEstimateCategoryValues
}

// MileageEstimateCategoryStrings returns a slice of all String values of the enum
func MileageEstimateCategoryStrings() []string {
	strs := make([]string, len(_MileageEstimateCategoryNames))
	copy(strs, _MileageEstimateCategoryNames)
	return strs
}

// IsAMileageEstimateCategory returns "true" if the value is listed in the enum definition. "false" otherwise
func (i MileageEstimateCategory) IsAMileageEstimateCategory() bool {
	for _, v := range _MileageEstimateCategoryValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for MileageEstimateCategory
func (i MileageEstimateCategory) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for MileageEstimateCategory
func (i *MileageEstimateCategory) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("MileageEstimateCategory should be a string, got %s", data)
	}

	var err error
	*i, err = MileageEstimateCategoryString(s)
	return err
}
