package mst_referral

import "time"

// ReferralRule represents strictly represents the attributes that define a referral rule.
type ReferralRule struct {
	Id      ReferralRuleId
	Type    ReferralRuleType
	Program ReferralRuleProgram
}

// ReferralRuleReview is a wrapper around ReferralRule that includes
// additional fields to track the referral status set by System (optionally, set by UW).
type ReferralRuleReview struct {
	ReferralRule `json:",inline"`
	RuleStatus   ReferralRuleStatus // value set by the system or underwriter user
}

type ReferralAssessment struct {
	ReviewStatus  ReferralReviewStatus // overall referral status
	Rules         []ReferralRuleReview
	SummaryPoints string
	UpdatedAt     time.Time // UpdatedAt helps to determine when was the last time the rules
	// were executed or later, updated by the underwriter.
	Metadata *Metadata
}

// Metadata holds additional information about the referral assessment.
type Metadata struct {
	DecisionSubmittedTimestamp *time.Time // first time the underwriter submits a decision
	ReferralEmailSentTimestamp *time.Time // first time the referral email was sent
	UnblockedFromMstTimestamp  *time.Time // first time the referral was marked unblocked from MST
}

type ReferralRuleId int

//go:generate go run github.com/dmarkham/enumer -type=ReferralRuleId -trimprefix=ReferralRuleId -json
const (
	ReferralRuleIdAnySymbol1Or61 ReferralRuleId = iota + 1
	ReferralRuleIdAnyPolicyWithPolicyTermLongerThan12Months
	ReferralRuleIdAnythingThatRequiresBackdatingAndDoesNotMeetTheRequirements
	ReferralRuleIdAnyClassOrExposureListedAsExcluded
	ReferralRuleIdAllRisksRequestingCSL
	ReferralRuleIdAccountsWithTwoOrMoreLosses
	ReferralRuleIdAnySingleClaimWithFatality
	ReferralRuleIdAnyRiskThatIsAlreadyOnTransversePaper
	ReferralRuleIdAllNewVentures
	ReferralRuleIdAnyRiskGeneratingAnUnmodifiedPremium
	ReferralRuleIdAnyAccountWith3YearLossRatio
	// California-state specific rules
	ReferralRuleIdAnySubHaulExposure
	ReferralRuleIdLongHaulPriceForALCoverage
	ReferralRuleIdShortToMediumHaulPriceForALCoverage
	ReferralRuleIdAPDCoverageRateWithTIV
	ReferralRuleIdAPDCoverageDeductible
)

type ReferralRuleType int

// Referral Determination Modes:
//
// - Automatic:
//   The system will automatically determine if the risk is eligible for referral.
//
// - Semi-Automatic:
//   The system will determine if the risk is potentially eligible for referral,
//   and the underwriter will have to manually review the risk.

//go:generate go run github.com/dmarkham/enumer -type=ReferralRuleType -trimprefix=ReferralRuleType -json
const (
	ReferralRuleTypeAutomatic ReferralRuleType = iota + 1
	ReferralRuleTypeSemiAutomatic
)

type ReferralReviewStatus int

//go:generate go run github.com/dmarkham/enumer -type=ReferralReviewStatus -trimprefix=ReferralReviewStatus -json
const (
	ReferralReviewStatusUnspecified ReferralReviewStatus = iota // basically, zero value
	ReferralReviewStatusReviewNotEligibleBySystem
	ReferralReviewStatusReviewNotEligibleByUW
	ReferralReviewStatusReviewPending
	ReferralReviewStatusReviewDecisionSubmitted
	ReferralReviewStatusReferralEmailSent
)

type ReferralRuleProgram int

//go:generate go run github.com/dmarkham/enumer -type=ReferralRuleProgram -trimprefix=ReferralRuleProgram -json
const (
	ReferralRuleProgramAdmitted ReferralRuleProgram = iota + 1
	ReferralRuleProgramNonAdmitted
)

type ReferralRuleStatus int

//go:generate go run github.com/dmarkham/enumer -type=ReferralRuleStatus -trimprefix=ReferralRuleStatus -json
const (
	ReferralRuleStatusActive ReferralRuleStatus = iota + 1
	ReferralRuleStatusInactive
)
