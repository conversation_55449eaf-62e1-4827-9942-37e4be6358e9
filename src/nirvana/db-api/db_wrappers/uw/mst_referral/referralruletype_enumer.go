// Code generated by "enumer -type=ReferralRuleType -trimprefix=ReferralRuleType -json"; DO NOT EDIT.

package mst_referral

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _ReferralRuleTypeName = "AutomaticSemiAutomatic"

var _ReferralRuleTypeIndex = [...]uint8{0, 9, 22}

const _ReferralRuleTypeLowerName = "automaticsemiautomatic"

func (i ReferralRuleType) String() string {
	i -= 1
	if i < 0 || i >= ReferralRuleType(len(_ReferralRuleTypeIndex)-1) {
		return fmt.Sprintf("ReferralRuleType(%d)", i+1)
	}
	return _ReferralRuleTypeName[_ReferralRuleTypeIndex[i]:_ReferralRuleTypeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ReferralRuleTypeNoOp() {
	var x [1]struct{}
	_ = x[ReferralRuleTypeAutomatic-(1)]
	_ = x[ReferralRuleTypeSemiAutomatic-(2)]
}

var _ReferralRuleTypeValues = []ReferralRuleType{ReferralRuleTypeAutomatic, ReferralRuleTypeSemiAutomatic}

var _ReferralRuleTypeNameToValueMap = map[string]ReferralRuleType{
	_ReferralRuleTypeName[0:9]:       ReferralRuleTypeAutomatic,
	_ReferralRuleTypeLowerName[0:9]:  ReferralRuleTypeAutomatic,
	_ReferralRuleTypeName[9:22]:      ReferralRuleTypeSemiAutomatic,
	_ReferralRuleTypeLowerName[9:22]: ReferralRuleTypeSemiAutomatic,
}

var _ReferralRuleTypeNames = []string{
	_ReferralRuleTypeName[0:9],
	_ReferralRuleTypeName[9:22],
}

// ReferralRuleTypeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ReferralRuleTypeString(s string) (ReferralRuleType, error) {
	if val, ok := _ReferralRuleTypeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ReferralRuleTypeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ReferralRuleType values", s)
}

// ReferralRuleTypeValues returns all values of the enum
func ReferralRuleTypeValues() []ReferralRuleType {
	return _ReferralRuleTypeValues
}

// ReferralRuleTypeStrings returns a slice of all String values of the enum
func ReferralRuleTypeStrings() []string {
	strs := make([]string, len(_ReferralRuleTypeNames))
	copy(strs, _ReferralRuleTypeNames)
	return strs
}

// IsAReferralRuleType returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ReferralRuleType) IsAReferralRuleType() bool {
	for _, v := range _ReferralRuleTypeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ReferralRuleType
func (i ReferralRuleType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ReferralRuleType
func (i *ReferralRuleType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ReferralRuleType should be a string, got %s", data)
	}

	var err error
	*i, err = ReferralRuleTypeString(s)
	return err
}
