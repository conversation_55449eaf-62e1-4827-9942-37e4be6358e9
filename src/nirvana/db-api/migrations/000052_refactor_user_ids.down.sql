-- Re-crete final_submission table
CREATE TYPE final_submission_status AS ENUM('processing', 'success', 'error');

CREATE TABLE IF NOT EXISTS final_submission (
    id              serial primary key,
    application_id  uuid not null references application,
    "status"        final_submission_status not null,

    created_at      timestamptz not null default now(),
    updated_at      timestamptz not null default now()
);

CREATE INDEX IF NOT EXISTS final_submission_application_id_index on final_submission(application_id);
-- Re-update application table
-- Note: we're unable to re-create the foreign key as that could break if a
-- new application was created with a producer not being present on
-- auth_user table 
DROP INDEX IF EXISTS application_producer_id_index;
DROP INDEX IF EXISTS application_created_by_index;
DROP INDEX IF EXISTS application_tsp_conn_handle_id_index;
-- Re-update submission table
ALTER TABLE submission DROP COLUMN underwriter_id;
ALTER TABLE submission ADD COLUMN underwriter_id integer;
DROP INDEX IF EXISTS submission_underwriter_id_index;
DROP INDEX IF EXISTS submission_selected_indication_id;
DROP INDEX IF EXISTS submission_indication_options_ids;
-- Re-update MVRs tables
-- table for MVR records, note that we do not reference either
-- application or mvr_fetch_batch because multiple applications
-- may have the same drivers
create table if not exists mvr (
    id              serial primary key,
    -- MVR details
    state           varchar(2),
    first_name      text,
    middle_name     text,
    last_name       text,
    street_address  text,
    -- status is needed to track cases where a DL# is present in application but
    -- we encounter an error when trying to fetch MVR
    "status"        mvr_status not null,
    error_details   text,
    total_points    int,
    report_date     date,
    address_line_2  text,
    dl_number       varchar(19),
    ssn             varchar(9),
    is_dppa         boolean,
    dob             date,
    driver_class    text,
    driver_status   text,
    date_issued     date,
    date_expires    date,
    restrictions    text,
    -- end MVR details
    created_at      timestamptz not null default now(),
    updated_at      timestamptz not null default now()
);

create index if not exists mvr_state_index on mvr(state);
create index if not exists mvr_dl_number_index on mvr(dl_number);

create table if not exists violation (
     id              serial primary key,
     mvr_id          int not null references mvr,

     type            text,
     start_date      date,
     end_date        date,
     description     text,
     code            text,
     points          int,

     created_at      timestamptz not null default now(),
     updated_at      timestamptz not null default now()
);

