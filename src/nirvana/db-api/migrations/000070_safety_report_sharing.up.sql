DROP INDEX IF EXISTS shareable_link_tenant_id_index;
ALTER TABLE sharing.shareable_link DROP COLUMN IF EXISTS tenant_id;

ALTER TABLE sharing.shareable_link ADD COLUMN fleet_id uuid;
ALTER TABLE sharing.shareable_link ADD COLUMN agency_id uuid;
ALTER TABLE sharing.shareable_link ADD COLUMN service_account_user_id uuid;

CREATE INDEX shareable_link_fleet_id_index ON sharing.shareable_link(fleet_id);
CREATE INDEX shareable_link_agency_id_index ON sharing.shareable_link(agency_id);
CREATE INDEX shareable_link_user_id_index ON sharing.shareable_link(service_account_user_id);
