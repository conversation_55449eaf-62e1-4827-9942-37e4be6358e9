package postups

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_models/non_fleet"
)

func init() {
	RegisterNirvanaDbPostup(317, "add_eff_date_in_nf_app_review", AddEffDateToNFAppReview)
}

func AddEffDateToNFAppReview(ctx context.Context, tx *sql.Tx) error {
	log.Info(ctx, "Execution of AddEffDateToNFAppReview started")

	appReviews, err := non_fleet.ApplicationReviews().All(ctx, tx)
	if err != nil {
		return errors.Wrap(err, "failed to get all application reviews")
	}

	var rowsProcessed, rowsUpdated int
	for _, appReview := range appReviews {
		rowsProcessed++

		appID := appReview.ApplicationID
		application, err := non_fleet.FindApplication(ctx, tx, appID)
		if err != nil {
			return errors.Wrapf(err, "failed to find application with id %s", appID)
		}

		if application.EffectiveDate.Valid {
			appReview.EffectiveDate = null.TimeFrom(application.EffectiveDate.Time)
		}

		if application.EffectiveDateTo.Valid {
			appReview.EffectiveDateTo = null.TimeFrom(application.EffectiveDateTo.Time)
		}
		_, err = appReview.Update(ctx, tx, boil.Infer())
		if err != nil {
			return errors.Wrapf(err, "failed to update appReview with id %s", appReview.ID)
		}

		rowsUpdated++
	}

	log.Info(ctx, "Execution of AddEffDateToNFAppReview completed",
		log.Int("rows_processed", rowsProcessed), log.Int("rows_updated", rowsUpdated),
	)
	return nil
}
