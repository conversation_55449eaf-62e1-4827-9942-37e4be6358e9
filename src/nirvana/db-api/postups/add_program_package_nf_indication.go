package postups

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_models/non_fleet"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
)

func init() {
	RegisterNirvanaDbPostup(257, "add_program_package_type_nf_indication", addProgramPackageTypeNFIndication)
}

func addProgramPackageTypeNFIndication(ctx context.Context, tx *sql.Tx) error {
	log.Info(ctx, "Execution of postup addProgramPackageTypeNFIndication started")

	nfIndOptions, err := non_fleet.IndicationOptions().All(ctx, tx)
	if err != nil {
		return errors.Wrap(err, "failed to get all nf indication options")
	}

	var rowsUpdated, totalRows int

	for _, ind := range nfIndOptions {
		totalRows++
		if !ind.ProgramType.Valid {
			ind.ProgramType = null.StringFrom(
				policy_enums.ProgramTypeNonFleetCanopiusNRB.String())
		}

		packageType := ""
		if ind.PackageType.Valid {
			packageType = ind.PackageType.String
		}

		if packageType == "" {
			ind.PackageType = null.StringFrom(app_enums.IndicationOptionTagCustom.String())
		}

		rowsAffected, err := ind.Update(ctx, tx, boil.Infer())
		if err != nil {
			return errors.Wrapf(
				err, "failed to update indication with id %s", ind.ID,
			)
		}

		rowsUpdated++
		log.Info(ctx, "updated program and package type for nf indications",
			log.Int64("rowsAffected", rowsAffected))
	}

	log.Info(
		ctx, "successfully updated program and package type for nf indication",
		log.Int("rowsUpdated", rowsUpdated), log.Int("totalRows", totalRows),
	)
	return nil
}
