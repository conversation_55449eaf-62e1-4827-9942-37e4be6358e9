package migrator

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"go.uber.org/multierr"

	"nirvanatech.com/nirvana/common-go/db_migration_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/path_utils"
	"nirvanatech.com/nirvana/db-api/testcontainer"
	fmcsa_postups "nirvanatech.com/nirvana/fmcsa/postups"
	"nirvanatech.com/nirvana/infra/fx/testcontainers"
)

type FMCSAMigrator struct {
	postgresC *testcontainers.PostgresContainer
}

func NewFMCSAMigrator(postgresC *testcontainers.PostgresContainer) *FMCSAMigrator {
	return &FMCSAMigrator{
		postgresC: postgresC,
	}
}

func (f *FMCSAMigrator) Container() *testcontainers.PostgresContainer {
	return f.postgresC
}

func (f *FMCSAMigrator) OpenMigrator(ctx context.Context) (*MigratorCloser, error) {
	dsn := f.postgresC.DSN(string(testcontainer.FmcsaDBTemplate))
	db, err := sql.Open(postgres, dsn)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to connect to postgres DB running in docker container")
	}

	path, err := path_utils.WorkspacePath("nirvana/fmcsa/migrations")
	if err != nil {
		return nil, err
	}

	migrationOptions := db_migration_utils.NewMigratorOptions(
		path,
		true, /*PostupsEnabled*/
		fmcsa_postups.GetFmcsaDbPostupExecutorForVersion,
	)

	migrator, err := db_migration_utils.InitMigrator(ctx, db, migrationOptions, false)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to init fmcsa DB migrator for path %s", path)
	}

	return &MigratorCloser{
		db:       db,
		migrator: migrator,
	}, nil
}

func (f *FMCSAMigrator) VerifyLatest(ctx context.Context) (retErr error) {
	migrator, err := f.OpenMigrator(ctx)
	if err != nil {
		return err
	}
	defer func() {
		retErr = multierr.Append(retErr, migrator.Close())
	}()

	return migrator.VerifyLatest()
}

func (f *FMCSAMigrator) Migrate(ctx context.Context) (retErr error) {
	log.Info(ctx, "Creating and migrating postgres fmcsa_template database...")
	if err := createTemplateDB(ctx, f.postgresC, testcontainer.FmcsaDBTemplate); err != nil {
		return err
	}

	migrator, err := f.OpenMigrator(ctx)
	if err != nil {
		return err
	}
	defer func() {
		retErr = multierr.Append(retErr, migrator.Close())
	}()

	return migrator.RunMigrate(ctx, &db_migration_utils.MigrateUp{})
}
