package policyevent_test

import (
	"testing"
	"time"

	policyconstants "nirvanatech.com/nirvana/policy/constants"

	policyenums "nirvanatech.com/nirvana/policy/enums"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	policyevent "nirvanatech.com/nirvana/events/policy-event"
)

// TODO: (manan) FronterX related changes
const cancellationReason = "Out of business"

func TestNewCancelled(t *testing.T) {
	newEffectiveDateTo := time_utils.NewDate(2023, time.October, 10).ToTime()
	cancellationTime := time.Now()

	validPolicy := getValidCancelledPolicy(t, newEffectiveDateTo, cancellationTime)

	invalidPolicyNoModified := getValidCancelledPolicy(t, newEffectiveDateTo, cancellationTime)
	invalidPolicyNoModified.Miscellaneous.ModifiedEffectiveDateTo = nil

	invalidPolicyNoCancellation := getValidCancelledPolicy(t, newEffectiveDateTo, cancellationTime)
	invalidPolicyNoCancellation.Miscellaneous.CancellationTime = nil

	testCases := []struct {
		name       string
		p          policy.Policy
		shouldFail bool
		want       *policyevent.Cancelled
	}{
		{
			name:       "valid new cancelled event",
			p:          validPolicy,
			shouldFail: false,
			want: &policyevent.Cancelled{
				PolicyNumber:            validPolicy.PolicyNumber.String(),
				PolicyID:                validPolicy.Id,
				ApplicationID:           validPolicy.ApplicationId,
				OriginalEffectiveDateTo: validPolicy.EffectiveDateTo,
				NewEffectiveDateTo:      newEffectiveDateTo,
				CancellationTime:        cancellationTime,
				CancellationReason:      cancellationReason,
			},
		},
		{
			name:       "invalid new cancelled event with null modified effective date",
			p:          invalidPolicyNoModified,
			shouldFail: true,
			want:       nil,
		},
		{
			name:       "invalid new cancelled event with null cancellation date",
			p:          invalidPolicyNoCancellation,
			shouldFail: true,
			want:       nil,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			got, err := policyevent.NewCancelledOld(
				tt.p,
				cancellationReason,
			)
			if tt.shouldFail {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestCancelled_Validate(t *testing.T) {
	newEffectiveDateTo := time_utils.NewDate(2023, time.October, 10).ToTime()
	cancellationTime := time.Now()
	p := getValidCancelledPolicy(t, newEffectiveDateTo, cancellationTime)

	cancelledEvent, err := policyevent.NewCancelledOld(p, cancellationReason)
	assert.NoError(t, err)
	assert.NoError(t, cancelledEvent.Validate(), "unexpected validation error")

	failureTestCases := []struct {
		name          string
		policyFactory func() policy.Policy
	}{
		{
			name: "ModifiedEffectiveDateTo is zero date",
			policyFactory: func() policy.Policy {
				newP := p
				newP.Miscellaneous = &policy.MiscellaneousInfo{
					ModifiedEffectiveDateTo: pointer_utils.ToPointer(time.Time{}),
					CancellationTime:        &cancellationTime,
					CancellationReason:      pointer_utils.ToPointer(cancellationReason),
				}
				return newP
			},
		},
		{
			name: "CancellationFilingDate is zero date",
			policyFactory: func() policy.Policy {
				newP := p
				newP.Miscellaneous = &policy.MiscellaneousInfo{
					ModifiedEffectiveDateTo: &newEffectiveDateTo,
					CancellationTime:        pointer_utils.ToPointer(time.Time{}),
					CancellationReason:      pointer_utils.ToPointer(cancellationReason),
				}
				return newP
			},
		},
	}
	for _, tt := range failureTestCases {
		t.Run(tt.name, func(t *testing.T) {
			event, err := policyevent.NewCancelledOld(
				tt.policyFactory(),
				cancellationReason,
			)
			assert.NoError(t, err)
			assert.Error(t, event.Validate())
		})
	}
}

func getValidCancelledPolicy(t *testing.T, newEffectiveDateTo time.Time, cancellationTime time.Time) policy.Policy {
	t.Helper()

	pn, err := policy.NewNirvanaPolicyNumber(
		policyconstants.NirvanaPolicyCarrierALPrefix,
		"0100010",
		time_utils.NewDate(2023, time.July, 1).ToTime(),
	)
	require.NoError(t, err)

	p := policy.NewPolicyImpl(&policy.NewPolicyArgs{
		Id:               uuid.New(),
		PolicyNumber:     pn,
		Version:          1,
		InsuredName:      "Name",
		ApplicationId:    uuid.New(),
		SubmissionId:     uuid.New(),
		DocumentHandleId: uuid.New(),
		State:            policyenums.PolicyStateCancelled,
		ProgramType:      enums.ProgramTypeFleet,
	})
	p.EffectiveDate = newEffectiveDateTo.AddDate(0, -11, 0)
	p.EffectiveDateTo = newEffectiveDateTo.AddDate(0, 1, 0)
	p.Miscellaneous = &policy.MiscellaneousInfo{
		ModifiedEffectiveDateTo: &newEffectiveDateTo,
		CancellationTime:        &cancellationTime,
		CancellationReason:      pointer_utils.ToPointer(cancellationReason),
	}
	return *p
}
