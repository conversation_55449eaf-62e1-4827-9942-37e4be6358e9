package quoting_events

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"gopkg.in/segmentio/analytics-go.v3"

	"nirvanatech.com/nirvana/common-go/str_utils"
	policy_eums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/infra/constants"
)

type QuotingTSPLinkOpened struct {
	ApplicationID uuid.UUID
	AgencyID      uuid.UUID
	ProgramType   policy_eums.ProgramType
}

// NewQuotingTSPLinkOpened creates a new event struct and returns it
func NewQuotingTSPLinkOpened(
	appID uuid.UUID,
	agencyID uuid.UUID,
	programType policy_eums.ProgramType,
) events.Event {
	return &QuotingTSPLinkOpened{
		ApplicationID: appID,
		AgencyID:      agencyID,
		ProgramType:   programType,
	}
}

func (f *QuotingTSPLinkOpened) Upload(ctx context.Context, deps events.EventDeps) error {
	// Avoid uploading events from test agencies
	if _, ok := constants.TestAgencies()[f.AgencyID]; ok {
		return nil
	}
	// Upload to segment
	if err := deps.SegmentClient.Enqueue(analytics.Track{
		AnonymousId: uuid.Nil.String(),
		Event:       f.Name(),
		Properties: analytics.NewProperties().
			Set("ApplicationID", f.ApplicationID).
			Set("AgencyID", f.AgencyID.String()).
			Set("ProgramType", f.ProgramType.String()),
		Integrations: analytics.NewIntegrations().EnableAll(),
	}); err != nil {
		return errors.Wrap(err, "unable to upload to segment")
	}
	return nil
}

// Validate function checks the sanity of the data fields in
// the event
func (f *QuotingTSPLinkOpened) Validate() error {
	// Sanity checks for inputs
	if f.ApplicationID == uuid.Nil {
		return errors.New("application id of the event is null")
	}
	if f.AgencyID == uuid.Nil {
		return errors.New("agency id of the event is null")
	}
	if f.ProgramType == policy_eums.ProgramTypeInvalid {
		return errors.New("programType of the event is invalid")
	}
	return nil
}

// Name returns the name of the event sent to the destination source
func (f *QuotingTSPLinkOpened) Name() string {
	return `QuotingTSPLinkOpened`
}

// UserVisibleName returns the visible name of the event
func (f *QuotingTSPLinkOpened) UserVisibleName() string {
	return str_utils.PrettyEnumString(f.Name(), "")
}

var _ events.Event = (*QuotingTSPLinkOpened)(nil)
