package builders

import (
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/experiments/models"
)

type ExperimentVariantBuilder struct {
	variant models.ExperimentVariant
}

func NewExperimentVariantBuilder() *ExperimentVariantBuilder {
	return &ExperimentVariantBuilder{variant: models.ExperimentVariant{}}
}

func (b *ExperimentVariantBuilder) WithId(id uuid.UUID) *ExperimentVariantBuilder {
	b.variant.Id = id
	return b
}

func (b *ExperimentVariantBuilder) WithExperimentId(id uuid.UUID) *ExperimentVariantBuilder {
	b.variant.ExperimentId = id
	return b
}

func (b *ExperimentVariantBuilder) WithName(name string) *ExperimentVariantBuilder {
	b.variant.Name = name
	return b
}

func (b *ExperimentVariantBuilder) WithDescription(desc string) *ExperimentVariantBuilder {
	b.variant.Description = &desc
	return b
}

func (b *ExperimentVariantBuilder) WithCreatedAt(ts time.Time) *ExperimentVariantBuilder {
	b.variant.CreatedAt = ts
	return b
}

func (b *ExperimentVariantBuilder) WithUpdatedAt(ts time.Time) *ExperimentVariantBuilder {
	b.variant.UpdatedAt = ts
	return b
}

func (b *ExperimentVariantBuilder) WithMockData_TestOnly() *ExperimentVariantBuilder {
	desc := "Mock variant description"
	b.variant = models.ExperimentVariant{
		Id:           uuid.New(),
		ExperimentId: uuid.New(),
		Name:         "Mock Variant",
		Description:  &desc,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	return b
}

func (b *ExperimentVariantBuilder) Build() models.ExperimentVariant {
	return b.variant
}
