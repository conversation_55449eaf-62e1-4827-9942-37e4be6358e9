load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "endorsementapp",
    srcs = [
        "client.go",
        "metrics.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_client/jira/endorsementapp",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/common/application",
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/api-server/handlers/common/ib",
        "//nirvana/api-server/interceptors/application/deps",
        "//nirvana/common-go/log",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/infra/authz",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/helper",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_ctreminiom_go_atlassian//pkg/infra/models",
        "@com_github_google_uuid//:uuid",
    ],
)
