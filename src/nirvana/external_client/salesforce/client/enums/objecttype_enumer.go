// Code generated by "enumer -type=ObjectType -json -trimprefix=Stage"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _ObjectTypeName = "OpportunityAccountContactUserQuoteQuoteLineItemCarrierMappingCampaignCampaignMemberLead"

var _ObjectTypeIndex = [...]uint8{0, 11, 18, 25, 29, 34, 47, 61, 69, 83, 87}

const _ObjectTypeLowerName = "opportunityaccountcontactuserquotequotelineitemcarriermappingcampaigncampaignmemberlead"

func (i ObjectType) String() string {
	i -= 1
	if i < 0 || i >= ObjectType(len(_ObjectTypeIndex)-1) {
		return fmt.Sprintf("ObjectType(%d)", i+1)
	}
	return _ObjectTypeName[_ObjectTypeIndex[i]:_ObjectTypeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ObjectTypeNoOp() {
	var x [1]struct{}
	_ = x[Opportunity-(1)]
	_ = x[Account-(2)]
	_ = x[Contact-(3)]
	_ = x[User-(4)]
	_ = x[Quote-(5)]
	_ = x[QuoteLineItem-(6)]
	_ = x[CarrierMapping-(7)]
	_ = x[Campaign-(8)]
	_ = x[CampaignMember-(9)]
	_ = x[Lead-(10)]
}

var _ObjectTypeValues = []ObjectType{Opportunity, Account, Contact, User, Quote, QuoteLineItem, CarrierMapping, Campaign, CampaignMember, Lead}

var _ObjectTypeNameToValueMap = map[string]ObjectType{
	_ObjectTypeName[0:11]:       Opportunity,
	_ObjectTypeLowerName[0:11]:  Opportunity,
	_ObjectTypeName[11:18]:      Account,
	_ObjectTypeLowerName[11:18]: Account,
	_ObjectTypeName[18:25]:      Contact,
	_ObjectTypeLowerName[18:25]: Contact,
	_ObjectTypeName[25:29]:      User,
	_ObjectTypeLowerName[25:29]: User,
	_ObjectTypeName[29:34]:      Quote,
	_ObjectTypeLowerName[29:34]: Quote,
	_ObjectTypeName[34:47]:      QuoteLineItem,
	_ObjectTypeLowerName[34:47]: QuoteLineItem,
	_ObjectTypeName[47:61]:      CarrierMapping,
	_ObjectTypeLowerName[47:61]: CarrierMapping,
	_ObjectTypeName[61:69]:      Campaign,
	_ObjectTypeLowerName[61:69]: Campaign,
	_ObjectTypeName[69:83]:      CampaignMember,
	_ObjectTypeLowerName[69:83]: CampaignMember,
	_ObjectTypeName[83:87]:      Lead,
	_ObjectTypeLowerName[83:87]: Lead,
}

var _ObjectTypeNames = []string{
	_ObjectTypeName[0:11],
	_ObjectTypeName[11:18],
	_ObjectTypeName[18:25],
	_ObjectTypeName[25:29],
	_ObjectTypeName[29:34],
	_ObjectTypeName[34:47],
	_ObjectTypeName[47:61],
	_ObjectTypeName[61:69],
	_ObjectTypeName[69:83],
	_ObjectTypeName[83:87],
}

// ObjectTypeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ObjectTypeString(s string) (ObjectType, error) {
	if val, ok := _ObjectTypeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ObjectTypeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ObjectType values", s)
}

// ObjectTypeValues returns all values of the enum
func ObjectTypeValues() []ObjectType {
	return _ObjectTypeValues
}

// ObjectTypeStrings returns a slice of all String values of the enum
func ObjectTypeStrings() []string {
	strs := make([]string, len(_ObjectTypeNames))
	copy(strs, _ObjectTypeNames)
	return strs
}

// IsAObjectType returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ObjectType) IsAObjectType() bool {
	for _, v := range _ObjectTypeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ObjectType
func (i ObjectType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ObjectType
func (i *ObjectType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ObjectType should be a string, got %s", data)
	}

	var err error
	*i, err = ObjectTypeString(s)
	return err
}
