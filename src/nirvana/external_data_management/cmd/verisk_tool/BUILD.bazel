load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "verisk_tool_lib",
    srcs = ["main.go"],
    importpath = "nirvanatech.com/nirvana/external_data_management/cmd/verisk_tool",
    visibility = ["//visibility:private"],
    deps = [
        "//nirvana/common-go/aws_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/random_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/mvr",
        "//nirvana/external_data_management/mvr/cacheserver",
        "//nirvana/external_data_management/mvr/verisk",
        "//nirvana/infra/fx/appfx/cobrafx",
        "//nirvana/rating/data_fetching/mvr_fetching",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_spf13_cobra//:cobra",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)

go_binary(
    name = "verisk_tool",
    embed = [":verisk_tool_lib"],
    visibility = ["//visibility:public"],
)
