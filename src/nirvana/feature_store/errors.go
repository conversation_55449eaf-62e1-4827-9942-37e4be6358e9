package feature_store

import (
	"database/sql"

	"github.com/cockroachdb/errors"
)

var (
	TimestampFormatError error = errors.New("Timestamp is specified in an incorrect format." +
		" Please use time.RFC3339Nano format.")
	FeatureValueMarshalError   = errors.New("Marshalling of feature value failed.")
	FeatureValueUnmarshalError = errors.New("Unmarshalling of feature value failed. " +
		"Check if you are using proper version.")
	TimestampMetadataMissingError  = errors.New("Timestamp is required in metadata")
	NoFeatureValueFoundError       = errors.WrapWithDepth(0, sql.ErrNoRows, "No feature value record found in DB for the given keys.")
	UnsupportedFilterOperatorError = errors.New("The filter operator is not supported on this attribute.")
	DatabaseError                  = errors.New("There was an error with the database operation")
	MetadataMarshalError           = errors.New("Unable to marshal metadata (unexpected)")
	OutdatedFeatureValueError      = errors.New("The latest feature value record is older than cache duration")
	FeatureGeneratorError          = errors.New("Feature generator for this feature faced an error")
)
