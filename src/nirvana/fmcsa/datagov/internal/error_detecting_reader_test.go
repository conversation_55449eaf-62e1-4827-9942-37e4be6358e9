package internal

import (
	"bytes"
	_ "embed"
	"io"
	"testing"

	"github.com/stretchr/testify/require"
)

//go:embed testdata/Crashes.2025-04-09-095753Z.corrupt.csv
var corruptCSVData []byte

//go:embed testdata/Crashes.2025-04-09-095753Z.valid.csv
var validCSVData []byte

func TestErrorDetectingReader(t *testing.T) {
	_, err := io.ReadAll(NewErrorDetectingReader(bytes.NewReader(corruptCSVData)))
	require.Error(t, err)

	data, err := io.ReadAll(NewErrorDetectingReader(bytes.NewReader(validCSVData)))
	require.NoError(t, err)
	require.Equal(t, validCSVData, data)
}
