package internal_test

import (
	"path"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/fmcsa/datagov/enums"
	"nirvanatech.com/nirvana/fmcsa/datagov/internal"
)

func TestParseFilename(t *testing.T) {
	ts := time.Date(2024, 0o4, 26, 23, 0o1, 26, 0o0, time.UTC)

	testcases := []struct {
		filename     string
		expectedFile enums.SourceFile
		timestamp    time.Time
		expectError  bool
	}{
		{
			filename:     "BOC3.2024-04-26-230126Z.csv.gz",
			expectedFile: enums.SourceFileBOC3,
			timestamp:    ts,
		},
		{
			filename:     "inshist/2024/04/InsHist.2024-04-26-230126Z.csv.gz",
			expectedFile: enums.SourceFileInsHist,
			timestamp:    ts,
		},
		{
			filename:    "invalid.2024-04-26-230126Z.csv.gz",
			expectError: true,
		},
		{
			filename:    "InsHist.20240426230126Z.csv.gz",
			expectError: true,
		},
		{
			filename:    "BOC3.Aggregate.2024-04-26-230126Z.csv.gz",
			expectError: true,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.filename, func(t *testing.T) {
			actualFile, actualTs, err := internal.ParseFilename(testcase.filename)
			if testcase.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, testcase.expectedFile, actualFile)
				require.Equal(t, testcase.timestamp, actualTs)

				filename := internal.Filename(testcase.expectedFile, testcase.timestamp)
				require.Equal(t, path.Base(testcase.filename), filename)
			}
		})
	}
}
