package models

import (
	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"

	datagov_models "nirvanatech.com/nirvana/fmcsa/db_models/datagov"
)

func InspectionFromDB(inspection *datagov_models.Inspection) (*Inspection, error) {
	retval := &Inspection{}
	if err := copier.CopyWithOption(retval, inspection, copier.Option{IgnoreEmpty: false, DeepCopy: true}); err != nil {
		return nil, errors.Wrapf(err, "failed to copy inspection %+v from DB", inspection)
	}
	return retval, nil
}

func InspectionToDB(inspection *Inspection) (*datagov_models.Inspection, error) {
	retval := &datagov_models.Inspection{}
	if err := copier.CopyWithOption(retval, inspection, copier.Option{IgnoreEmpty: false, DeepCopy: true}); err != nil {
		return nil, errors.Wrapf(err, "failed to copy inspection %+v to DB", inspection)
	}
	return retval, nil
}
