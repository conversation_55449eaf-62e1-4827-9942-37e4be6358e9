package models

import (
	"time"

	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/vehicles"

	"github.com/volatiletech/null/v8"
)

// See https://www.fmcsa.dot.gov/registration/mcmis-catalog-inspection-file-data-element-definitions#IU

// CHANGE_DATE,INSPECTION_ID,INSP_UNIT_ID,INSP_UNIT_TYPE_ID,INSP_UNIT_NUMBER,INSP_UNIT_MAKE,INSP_UNIT_COMPANY,INSP_UNIT_LICENSE,
// INSP_UNIT_LICENSE_STATE,INSP_UNIT_VEHICLE_ID_NUMBER,INSP_UNIT_DECAL,INSP_UNIT_DECAL_NUMBER
// 20210524 2133,72741076,*********,9,2,VANR,109407,781858,IL,5V8VC532XMM109407,Y,30040954
type Vehicle struct {
	ChangeDate     time.Time   `csv:"CHANGE_DATE"`                 // 20210524 2133
	InspectionID   int64       `csv:"INSPECTION_ID"`               // 72741076
	VehicleID      int64       `csv:"INSP_UNIT_ID" copier:"ID"`    // ********* - This is a unique identifying number assigned to an inspection record.
	VehicleTypeID  int         `csv:"INSP_UNIT_TYPE_ID"`           // 9 - ST - Semi Trailer
	SequenceNumber int         `csv:"INSP_UNIT_NUMBER"`            // 2
	Make           null.String `csv:"INSP_UNIT_MAKE"`              // VANR
	Company        null.String `csv:"INSP_UNIT_COMPANY"`           // 109407 or EGL3750
	LicensePlate   null.String `csv:"INSP_UNIT_LICENSE"`           // 781858 or P798756
	LicenseState   null.String `csv:"INSP_UNIT_LICENSE_STATE"`     // IL
	VIN            null.String `csv:"INSP_UNIT_VEHICLE_ID_NUMBER"` // 5V8VC532XMM109407
	DecalIssued    null.Bool   `csv:"INSP_UNIT_DECAL"`             // Y
	DecalNumber    null.String `csv:"INSP_UNIT_DECAL_NUMBER"`      // 30040954 - CVSA Decal Number

	Checksum  string    `csv:"-"`
	CreatedAt time.Time `csv:"-"`
	UpdatedAt time.Time `csv:"-"`
	DeletedAt null.Time `csv:"-"`
}

func (v *Vehicle) SHA1() string {
	return v.Checksum
}

func (v *Vehicle) Initialize(sha1 string, csvTimestamp, now time.Time) {
	v.Checksum = sha1
	v.CreatedAt = csvTimestamp
	v.UpdatedAt = csvTimestamp
}

func (v *Vehicle) PrimaryKey() vehicles.VehicleID {
	return vehicles.VehicleID(v.VehicleID)
}
