/*
This model is the source of truth for a DOT#, storing all the available
information. We combine each data source  as a json column with an additional
IS_VALID flag columns that indicates if that data is up-to-date and usable.

*/

{{ config(materialized='table') }}

with
census_extended as (select * from {{ ref('census_extended') }}),
census_latest as (select * from {{ ref('census_extract_latest') }}),

-- This query validates that the latest census file was not extracted more than
-- two months ago. Additionally, it groups all the census information on an
-- object to facilitate the later query.
is_valid_census as (
    select
        dot_number,
        dump_date,
        object_construct(
            'dump_date', dump_date,
            'is_active', is_active,
            'company_name', company_name,
            'company_name_dba', company_name_dba,
            'physical_address_nation', physical_address_nation,
            'physical_address_state', physical_address_state,
            'physical_address_city', physical_address_city,
            'physical_address_county_code', physical_address_county_code,
            'physical_address_street', physical_address_street,
            'physical_address_zipcode', physical_address_zipcode,
            'total_trucks', total_trucks,
            'total_power_units', total_power_units,
            'total_drivers', total_drivers,
            'total_cdl_drivers', total_cdl_drivers,
            'total_mileage', total_mileage,
            'mcs_150_mileage_year', mcs_150_mileage_year,
            'cargo_types_carried', cargo_types_carried,
            'other_cargo_carried', other_cargo_carried,
            'hazmat_carried', hazmat_carried,
            'rating', rating,
            'rating_date', rating_date,
            'telephone_number', telephone_number,
            'cellphone_number', cellphone_number,
            'fax_number', fax_number,
            'email_address', email_address
        ) as json_col,
        IFF(
            timediff(month, dump_date, CURRENT_DATE()) > 2,
            false,
            true
        ) as is_valid
    from census_latest
),

operational_history_objects as (
    select
        dot_number,
        date,
        object_construct(
            'date', date,
            'total_power_units', total_power_units,
            'total_mileage', total_mileage,
            'mcs_150_mileage_year', mcs_150_mileage_year,
            'segment', segment
        ) as operational_history_object
    from census_extended
    where year(date) >= 2018
),
operational_history as (
    select
        dot_number,
        array_agg(operational_history_object)
        within group (order by operational_history_object:date desc) as json_col
    from operational_history_objects group by dot_number
),

-- This query validates that the date of the last object of
-- CENSUS_EXTENDED (most recent object) matches the most recent
-- row from CENSUS_EXTRACT(i.e., CENSUS_LATEST). This means that the history
-- is up-to-date, and therefore valid to be used. We also store all the
-- history in this table to facilitate the later query as well.
-- TODO: change >= for = when we finish scrapping all data. We're currently
-- scrapping data, so there are cases were history could be > last census
-- and we don't want to invalidate those cases.
is_valid_operational_history as (
    select
        O.*,
        IFF(
            GET(json_col, 0):date >= date_trunc('month', DATE(C.dump_date)),
            true,
            false
        ) as is_valid
    from operational_history as O
    inner join census_latest as C
    on C.dot_number = O.dot_number
),

-- This query is the source of truth, storing the data of each source as an
-- object with an additional flag to check it's validity.
dot_fmcsa_details as (
    select
        C.dot_number as dot_number,
        C.dump_date as census_dump_date,
        C.json_col as census,
        C.is_valid as is_valid_census,
        H.json_col as operational_history,
        H.is_valid as is_valid_operational_history
    from is_valid_census as C
    left join is_valid_operational_history as H
    on C.dot_number = H.dot_number
)

select * from dot_fmcsa_details
