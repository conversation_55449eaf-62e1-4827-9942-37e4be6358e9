load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "census",
    srcs = [
        "gen.go",
        "gen_combined.go",
    ],
    embedsrcs = [
        "stages.sql.tmpl",
        "tables.sql.tmpl",
        "tables.public.sql.tmpl",
        "tables.public_day.sql.tmpl",
        "census_combined.sql.tmpl",
        "census_public_combined.sql.tmpl",
        "census_public_combined_day.sql.tmpl",
    ],
    importpath = "nirvanatech.com/nirvana/fmcsa/dbt/source_data/census",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/path_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/fmcsa/dbt/source_data",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "census_test",
    srcs = [
        "day_level_test.go",
        "gen_test.go",
    ],
    embed = [":census"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "@com_github_stretchr_testify//require",
    ],
)
