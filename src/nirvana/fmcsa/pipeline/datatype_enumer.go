// Code generated by "enumer -type=DataType -json"; DO NOT EDIT.

package pipeline

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _DataTypeName = "PublicPrivate"

var _DataTypeIndex = [...]uint8{0, 6, 13}

const _DataTypeLowerName = "publicprivate"

func (i DataType) String() string {
	i -= 1
	if i < 0 || i >= DataType(len(_DataTypeIndex)-1) {
		return fmt.Sprintf("DataType(%d)", i+1)
	}
	return _DataTypeName[_DataTypeIndex[i]:_DataTypeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _DataTypeNoOp() {
	var x [1]struct{}
	_ = x[Public-(1)]
	_ = x[Private-(2)]
}

var _DataTypeValues = []DataType{Public, Private}

var _DataTypeNameToValueMap = map[string]DataType{
	_DataTypeName[0:6]:       Public,
	_DataTypeLowerName[0:6]:  Public,
	_DataTypeName[6:13]:      Private,
	_DataTypeLowerName[6:13]: Private,
}

var _DataTypeNames = []string{
	_DataTypeName[0:6],
	_DataTypeName[6:13],
}

// DataTypeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func DataTypeString(s string) (DataType, error) {
	if val, ok := _DataTypeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _DataTypeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to DataType values", s)
}

// DataTypeValues returns all values of the enum
func DataTypeValues() []DataType {
	return _DataTypeValues
}

// DataTypeStrings returns a slice of all String values of the enum
func DataTypeStrings() []string {
	strs := make([]string, len(_DataTypeNames))
	copy(strs, _DataTypeNames)
	return strs
}

// IsADataType returns "true" if the value is listed in the enum definition. "false" otherwise
func (i DataType) IsADataType() bool {
	for _, v := range _DataTypeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for DataType
func (i DataType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for DataType
func (i *DataType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("DataType should be a string, got %s", data)
	}

	var err error
	*i, err = DataTypeString(s)
	return err
}
