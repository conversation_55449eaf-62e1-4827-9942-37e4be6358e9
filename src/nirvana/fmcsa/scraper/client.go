package scraper

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/go-resty/resty/v2"

	"nirvanatech.com/nirvana/common-go/log"
)

const (
	usernameQueryParamName          = "username"
	passwordQueryParamName          = "password"
	twoFAQueryParamName             = "twoFA"
	twoFactorUrlParamName           = "twoFactorUrl"
	browserWSEndpointQueryParamName = "browserWSEndpoint"
	dotNumberQueryParamName         = "dot"
	complete2FAEndpoint             = "/complete2FAScrape/"
	initiate2FAEndpoint             = "/initiate2FAScrape/"
	saferScraperEndpoint            = "/saferscrape/"
	publicEndpoint                  = "/dotscrape/"
	historicalMeasuresEndpoint      = "/historical-measures/"
	invalidDotError                 = "DOT invalid OR server error."
	invalid2FAError                 = "invalid2FA"
	invalidCredentialsError         = "invalidCredentials"
)

type Client struct {
	resty *resty.Client
	url   string // base URL of form http://host:port with no trailing slash
}

func NewURLClient(url string) *Client {
	return &Client{
		resty: resty.New(),
		url:   string(url),
	}
}

func (c *Client) DoSaferScrape(ctx context.Context, dotNumber string) (*SaferScraperResponse, error) {
	log.Info(ctx, "Entered DoSaferScrape", log.String("dotNumber", dotNumber))
	restyResp, err := c.newRequest(ctx).
		SetQueryParams(map[string]string{
			"dot": dotNumber,
		}).
		Post(c.url + saferScraperEndpoint)
	if err != nil {
		log.Error(
			ctx,
			"Failed to receive HTTP response from safer scraper",
			log.String("dotNumber", dotNumber),
			log.Err(err),
		)
		return nil, err
	}
	if restyResp.StatusCode() != http.StatusOK {
		errStr := restyResp.String()
		if strings.Contains(errStr, invalidDotError) {
			return nil, errors.New("Invalid DOT")
		}
		log.Error(
			ctx,
			"Failed to parse error response from safer scraper",
			log.String("dotNumber", dotNumber),
			log.String("response", errStr),
		)
		return nil, errors.Wrap(ErrUnmarshalErrorResponse, errStr)
	}

	resp := new(SaferScraperResponse)
	err = json.Unmarshal(restyResp.Body(), resp)
	if err != nil {
		log.Error(
			ctx,
			"Failed to parse HTTP response body from scraper",
			log.String("dotNumber", dotNumber),
			log.Err(err),
		)
		return nil, errors.Wrap(ErrUnmarshalErrorResponse, err.Error())
	}
	return resp, nil
}

// DoInitiate2FAScrape returns the response from the initiate 2fa scrape request endpoint
// for the fmcsa_ani scraper
func (c *Client) DoInitiate2FAScrape(ctx context.Context, username, password string) (*string, error) {
	restyResp, err := c.newRequest(ctx).
		SetQueryParams(map[string]string{
			usernameQueryParamName: username,
			passwordQueryParamName: password,
		}).
		Post(c.url + initiate2FAEndpoint)
	if err != nil {
		log.Error(
			ctx,
			"Failed to receive HTTP response from scraper",
			log.Err(err),
		)
		return nil, err
	}
	if restyResp.StatusCode() != http.StatusOK {
		errStr := restyResp.String()
		if strings.Contains(errStr, invalidCredentialsError) {
			return nil, ErrInvalidCredentials
		}
		log.Error(
			ctx,
			"Unexpected error response from scraper",
			log.String("response", errStr),
		)
		return nil, errors.Wrap(ErrUnmarshalErrorResponse, errStr)
	}

	result, err := strconv.Unquote(string(restyResp.Body()))
	if err != nil {
		log.Error(ctx, "Failed to unquote result from scraper", log.Err(err))
	}

	return &result, nil
}

// DoComplete2FAScrape returns the response from the authenticated request endpoint
// for fmcsa_ani scraper.
func (c *Client) DoComplete2FAScrape(
	ctx context.Context,
	twoFA string,
	browserWSEndpoint string,
	twoFactorUrl string,
) (*AuthenticatedResponseV1, error) {
	log.Info(ctx, "Entered DoComplete2FAScrape")
	restyResp, err := c.newRequest(ctx).
		SetQueryParams(map[string]string{
			twoFAQueryParamName:             twoFA,
			browserWSEndpointQueryParamName: browserWSEndpoint,
			twoFactorUrlParamName:           twoFactorUrl,
		}).
		Post(c.url + complete2FAEndpoint)
	if err != nil {
		log.Error(
			ctx,
			"Failed to receive HTTP response from scraper",
			log.Err(err),
		)
		return nil, err
	}
	if restyResp.StatusCode() != http.StatusOK {
		errStr := restyResp.String()
		if strings.Contains(errStr, invalid2FAError) {
			return nil, ErrInvalid2FACode
		}
		log.Error(
			ctx,
			"Unexpected error response from scraper",
			log.String("response", errStr),
		)
		return nil, errors.Wrap(ErrUnmarshalErrorResponse, errStr)
	}
	resp := new(AuthenticatedResponseV1)
	err = json.Unmarshal(restyResp.Body(), resp)
	if err != nil {
		log.Error(
			ctx,
			"Failed to parse HTTP response body from scraper",
			log.Err(err),
		)
		return nil, errors.Wrap(ErrUnmarshal, err.Error())
	}
	return resp, nil
}

// DoPublicScrape returns the response from the Public request
// endpoint for fmcsa_ani scraper.
func (c *Client) DoPublicScrape(
	ctx context.Context, dotNumber int64,
) (*PublicResponse, error) {
	restyResp, err := c.newRequest(ctx).
		SetQueryParams(map[string]string{
			dotNumberQueryParamName: strconv.FormatInt(dotNumber, 10),
		}).
		Post(c.url + publicEndpoint)
	if err != nil {
		log.Error(
			ctx,
			"Failed to receive HTTP response from scraper",
			log.Int64("dotNumber", dotNumber),
			log.Err(err),
		)
		return nil, err
	}
	if restyResp.StatusCode() != http.StatusOK {
		errStr := restyResp.String()
		if strings.Contains(errStr, invalidDotError) {
			return nil, ErrInvalidDot
		}
		log.Error(
			ctx,
			"Failed to parse error response from scraper",
			log.Int64("dotNumber", dotNumber),
			log.String("response", errStr),
		)
		return nil, errors.Wrap(ErrUnmarshalErrorResponse, errStr)
	}
	resp := new(PublicResponse)
	err = json.Unmarshal(restyResp.Body(), resp)
	if err != nil {
		log.Error(
			ctx,
			"Failed to parse HTTP response body from scraper",
			log.Int64("dotNumber", dotNumber),
			log.Err(err),
		)
		return nil, errors.Wrap(ErrUnmarshal, err.Error())
	}
	return resp, nil
}

// DoHistoricalMeasuresScrape returns the historical BASIC measures from the
// historical measures' endpoint for fmcsa_ani scraper.
func (c *Client) DoHistoricalMeasuresScrape(
	ctx context.Context, dotNumber int64,
) (*HistoricalMeasuresResponse, error) {
	restyResp, err := c.newRequest(ctx).
		SetQueryParams(map[string]string{
			dotNumberQueryParamName: strconv.FormatInt(dotNumber, 10),
		}).
		Post(c.url + historicalMeasuresEndpoint)
	if err != nil {
		log.Error(
			ctx,
			"Failed to receive HTTP response from scraper",
			log.Int64("dotNumber", dotNumber),
			log.Err(err),
		)
		return nil, err
	}
	if restyResp.StatusCode() != http.StatusOK {
		errStr := restyResp.String()
		if strings.Contains(errStr, invalidDotError) {
			return nil, ErrInvalidDot
		}
		log.Error(
			ctx,
			"Failed to parse error response from scraper",
			log.Int64("dotNumber", dotNumber),
			log.String("response", errStr),
		)
		return nil, errors.Wrap(ErrUnmarshalErrorResponse, errStr)
	}
	resp := new(HistoricalMeasuresResponse)
	err = json.Unmarshal(restyResp.Body(), resp)
	if err != nil {
		log.Error(
			ctx,
			"Failed to parse HTTP response body from scraper",
			log.Int64("dotNumber", dotNumber),
			log.Err(err),
		)
		return nil, errors.Wrap(ErrUnmarshal, err.Error())
	}
	return resp, nil
}

func (c *Client) newRequest(ctx context.Context) *resty.Request {
	return c.resty.R().EnableTrace().SetContext(ctx)
}
