package scraper

import (
	"encoding/json"

	"github.com/cockroachdb/errors"

	fmcsa_models "nirvanatech.com/nirvana/fmcsa/models"
)

var (
	ErrInvalidCredentials     = errors.New("Invalid username/password")
	ErrInvalid2FACode         = errors.New("Invalid 2FA code")
	ErrInvalidDot             = errors.New("scraper received invalid dot")
	ErrUnmarshal              = errors.New("failed to unmarshal response from scraper")
	ErrUnmarshalErrorResponse = errors.New("failed to unmarshal error response from scraper")
)

type SaferScraperResponse struct {
	DotNumber string           `json:"DOTNO"`
	Status    string           `json:"Status"`
	Exists    bool             `json:"Exists"`
	Data      SaferScraperData `json:"Data"`
	Error     struct {
		Message string `json:"Message"`
		Stack   string `json:"Stack"`
	} `json:"Error"`
}

type SaferScraperData struct {
	IDOperations            fmcsa_models.SaferIDOperations            `json:"IDOperations"`
	InspectionCrashesUS     fmcsa_models.SaferInspectionCrashesUS     `json:"InspectionCrashesUS"`
	InspectionCrashesCanada fmcsa_models.SaferInspectionCrashesCanada `json:"InspectionCrashesCanada"`
	SafetyRating            fmcsa_models.SaferSafetyRating            `json:"SafetyRating"`
}

// PublicResponse holds all the public information scraped from https://ai.fmcsa.dot.gov/
// using the `fmcsa_ani_scraper`.
type PublicResponse struct {
	Summary           fmcsa_models.ANISummaryPublic
	InspectionHistory []*fmcsa_models.ANIInspectionInfoPublic `json:"Inspections"`
	CrashHistory      []*fmcsa_models.ANICrashInfoPublic      `json:"Crashes"`
}

var _ json.Unmarshaler = (*PublicResponse)(nil)

func (p *PublicResponse) UnmarshalJSON(bytes []byte) error {
	type alias PublicResponse
	aux := &struct {
		*alias
	}{
		alias: (*alias)(p),
	}
	if err := json.Unmarshal(bytes, aux); err != nil {
		return err
	}
	if err := json.Unmarshal(bytes, &aux.Summary); err != nil {
		return err
	}
	return nil
}

// HistoricalMeasuresResponse holds all the "Public" BASIC measures scraped from
// https://ai.fmcsa.dot.gov/ using the `fmcsa_ani_scraper`.
type HistoricalMeasuresResponse struct {
	UnsafeDriving                  fmcsa_models.ANIHistoricalMeasures `json:"UnsafeDriving"`
	HOSCompliance                  fmcsa_models.ANIHistoricalMeasures `json:"HOSCompliance"`
	VehicleMaintenance             fmcsa_models.ANIHistoricalMeasures `json:"VehicleMaint"`
	ControlledSubstancesAndAlcohol fmcsa_models.ANIHistoricalMeasures `json:"DrugsAlcohol"`
	DriverFitness                  fmcsa_models.ANIHistoricalMeasures `json:"DriverFitness"`
}

// AuthenticatedResponseV1 holds all the information scraped from https://ai.fmcsa.dot.gov/
// with username/password/2fa using the `fmcsa_ani_scraper`.
type AuthenticatedResponseV1 struct {
	// the information from https://ai.fmcsa.dot.gov/SMS/Carrier/<DOT>/CompleteProfile.aspx
	Summary           fmcsa_models.ANISummaryAuthenticated
	InspectionHistory []*fmcsa_models.ANIInspectionInfoAuthenticated `json:"Inspections"`
	CrashHistory      []*fmcsa_models.ANICrashInfoAuthenticated      `json:"Crashes"`
	DOT               string                                         `json:"DOT"`
}

var _ json.Unmarshaler = (*AuthenticatedResponseV1)(nil)

func (a *AuthenticatedResponseV1) UnmarshalJSON(bytes []byte) error {
	type alias AuthenticatedResponseV1
	aux := &struct {
		*alias
	}{
		alias: (*alias)(a),
	}
	if err := json.Unmarshal(bytes, aux); err != nil {
		return err
	}
	if err := json.Unmarshal(bytes, &aux.Summary); err != nil {
		return err
	}
	return nil
}
