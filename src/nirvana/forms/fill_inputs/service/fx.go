package service

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs/business_auto"
	"nirvanatech.com/nirvana/forms/fill_inputs/fleet"
	"nirvanatech.com/nirvana/forms/fill_inputs/handler_registry"
	"nirvanatech.com/nirvana/forms/fill_inputs/non_fleet_admitted"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
)

func registerHandlers() {
	handler_registry.Register(enums.ProgramTypeFleet, fleet.NewInputsHandler)
	handler_registry.Register(enums.ProgramTypeNonFleetAdmitted, non_fleet_admitted.NewInputsHandler)
	handler_registry.Register(enums.ProgramTypeBusinessAuto, business_auto.NewInputsHandler)
}

func init() {
	fxregistry.Register(
		fx.Options(
			fx.Provide(
				fx.Annotate(newFillInputsService, fx.As(new(FillInputsManager))),
			),
			fx.Invoke(registerHandlers),
		),
	)
}
