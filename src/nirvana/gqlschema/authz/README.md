Steps to bootstrap local DB with agents to start using our various apps with locally with required Authz: 
## Usage
1. Create a super-user, to be used to create other accounts: 
```bash
bazel run //nirvana/db-api/cmd/auth_management_tool -- createSuperuser 
  --user_email=<EMAIL> --user_password=Test12345 
  --user_first_name=Sanket --user_last_name=Rathi
```
2. Start graphiql by running graphql server, api server and start `yarn start:graphiql`.
3. Login using super user login and password. 
4. Run the following on graphiql to create a new agency:
```
mutation createAgenc($name: string!) {
  createAgency(name: $name){
    id
  }
}
```
with
Variables:
```
{
   "name": "<dev_agency>"
}
```

5. Create a new user with following mutations:
```
mutation createUser($firstName: string!, $lastName: string!, $email: string!, $password: string!, $roles: [createUserRoleArgs_InputObject!]) {
  createUser(firstName: $firstName, lastName: $lastName, email: $email, password: $password, roles: $roles){
    id
  }
}
```
with variables:
```
{
  "firstName": "<PERSON>",
  "lastName": "<PERSON>",
  "email": "<EMAIL>",
  "password": "Matt123",
  "roles": [{
    "agencyID": "<id_received_in_above_query>",
    "group": "AgencyAdminRole"
  }]
}
```