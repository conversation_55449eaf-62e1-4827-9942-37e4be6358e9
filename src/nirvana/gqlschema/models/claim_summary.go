package models

import (
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

type ClaimSummary struct {
	Id            uuid.UUID `graphql:"id,key"`
	ClaimId       uuid.UUID `graphql:"claimId"`
	Title         string    `graphql:"title"`
	IntervalStart time.Time `graphql:"intervalStart"`
	IntervalEnd   time.Time `graphql:"intervalEnd"`
	Summary       string    `graphql:"summary"`
	CreatedAt     time.Time `graphql:"createdAt"`
	Feedback      *bool     `graphql:"feedback"`
	Scheduled     bool      `graphql:"scheduled"`
}

func SummaryFromDB(summary db.ClaimSummary, userId uuid.UUID) ClaimSummary {
	feedbackFromUser := slice_utils.Find(summary.Feedbacks, func(f db.Feedback) bool {
		return f.CreatedBy == userId
	})

	var feedback *bool
	if feedbackFromUser != nil {
		feedback = pointer_utils.ToPointer(feedbackFromUser.Rating == 1)
	}

	return ClaimSummary{
		Id:            summary.Id,
		ClaimId:       summary.ClaimId,
		Title:         summary.Title,
		IntervalStart: summary.Interval.Start,
		IntervalEnd:   summary.Interval.End,
		Summary:       summary.Summary,
		CreatedAt:     summary.CreatedAt,
		Feedback:      feedback,
		Scheduled:     summary.Scheduled,
	}
}

type ClaimSummaryFeedback struct {
	Id             uuid.UUID `graphql:"id,key"`
	ClaimSummaryId uuid.UUID `graphql:"claimSummaryId"`
	CreatedBy      uuid.UUID `graphql:"createdBy"`
	Rating         bool      `graphql:"rating"`
	CreatedAt      time.Time `graphql:"createdAt"`
	UpdatedAt      time.Time `graphql:"updatedAt"`
}

func ClaimSummaryFeedbackFromDB(f db.Feedback) (*ClaimSummaryFeedback, error) {
	if f.Rating != -1 && f.Rating != 1 {
		return nil, errors.Newf("Rating should be -1 or 1, found %d", f.Rating)
	}
	return &ClaimSummaryFeedback{
		Id:             f.Id,
		ClaimSummaryId: f.ClaimSummaryId,
		CreatedBy:      f.CreatedBy,
		Rating:         f.Rating == 1,
		CreatedAt:      f.CreatedAt,
		UpdatedAt:      f.UpdatedAt,
	}, nil
}
