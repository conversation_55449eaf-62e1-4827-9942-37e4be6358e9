package queryclient

import (
	"encoding"
	"encoding/json"
	"reflect"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/mitchellh/mapstructure"
)

type Output func() (interface{}, error)

// errorOutput is used for wrapping error responses.
func errorOutput(err error) Output {
	return func() (interface{}, error) {
		return nil, err
	}
}

// Result returns the results without modification, which is the raw results
// that thunder returns. This is typically a map[string]interface{} with the
// query name being the top-most map key.
func (o Output) Result() (interface{}, error) {
	return o()
}

// decode uses mapstructure to decode input to the output value.
// Note: output must be a pointer to map or struct.
// We use several features of mapstructure:
// 1. Weak Decoding: Perform case-insensitive match on JSON tag names (e.g. "id" matches "ID").
// 2. Squash: Supports the ",squash" json tag. This suppresses the embedded type name in the JSON representation.
// 3. Custom Unmarshaler: We provide a custom decode hook so that types like uuid.UUID which implement the
// encoding.TextUnmarshaler interface will properly unmarshal.
func decode(input, output interface{}) error {
	// We use our own decodeFunc to support the encoding.TextUnmarshaler interface.
	decodeFunc := mapstructure.DecodeHookFuncValue(func(from, to reflect.Value) (interface{}, error) {
		if to.CanAddr() {
			to = to.Addr()
		}

		// If the destination doesn't implement the TextUnmarshaler interface, then return early.
		u, ok := to.Interface().(encoding.TextUnmarshaler)
		if !ok {
			return from.Interface(), nil
		}

		// Special handling to avoid *uuid.UUID errors parsing from empty "from"
		// strings with "invalid UUID length: 0".
		switch to.Interface().(type) {
		case *uuid.UUID:
			if from.Type().Kind() == reflect.String && from.IsZero() {
				// TODO: This decodes to a zero value UUID rather than nil; figure
				// out how to decode to nil.
				return from.Interface(), nil
			}
		}

		// If it is nil and a pointer, create and assign the target value first
		if to.Type().Kind() == reflect.Ptr && to.IsNil() {
			to.Set(reflect.New(to.Type().Elem()))
			u = to.Interface().(encoding.TextUnmarshaler)
		}

		var text []byte
		switch v := from.Interface().(type) {
		case string:
			text = []byte(v)
		case []byte:
			text = v
		default:
			return v, nil
		}

		if err := u.UnmarshalText(text); err != nil {
			return to.Interface(), err
		}
		return to.Interface(), nil
	})
	config := &mapstructure.DecoderConfig{
		Metadata:         nil,
		Result:           output,
		WeaklyTypedInput: true,
		Squash:           true,
		TagName:          "json",
		DecodeHook:       decodeFunc,
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return errors.Wrapf(err, "failed to create new mapstructure decoder")
	}

	return decoder.Decode(input)
}

// ResultAs returns the result with a mapstructure decoding as
// the requested output, which must be a pointer to a map or struct.
func (o Output) ResultAs(output interface{}) error {
	results, err := o()
	if err != nil {
		return err
	}

	return decode(results, output)
}

// ResultString returns the result as a pretty printed JSON string.
func (o Output) ResultString() (string, error) {
	results, err := o()
	if err != nil {
		return "", err
	}
	indented, err := json.MarshalIndent(results, "", " ")
	if err != nil {
		return "", errors.Wrapf(err, "failed to marshal indent %+v", results)
	}
	return string(indented), nil
}
