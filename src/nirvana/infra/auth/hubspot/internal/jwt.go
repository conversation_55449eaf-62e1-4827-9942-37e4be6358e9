package internal

import (
	"time"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/infra/config"

	"github.com/golang-jwt/jwt/v5"
)

const (
	issuer = "nirvanatech.com"
	ttl    = 2 * time.Minute
)

var signingMethod = jwt.SigningMethodHS512

// HubspotClaims are defined at https://knowledge.hubspot.com/website-pages/set-up-single-sign-on-sso-to-access-private-content#require-sso-for-specific-knowledge-base-articles
type HubspotClaims struct {
	jwt.RegisteredClaims
	Email string `json:"email,omitempty"`
}

// JWTEncoder is a JWT encoder that uses the HS512 secret for our hubspot SSO login.
type JWTEncoder struct {
	clk      clock.Clock
	secret   []byte
	audience string
}

func NewJWTEncoder(clk clock.Clock, cfg *config.Config) *JWTEncoder {
	// Our prod secret was generated with:
	// openssl rand -base64 172 | tr -d '\n'
	secretStr := cfg.GetProductTools().GetHubspot().GetJwtSecret()
	audience := cfg.GetProductTools().GetHubspot().GetHost()

	return &JWTEncoder{
		clk:      clk,
		secret:   []byte(secretStr),
		audience: audience,
	}
}

// EncodeJWT encodes and signs a JWT containing the user's email.
func (j *JWTEncoder) EncodeJWT(lowerEmail string) (string, error) {
	now := j.clk.Now().UTC()
	claims := HubspotClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Audience:  jwt.ClaimStrings{j.audience},
			ExpiresAt: jwt.NewNumericDate(now.Add(ttl)),
			IssuedAt:  jwt.NewNumericDate(now),
			Issuer:    issuer,
			NotBefore: jwt.NewNumericDate(now.Add(-5 * time.Second)),
			Subject:   lowerEmail,
		},
		Email: lowerEmail,
	}

	// We use a symmetric HS512 signature for Hubspot.
	token := jwt.NewWithClaims(signingMethod, claims)
	signedString, err := token.SignedString(j.secret)

	return signedString, errors.Wrapf(err, "failed to sign hubspot claims %+v", claims)
}

func (j *JWTEncoder) DecodeJWT(token string) (*jwt.Token, error) {
	keyFunc := func(token *jwt.Token) (interface{}, error) {
		method, ok := token.Method.(*jwt.SigningMethodHMAC)
		if !ok {
			return nil, errors.Newf("invalid JWT signing method")
		}
		if method.Alg() != jwt.SigningMethodHS512.Alg() {
			return nil, errors.Newf("unexpected JWT signing method %v != HS512", method.Alg())
		}
		return j.secret, nil
	}
	parserOptions := []jwt.ParserOption{
		jwt.WithAudience(j.audience),
		jwt.WithIssuer(issuer),
		jwt.WithTimeFunc(j.clk.Now),
		jwt.WithIssuedAt(),
		jwt.WithExpirationRequired(),
		jwt.WithStrictDecoding(),
	}
	return jwt.ParseWithClaims(token, &HubspotClaims{}, keyFunc, parserOptions...)
}
