package internal

import (
	"fmt"
	"net/http"

	"nirvanatech.com/nirvana/infra/config"
)

type Transport struct {
	http.RoundTripper
	cfg *config.Config
}

func NewTransport(cfg *config.Config) *Transport {
	return &Transport{
		cfg:          cfg,
		RoundTripper: http.DefaultTransport,
	}
}

func (t *Transport) RoundTrip(req *http.Request) (*http.Response, error) {
	rt := t.RoundTripper
	if rt == nil {
		rt = http.DefaultTransport
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	bearerToken := t.cfg.GetProductTools().GetWorkramp().GetApiKey()
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", bearerToken))

	return rt.RoundTrip(req)
}
