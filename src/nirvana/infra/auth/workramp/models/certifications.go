package models

import (
	"github.com/google/uuid"
)

type CertificationEnrollment struct {
	Awarded                               bool           `json:"awarded"`
	AwardedAt                             *int64         `json:"awarded_at"`
	CanRecertify                          bool           `json:"can_recertify"`
	Certification                         *Certification `json:"certification,omitempty"`
	Completed                             bool           `json:"completed"`
	CompletedAt                           *int64         `json:"completed_at"`
	CreatedAt                             int64          `json:"created_at"`
	DueAt                                 *int64         `json:"due_at"`
	Expired                               bool           `json:"expired"`
	ExpiresAt                             *int64         `json:"expires_at"`
	HasLearnerSeenAward                   bool           `json:"has_learner_seen_award"`
	HasLearnerSeenAwardAndRequiresGrading bool           `json:"has_learner_seen_award_and_requires_grading"`
	ID                                    uuid.UUID      `json:"id"`
	IsCompletedAndRequiresGrading         bool           `json:"is_completed_and_requires_grading"`
	IsStarted                             bool           `json:"is_started"`
	LastAccessedAt                        *int64         `json:"last_accessed_at"`
	LinkedInAddToProfileClickedAt         *int64         `json:"linked_in_add_to_profile_clicked_at"`
	ProgressPercentage                    int            `json:"progress_percentage"`
	PublicURL                             *string        `json:"public_url"`
	ShortID                               *string        `json:"short_id"`
	TimeSpentCache                        *int           `json:"time_spent_cache"`
	UpdatedAt                             int64          `json:"updated_at"`
}

type Certification struct {
	AwardedByName  *string   `json:"awarded_by_name"`
	AwardedByTitle *string   `json:"awarded_by_title"`
	CreatedAt      int64     `json:"created_at"`
	Description    *string   `json:"description"`
	ID             uuid.UUID `json:"id"`
	Name           *string   `json:"name"`
}

// AcademyContactMinimal is used in place of AcademyContact for the Certifications endpoint,
// since the Certifications endpoint returns an incompatible JSON schema with the /users endpoint.
// For example, the certifications endpoint treats `created_at` as a **number** instead of a **string**,
// and uses the name `academy_segments` instead of just `segments`. We ignore all fields except the
// id and email, since those are really the only ones we care about anyway.
type AcademyContactMinimal struct {
	ID    string `json:"id"`
	Email string `json:"email"`
}

type GetAcademyContactCertifications struct {
	Contact                  AcademyContactMinimal     `json:"contact"`
	CertificationEnrollments []CertificationEnrollment `json:"certification_enrollments"`
}

// PaginatedGetAcademyContactCertificationsResponse is returned by https://developers.workramp.com/reference/get-all-certifications-for-a-contact
type PaginatedGetAcademyContactCertificationsResponse struct {
	PaginatedResponse[GetAcademyContactCertifications]
}
