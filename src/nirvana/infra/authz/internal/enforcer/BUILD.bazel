load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "enforcer",
    srcs = [
        "enforcer.go",
        "policies.go",
    ],
    importpath = "nirvanatech.com/nirvana/infra/authz/internal/enforcer",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/infra/authz/internal/files",
        "@com_github_casbin_casbin_v2//:casbin",
        "@com_github_casbin_casbin_v2//model",
        "@com_github_casbin_casbin_v2//persist",
        "@com_github_cockroachdb_errors//:errors",
    ],
)
