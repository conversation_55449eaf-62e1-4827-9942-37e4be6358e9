package enforcer

import (
	"bufio"
	"bytes"
	"io"
	"strings"

	"nirvanatech.com/nirvana/infra/authz/internal/files"

	"github.com/casbin/casbin/v2/model"
	"github.com/casbin/casbin/v2/persist"
	"github.com/cockroachdb/errors"
)

type policyAdapter struct {
	policyData []byte
}

var _ persist.Adapter = &policyAdapter{}

var errNotImplemented = errors.New("not implemented")

func NewEmbeddedPolicy() (*policyAdapter, error) {
	const filename = "policies.csv"
	policyFile, err := files.Policies.Open(filename)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to open %s", filename)
	}
	defer policyFile.Close()
	policyData, err := io.ReadAll(policyFile)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to read policy %s", filename)
	}
	return NewPolicy(policyData), nil
}

func NewPolicy(data []byte) *policyAdapter {
	return &policyAdapter{
		policyData: data,
	}
}

func (a *policyAdapter) LoadPolicy(model model.Model) (retErr error) {
	scanner := bufio.NewScanner(bytes.NewBuffer(a.policyData))
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		persist.LoadPolicyLine(line, model)
	}
	return errors.Wrapf(scanner.Err(), "failed to scan policy data %s", string(a.policyData))
}

func (a *policyAdapter) SavePolicy(model model.Model) error {
	return errNotImplemented
}

func (a *policyAdapter) AddPolicy(sec, ptype string, rule []string) error {
	return errNotImplemented
}

func (a *policyAdapter) RemovePolicy(sec, ptype string, rule []string) error {
	return errNotImplemented
}

func (a *policyAdapter) RemoveFilteredPolicy(sec, ptype string, fieldIndex int, fieldValues ...string) error {
	return errNotImplemented
}
