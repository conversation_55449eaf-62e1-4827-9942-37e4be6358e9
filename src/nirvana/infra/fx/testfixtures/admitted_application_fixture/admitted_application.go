package admitted_application_fixture

import (
	"context"

	"nirvanatech.com/nirvana/common-go/uuid_utils"

	nf_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"

	"go.uber.org/fx"

	admitted_application_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_app"
	nf_application "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/agency_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
)

type AdmittedApplicationsFixture struct {
	Application            *nf_application.Application[*admitted_app.AdmittedApp]
	AdditionalApplications []*nf_application.Application[*admitted_app.AdmittedApp]

	applicationWrapper nf_application.Wrapper[*admitted_app.AdmittedApp]
}

func newAdmittedApplicationsFixture(
	lc fx.Lifecycle,
	wrapper nf_application.Wrapper[*admitted_app.AdmittedApp],
	agencyFixture *agency_fixture.AgencyFixture,
	usersFixture *users_fixture.UsersFixture,
) *AdmittedApplicationsFixture {
	app := admitted_application_builder.New().
		WithID(uuid_utils.StableUUID("nf_application:Default")).
		WithDefaultMockData().
		WithAgencyID(agencyFixture.Agency.ID).
		WithProducerID(usersFixture.AgencyAdmin.ID).
		WithCreatedBy(usersFixture.AgencyAdmin.ID).
		WithUnderwriterID(usersFixture.Underwriters[0].ID).
		WithDOTNumber(536917).
		Build()

	closedApp := admitted_application_builder.New().
		WithID(uuid_utils.StableUUID("nf_application:Closed")).
		WithDefaultMockData().
		WithAgencyID(agencyFixture.Agency.ID).
		WithProducerID(usersFixture.AgencyAdmin.ID).
		WithCreatedBy(usersFixture.AgencyAdmin.ID).
		WithUnderwriterID(usersFixture.Underwriters[0].ID).
		WithDOTNumber(10000).
		WithState(nf_enums.AppStateClosed).
		Build()

	testApp := admitted_application_builder.New().
		WithID(uuid_utils.StableUUID("nf_application:Test")).
		WithDefaultMockData().
		WithAgencyID(agencyFixture.Nirvana.ID).
		WithProducerID(usersFixture.AgencyAdmin.ID).
		WithCreatedBy(usersFixture.AgencyAdmin.ID).
		WithUnderwriterID(usersFixture.Underwriters[0].ID).
		WithDOTNumber(20000).
		WithState(nf_enums.AppStateClosed).
		Build()

	fixture := &AdmittedApplicationsFixture{
		Application: app,
		AdditionalApplications: []*nf_application.Application[*admitted_app.AdmittedApp]{
			closedApp,
			testApp,
		},
		applicationWrapper: wrapper,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			return fixture.apply(ctx)
		},
	})

	return fixture
}

func (f *AdmittedApplicationsFixture) apply(ctx context.Context) error {
	if f.Application != nil {
		if err := f.applicationWrapper.InsertApp(ctx, *f.Application); err != nil {
			return err
		}
	}
	if f.AdditionalApplications != nil {
		for _, app := range f.AdditionalApplications {
			if err := f.applicationWrapper.InsertApp(ctx, *app); err != nil {
				return err
			}
		}
	}
	return nil
}

var _ = fxregistry.RegisterForTest(fx.Provide(newAdmittedApplicationsFixture))
