INSERT INTO appetite_guidelines.rule_sets (id,
                                           category,
                                           program,
                                           version,
                                           description,
                                           rules,
                                           created_at,
                                           created_by)
VALUES
    -- InternalGuideline
    ('5f3c6c2e-8e4f-4c7a-9359-1b2d9f5a0abc',
     'InternalGuideline',
     'FleetAdmitted',
     1,
     'Initial rule using GRL JSON DSL',
     '[
       {
         "name": "AutoLiability_LossesBurnRate_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.AlLossesBurnRate.Valid",
         "then": [
           "output.SetLossesBurnRateDecision(constants.Decision.Pending, constants.Variant.AutoLiability);",
           "Retract(\"AutoLiability_LossesBurnRate_Pending\");"
         ]
       },
       {
         "name": "AutoLiability_LossesBurnRate_None",
         "desc": "",
         "salience": 0,
         "when": "input.AlLossesBurnRate.Valid && input.AlLossesBurnRate.Float32 <= 20000",
         "then": [
           "output.SetLossesBurnRateDecision(constants.Decision.None, constants.Variant.AutoLiability);",
           "Retract(\"AutoLiability_LossesBurnRate_None\");"
         ]
       },
       {
         "name": "AutoLiability_LossesBurnRate_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.AlLossesBurnRate.Valid && input.AlLossesBurnRate.Float32 > 20000",
         "then": [
           "output.SetLossesBurnRateDecision(constants.Decision.Decline, constants.Variant.AutoLiability);",
           "Retract(\"AutoLiability_LossesBurnRate_Decline\");"
         ]
       },
       {
         "name": "Utilization_HalfYearly_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.HalfYearlyUtilization.Valid",
         "then": [
           "output.SetUtilizationDecision(constants.Decision.Pending, constants.Variant.HalfYearly);",
           "Retract(\"Utilization_HalfYearly_Pending\");"
         ]
       },
       {
         "name": "Utilization_HalfYearly_None",
         "desc": "",
         "salience": 0,
         "when": "input.HalfYearlyUtilization.Valid && input.HalfYearlyUtilization.Float32 < 160000",
         "then": [
           "output.SetUtilizationDecision(constants.Decision.None, constants.Variant.HalfYearly);",
           "Retract(\"Utilization_HalfYearly_None\");"
         ]
       },
       {
         "name": "Utilization_HalfYearly_FurtherReview",
         "desc": "",
         "salience": 0,
         "when": "input.HalfYearlyUtilization.Valid && input.HalfYearlyUtilization.Float32 >= 160000",
         "then": [
           "output.SetUtilizationDecision(constants.Decision.FurtherReview, constants.Variant.HalfYearly);",
           "Retract(\"Utilization_HalfYearly_FurtherReview\");"
         ]
       },
       {
         "name": "Utilization_Quarterly_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.QuarterlyUtilization.Valid",
         "then": [
           "output.SetUtilizationDecision(constants.Decision.Pending, constants.Variant.Quarterly);",
           "Retract(\"Utilization_Quarterly_Pending\");"
         ]
       },
       {
         "name": "Utilization_Quarterly_None",
         "desc": "",
         "salience": 0,
         "when": "input.QuarterlyUtilization.Valid && input.QuarterlyUtilization.Float32 < 160000",
         "then": [
           "output.SetUtilizationDecision(constants.Decision.None, constants.Variant.Quarterly);",
           "Retract(\"Utilization_Quarterly_None\");"
         ]
       },
       {
         "name": "Utilization_Quarterly_FurtherReview",
         "desc": "",
         "salience": 0,
         "when": "input.QuarterlyUtilization.Valid && input.QuarterlyUtilization.Float32 >= 160000",
         "then": [
           "output.SetUtilizationDecision(constants.Decision.FurtherReview, constants.Variant.Quarterly);",
           "Retract(\"Utilization_Quarterly_FurtherReview\");"
         ]
       },
       {
         "name": "IsTspSupported_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.IsTspSupported.Valid",
         "then": [
           "output.SetUnsupportedTSPDecision(constants.Decision.Pending);",
           "Retract(\"IsTspSupported_Pending\");"
         ]
       },
       {
         "name": "IsTspSupported_None",
         "desc": "",
         "salience": 0,
         "when": "input.IsTspSupported.Valid && input.IsTspSupported.Bool == true",
         "then": [
           "output.SetUnsupportedTSPDecision(constants.Decision.None);",
           "Retract(\"IsTspSupported_None\");"
         ]
       },
       {
         "name": "IsTspSupported_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.IsTspSupported.Valid && input.IsTspSupported.Bool == false",
         "then": [
           "output.SetUnsupportedTSPDecision(constants.Decision.Decline);",
           "Retract(\"IsTspSupported_Decline\");"
         ]
       },
       {
         "name": "DriverTurnover_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.DriverTurnover.Valid",
         "then": [
           "output.SetDriverTurnoverDecision(constants.Decision.Pending);",
           "Retract(\"DriverTurnover_Pending\");"
         ]
       },
       {
         "name": "DriverTurnover_None",
         "desc": "",
         "salience": 0,
         "when": "input.DriverTurnover.Valid && input.DriverTurnover.Float32 <= 80",
         "then": [
           "output.SetDriverTurnoverDecision(constants.Decision.None);",
           "Retract(\"DriverTurnover_None\");"
         ]
       },
       {
         "name": "DriverTurnover_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.DriverTurnover.Valid && input.DriverTurnover.Float32 > 80",
         "then": [
           "output.SetDriverTurnoverDecision(constants.Decision.Decline);",
           "Retract(\"DriverTurnover_Decline\");"
         ]
       },
       {
         "name": "FleetSize_Driver_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.DriverCount.Valid",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.Pending, constants.Variant.Driver);",
           "Retract(\"FleetSize_Driver_Pending\");"
         ]
       },
       {
         "name": "FleetSize_Driver_None",
         "desc": "",
         "salience": 0,
         "when": "input.DriverCount.Valid && input.DriverCount.Int32 >= 10",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.None, constants.Variant.Driver);",
           "Retract(\"FleetSize_Driver_None\");"
         ]
       },
       {
         "name": "FleetSize_Driver_FurtherReview_1",
         "desc": "Less than or equal to 2",
         "salience": 0,
         "when": "input.DriverCount.Valid && input.DriverCount.Int32 <= 2",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.FurtherReview, constants.Variant.Driver);",
           "Retract(\"FleetSize_Driver_FurtherReview_1\");"
         ]
       },
       {
         "name": "FleetSize_Driver_FurtherReview_2",
         "desc": "Between 6 (inclusive) and 10 (exclusive)",
         "salience": 0,
         "when": "input.DriverCount.Valid && input.DriverCount.Int32 >= 6 && input.DriverCount.Int32 < 10",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.FurtherReview, constants.Variant.Driver);",
           "Retract(\"FleetSize_Driver_FurtherReview_2\");"
         ]
       },
       {
         "name": "FleetSize_Driver_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.DriverCount.Valid && input.DriverCount.Int32 > 2 && input.DriverCount.Int32 < 6",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.Decline, constants.Variant.Driver);",
           "Retract(\"FleetSize_Driver_Decline\");"
         ]
       },
       {
         "name": "FleetSize_Vehicle_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.VehicleCount.Valid",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.Pending, constants.Variant.Vehicle);",
           "Retract(\"FleetSize_Vehicle_Pending\");"
         ]
       },
       {
         "name": "FleetSize_Vehicle_None",
         "desc": "",
         "salience": 0,
         "when": "input.VehicleCount.Valid && input.VehicleCount.Int32 >= 10",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.None, constants.Variant.Vehicle);",
           "Retract(\"FleetSize_Vehicle_None\");"
         ]
       },
       {
         "name": "FleetSize_Vehicle_FurtherReview_1",
         "desc": "Less than or equal to 2",
         "salience": 0,
         "when": "input.VehicleCount.Valid && input.VehicleCount.Int32 <= 2",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.FurtherReview, constants.Variant.Vehicle);",
           "Retract(\"FleetSize_Vehicle_FurtherReview_1\");"
         ]
       },
       {
         "name": "FleetSize_Vehicle_FurtherReview_2",
         "desc": "Between 6 (inclusive) and 10 (exclusive)",
         "salience": 0,
         "when": "input.VehicleCount.Valid && input.VehicleCount.Int32 >= 6 && input.VehicleCount.Int32 < 10",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.FurtherReview, constants.Variant.Vehicle);",
           "Retract(\"FleetSize_Vehicle_FurtherReview_2\");"
         ]
       },
       {
         "name": "FleetSize_Vehicle_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.VehicleCount.Valid && input.VehicleCount.Int32 > 2 && input.VehicleCount.Int32 < 6",
         "then": [
           "output.SetFleetSizeDecision(constants.Decision.Decline, constants.Variant.Vehicle);",
           "Retract(\"FleetSize_Vehicle_Decline\");"
         ]
       },
       {
         "name": "HazardZones_DistancePercentage_NJ_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.HazardZoneDistancePercentageNJ.Valid",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.Pending, constants.Variant.DistancePercentageNJ);",
           "Retract(\"HazardZones_DistancePercentage_NJ_Pending\");"
         ]
       },
       {
         "name": "HazardZones_DistancePercentage_NJ_None",
         "desc": "",
         "salience": 0,
         "when": "input.HazardZoneDistancePercentageNJ.Valid && input.HazardZoneDistancePercentageNJ.Float32 <= 25",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.None, constants.Variant.DistancePercentageNJ);",
           "Retract(\"HazardZones_DistancePercentage_NJ_None\");"
         ]
       },
       {
         "name": "HazardZones_DistancePercentage_NJ_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.HazardZoneDistancePercentageNJ.Valid && input.HazardZoneDistancePercentageNJ.Float32 > 25",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.Decline, constants.Variant.DistancePercentageNJ);",
           "Retract(\"HazardZones_DistancePercentage_NJ_Decline\");"
         ]
       }
     ]'::jsonb,
     now(),
     '<EMAIL>'),
    -- FrontingGuideline
    ('12345678-9abc-def0-1234-56789abcdef0',
     'FrontingGuideline',
     'FleetAdmitted',
     1,
     'Initial rule using GRL JSON DSL',
     '[
       {
         "name": "YearsInBusiness_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.TotalMonths.Valid",
         "then": [
           "output.SetYearsInBusinessDecision(constants.Decision.Pending);",
           "Retract(\"YearsInBusiness_Pending\");"
         ]
       },
       {
         "name": "YearsInBusiness_None",
         "desc": "",
         "salience": 0,
         "when": "input.TotalMonths.Valid && input.TotalMonths.Int >= 24",
         "then": [
           "output.SetYearsInBusinessDecision(constants.Decision.None);",
           "Retract(\"YearsInBusiness_None\");"
         ]
       },
       {
         "name": "YearsInBusiness_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.TotalMonths.Valid && input.TotalMonths.Int < 24",
         "then": [
           "output.SetYearsInBusinessDecision(constants.Decision.Decline);",
           "Retract(\"YearsInBusiness_Decline\");"
         ]
       },
       {
         "name": "DotRating_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.DotRating.Rating.Valid || !input.DotRating.IsDateWithinTwoYears.Valid",
         "then": [
           "output.SetDotRatingDecision(constants.Decision.Pending);",
           "Retract(\"DotRating_Pending\");"
         ]
       },
       {
         "name": "DotRating_None_1",
         "desc": "",
         "salience": 0,
         "when": "input.DotRating.Rating.Valid && input.DotRating.IsDateWithinTwoYears.Valid && input.DotRating.Rating.String != \"ConditionalRating\"",
         "then": [
           "output.SetDotRatingDecision(constants.Decision.None);",
           "Retract(\"DotRating_None_1\");"
         ]
       },
       {
         "name": "DotRating_None_2",
         "desc": "",
         "salience": 0,
         "when": "input.DotRating.Rating.Valid && input.DotRating.IsDateWithinTwoYears.Valid && input.DotRating.Rating.String == \"ConditionalRating\" && input.DotRating.IsDateWithinTwoYears.Bool == false",
         "then": [
           "output.SetDotRatingDecision(constants.Decision.None);",
           "Retract(\"DotRating_None_2\");"
         ]
       },
       {
         "name": "DotRating_RecentConditionalRating",
         "desc": "",
         "salience": 0,
         "when": "input.DotRating.Rating.Valid && input.DotRating.IsDateWithinTwoYears.Valid && input.DotRating.Rating.String == \"ConditionalRating\" && input.DotRating.IsDateWithinTwoYears.Bool == true",
         "then": [
           "output.SetDotRatingDecision(constants.Decision.Decline);",
           "Retract(\"DotRating_RecentConditionalRating\");"
         ]
       },
       {
         "name": "TelematicsRiskScore_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.TrsMarketCategory.Valid",
         "then": [
           "output.SetTelematicsRiskScoreDecision(constants.Decision.Pending);",
           "Retract(\"TelematicsRiskScore_Pending\");"
         ]
       },
       {
         "name": "TelematicsRiskScore_None",
         "desc": "",
         "salience": 0,
         "when": "input.TrsMarketCategory.Valid && input.TrsMarketCategory.String != constants.Decision.Decline",
         "then": [
           "output.SetTelematicsRiskScoreDecision(constants.Decision.None);",
           "Retract(\"TelematicsRiskScore_None\");"
         ]
       },
       {
         "name": "TelematicsRiskScore_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.TrsMarketCategory.Valid && input.TrsMarketCategory.String == constants.Decision.Decline",
         "then": [
           "output.SetTelematicsRiskScoreDecision(constants.Decision.Decline);",
           "Retract(\"TelematicsRiskScore_Decline\");"
         ]
       },
       {
         "name": "HazardZones_DistancePercentage_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.HazardZoneDistancePercentage.Valid",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.Pending, constants.Variant.DistancePercentage);",
           "Retract(\"HazardZones_DistancePercentage_Pending\");"
         ]
       },
       {
         "name": "HazardZones_DistancePercentage_None",
         "desc": "",
         "salience": 0,
         "when": "input.HazardZoneDistancePercentage.Valid && input.HazardZoneDistancePercentage.Float32 <= 25",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.None, constants.Variant.DistancePercentage);",
           "Retract(\"HazardZones_DistancePercentage_None\");"
         ]
       },
       {
         "name": "HazardZones_DistancePercentage_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.HazardZoneDistancePercentage.Valid && input.HazardZoneDistancePercentage.Float32 > 25",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.Decline, constants.Variant.DistancePercentage);",
           "Retract(\"HazardZones_DistancePercentage_Decline\");"
         ]
       },
       {
         "name": "HazardZones_DurationPercentage_Pending",
         "desc": "",
         "salience": 0,
         "when": "!input.HazardZoneDurationPercentage.Valid",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.Pending, constants.Variant.DurationPercentage);",
           "Retract(\"HazardZones_DurationPercentage_Pending\");"
         ]
       },
       {
         "name": "HazardZones_DurationPercentage_None",
         "desc": "",
         "salience": 0,
         "when": "input.HazardZoneDurationPercentage.Valid && input.HazardZoneDurationPercentage.Float32 <= 75",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.None, constants.Variant.DurationPercentage);",
           "Retract(\"HazardZones_DurationPercentage_None\");"
         ]
       },
       {
         "name": "HazardZones_DurationPercentage_Decline",
         "desc": "",
         "salience": 0,
         "when": "input.HazardZoneDurationPercentage.Valid && input.HazardZoneDurationPercentage.Float32 > 75",
         "then": [
           "output.SetHazardZonesDecision(constants.Decision.Decline, constants.Variant.DurationPercentage);",
           "Retract(\"HazardZones_DurationPercentage_Decline\");"
         ]
       }
     ]'::jsonb,
     now(),
     '<EMAIL>');