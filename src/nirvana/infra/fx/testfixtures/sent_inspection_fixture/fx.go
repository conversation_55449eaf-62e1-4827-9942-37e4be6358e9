package sent_inspection_fixture

import (
	"context"

	"github.com/benbjo<PERSON>son/clock"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/safety"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/safety_report_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
)

type SentInspectionFixture struct {
	SentInspections []safety.SentInspection
}

func newSentInspectionFixture(
	lc fx.Lifecycle,
	userFixture *users_fixture.UsersFixture,
	clock *clock.Mock,
	safetyReportFixture *safety_report_fixture.SafetyReportFixture,
	safetyDBWrapper safety.DataWrapper,
) *SentInspectionFixture {
	fixture := &SentInspectionFixture{
		SentInspections: []safety.SentInspection{
			{
				ID:             uuid.New(),
				SafetyReportID: safetyReportFixture.Reports[0].ID,
				InspectionID:   1234,
				SentAt:         clock.Now(),
				CreatedAt:      clock.Now(),
			},
			{
				ID:             uuid.New(),
				SafetyReportID: safetyReportFixture.Reports[0].ID,
				InspectionID:   2345,
				SentAt:         clock.Now(),
				CreatedAt:      clock.Now(),
			},
		},
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			return fixture.apply(ctx, safetyDBWrapper)
		},
	})
	return fixture
}

func (fixture *SentInspectionFixture) apply(ctx context.Context, safetyDBWrapper safety.DataWrapper) error {
	return safetyDBWrapper.CreateSentInspections(ctx, fixture.SentInspections)
}

var _ = fxregistry.RegisterForTest(fx.Provide(newSentInspectionFixture))
