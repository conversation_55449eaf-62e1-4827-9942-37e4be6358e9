package submission_fixture

import (
	"context"

	"go.uber.org/fx"

	submission_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/submission"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_fixture"
)

type SubmissionsFixture struct {
	IndicationSubmission            *application.SubmissionObject
	AdditionalIndicationSubmissions []*application.SubmissionObject

	UWSubmission            *application.SubmissionObject
	AdditionalUWSubmissions []*application.SubmissionObject

	BindableSubmission            *application.SubmissionObject
	AdditionalBindableSubmissions []*application.SubmissionObject

	wrapper application.DataWrapper
}

func newSubmissionsFixture(
	lc fx.Lifecycle,
	wrapper application.DataWrapper,
	applicationFixture *application_fixture.ApplicationsFixture,
) *SubmissionsFixture {
	indicationSubmission := submission_builder.New().
		WithID(uuid_utils.StableUUID("submission:Indication")).
		FromApplication(*applicationFixture.Application).
		Build()

	uwSubmission := submission_builder.New().
		WithID(uuid_utils.StableUUID("submission:UW")).
		FromApplication(*applicationFixture.Application).
		Build()

	bindableSubmission := submission_builder.New().
		WithID(uuid_utils.StableUUID("submission:Bindable")).
		FromApplication(*applicationFixture.Application).
		WithBindable(true).
		Build()

	fixture := &SubmissionsFixture{
		IndicationSubmission: indicationSubmission,
		UWSubmission:         uwSubmission,
		BindableSubmission:   bindableSubmission,

		wrapper: wrapper,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			return fixture.apply(ctx, applicationFixture)
		},
	})

	return fixture
}

func (f *SubmissionsFixture) apply(ctx context.Context, applicationFixture *application_fixture.ApplicationsFixture) error {
	if f.IndicationSubmission != nil {
		if err := f.wrapper.InsertSubmission(ctx, *f.IndicationSubmission); err != nil {
			return err
		}
		if err := f.wrapper.UpdateApp(
			ctx,
			applicationFixture.Application.ID,
			func(a application.Application) (application.Application, error) {
				a.IndicationSubmissionID = &f.IndicationSubmission.ID
				return a, nil
			},
		); err != nil {
			return err
		}

	}
	if f.AdditionalIndicationSubmissions != nil {
		for _, submission := range f.AdditionalIndicationSubmissions {
			if err := f.wrapper.InsertSubmission(ctx, *submission); err != nil {
				return err
			}
		}
	}

	if f.UWSubmission != nil {
		if err := f.wrapper.InsertSubmission(ctx, *f.UWSubmission); err != nil {
			return err
		}
		if err := f.wrapper.UpdateApp(
			ctx,
			applicationFixture.Application.ID,
			func(a application.Application) (application.Application, error) {
				a.UwSubmissionID = &f.UWSubmission.ID
				return a, nil
			},
		); err != nil {
			return err
		}
	}
	if f.AdditionalUWSubmissions != nil {
		for _, submission := range f.AdditionalUWSubmissions {
			if err := f.wrapper.InsertSubmission(ctx, *submission); err != nil {
				return err
			}
		}
	}

	if f.BindableSubmission != nil {
		if err := f.wrapper.InsertSubmission(ctx, *f.BindableSubmission); err != nil {
			return err
		}
		if err := f.wrapper.UpdateApp(
			ctx,
			applicationFixture.Application.ID,
			func(a application.Application) (application.Application, error) {
				a.BindableSubmissionID = &f.BindableSubmission.ID
				return a, nil
			},
		); err != nil {
			return err
		}
	}
	if f.AdditionalBindableSubmissions != nil {
		for _, submission := range f.AdditionalBindableSubmissions {
			if err := f.wrapper.InsertSubmission(ctx, *submission); err != nil {
				return err
			}
		}
	}

	return nil
}

var _ = fxregistry.RegisterForTest(fx.Provide(newSubmissionsFixture))
