package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestCalculateBaseChargeDistributionAtAppSubCovLevel(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	tests := []struct {
		name                     string
		charges                  []*ptypes.Charge
		exposure                 *ptypes.Exposure
		expectedTotal            *float64
		expectedChargesBySubCov  map[appEnums.Coverage]*float64
		expectedChargesByVehicle map[string]map[appEnums.Coverage]*float64
		expectError              bool
		errorMsg                 string
	}{
		{
			name:                     "empty charges slice",
			charges:                  []*ptypes.Charge{},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "single charge without distributions",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.50", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(100.50),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability: floatPtr(100.50),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "single charge with vehicle distribution",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("200.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.6"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.4"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(200.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability: floatPtr(200.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageAutoLiability: floatPtr(120.00),
				},
				"vehicle2": {
					appEnums.CoverageAutoLiability: floatPtr(80.00),
				},
			},
			expectError: false,
		},
		{
			name: "multiple charges different coverages",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("150.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("300.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("250.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Cargo).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(700.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability:    floatPtr(150.00),
				appEnums.CoverageGeneralLiability: floatPtr(300.00),
				appEnums.CoverageMotorTruckCargo:  floatPtr(250.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "charge with multiple sub-coverages mapping to primary coverage",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("400.00", effectiveDate).
					WithChargeableSubCoverageGroup(
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(400.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability: floatPtr(400.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "charge without sub-coverage group - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargedItem(ptypes.NewChargeChargedPolicy("POLICY123")).
					Build(),
			},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "complex scenario with multiple vehicles and coverages",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("600.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.5"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.3"),
							ptypes.NewChargeDistributionItem("vehicle3", "0.2"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("400.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.7"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.3"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(1000.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability:    floatPtr(600.00),
				appEnums.CoverageGeneralLiability: floatPtr(400.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageAutoLiability:    floatPtr(300.00),
					appEnums.CoverageGeneralLiability: floatPtr(280.00),
				},
				"vehicle2": {
					appEnums.CoverageAutoLiability:    floatPtr(180.00),
					appEnums.CoverageGeneralLiability: floatPtr(120.00),
				},
				"vehicle3": {
					appEnums.CoverageAutoLiability: floatPtr(120.00),
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
				tt.charges,
				tt.exposure,
			)

			if tt.expectError {
				require.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectedTotal, totalCharges)
			assert.Equal(t, tt.expectedChargesBySubCov, chargesBySubCov)
			assert.Equal(t, tt.expectedChargesByVehicle, chargesByVehicle)
		})
	}
}

// floatPtr is a helper function to create float64 pointers for test assertions
func floatPtr(f float64) *float64 {
	return &f
}
