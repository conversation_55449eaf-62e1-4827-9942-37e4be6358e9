package endorsement

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
)

func TestInsuredChange_Apply_AddNonPrimaryInsured(t *testing.T) {
	// Setup

	policy := &model.Policy{
		PolicyNumber:       "AL123",
		NonPrimaryInsureds: []*insurancecoreproto.Insured{},
	}

	ibSegmentToUpdate := &model.InsuranceBundleSegment{
		Policies: map[string]*model.Policy{
			"AL123": policy,
		},
	}

	insuredChange := &CoreChange_InsuredChange{
		InsuredChange: &InsuredChange{
			Add: []*insurancecoreproto.Insured{
				{
					Id:   "123",
					Type: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
					Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
					Address: &proto.Address{
						Nation:     pointer_utils.String("US"),
						State:      pointer_utils.String("CA"),
						City:       pointer_utils.String("San Francisco"),
						CountyCode: pointer_utils.String("123"),
						Street:     pointer_utils.String("123 Main St"),
						ZipCode:    pointer_utils.String("12345"),
					},
				},
			},
		},
	}

	// Execute
	insuredChange.Apply([]string{"AL123"}, ibSegmentToUpdate)

	// Verify
	assert.Equal(t, 1, len(ibSegmentToUpdate.Policies["AL123"].NonPrimaryInsureds))
	assert.Equal(t, "123", ibSegmentToUpdate.Policies["AL123"].NonPrimaryInsureds[0].Id)
	assert.Equal(
		t,
		insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
		ibSegmentToUpdate.Policies["AL123"].NonPrimaryInsureds[0].Type,
	)
}

func TestInsuredChange_Apply_RemoveNonPrimaryInsured(t *testing.T) {
	// Setup
	policy := &model.Policy{
		NonPrimaryInsureds: []*insurancecoreproto.Insured{
			{
				Id:   "123",
				Type: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
				Address: &proto.Address{
					Nation:     pointer_utils.String("US"),
					State:      pointer_utils.String("CA"),
					City:       pointer_utils.String("San Francisco"),
					CountyCode: pointer_utils.String("123"),
					Street:     pointer_utils.String("123 Main St"),
					ZipCode:    pointer_utils.String("12345"),
				},
			},
		},
	}

	ibSegmentToUpdate := &model.InsuranceBundleSegment{
		Policies: map[string]*model.Policy{
			"AL123": policy,
		},
	}

	insuredChange := &CoreChange_InsuredChange{
		InsuredChange: &InsuredChange{
			Remove:      []string{"Insured 1"},
			InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
		},
	}

	// Execute
	insuredChange.Apply([]string{"AL123"}, ibSegmentToUpdate)

	// Verify
	assert.Equal(t, 0, len(policy.NonPrimaryInsureds))
}

func TestInsuredChange_Validate(t *testing.T) {
	tests := []struct {
		name              string
		insuredChange     *CoreChange_InsuredChange
		ibSegmentToUpdate *model.InsuranceBundleSegment
		expectedError     string
	}{
		{
			name:          "Nil InsuredChange | inner struct",
			insuredChange: nil,
			expectedError: "InsuredChange cannot be nil",
		},
		{
			name: "Nil InsuredChange | outer struct",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: nil,
			},
			expectedError: "InsuredChange cannot be nil",
		},
		{
			name: "Invalid InsuredType",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_Invalid,
				},
			},
			expectedError: "invalid insured type",
		},
		{
			name: "Empty InsuredChange",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				},
			},
			expectedError: "failed to self validate insured change: ChangeSet cannot be empty",
		},
		{
			name: "Add and Remove same Insured",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Add: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: proto.GetMockAddress(),
						},
					},
					Remove:      []string{"Insured 1"},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				},
			},
			expectedError: "failed to self validate insured change: Item cannot be added and removed simultaneously: Insured 1",
		},
		{
			name: "Add and Update same Insured",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Add: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: proto.GetMockAddress(),
						},
					},
					Update: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: proto.GetMockAddress(),
						},
					},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				},
			},
			expectedError: "failed to self validate insured change: Item cannot be added and updated simultaneously: Insured 1",
		},
		{
			name: "Remove and Update same Insured",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Remove: []string{"Insured 1"},
					Update: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalNamedInsured,
							Address: proto.GetMockAddress(),
						},
					},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalNamedInsured,
				},
			},
			expectedError: "failed to self validate insured change: Item cannot be removed and updated simultaneously",
		},
		{
			name: "Update Insured not in policy",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Update: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalNamedInsured,
							Address: proto.GetMockAddress(),
						},
					},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalNamedInsured,
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{},
					},
				},
			},
			expectedError: "Insured change is not valid for policy number AL123: Item to update does not exist: Insured 1",
		},
		{
			name: "Add Insured already in policy | Same Name + Same Type",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Add: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: proto.GetMockAddress(),
						},
					},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{
								Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
								Type: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							},
						},
					},
				},
			},
			expectedError: "Insured change is not valid for policy number AL123: Item to add already exists: Insured 1",
		},
		{
			name: "Add Insured already in policy | Same Name + Different Type",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Add: []*insurancecoreproto.Insured{
						{
							Name:    &insurancecoreproto.InsuredName{BusinessName: "Insured 1"},
							Type:    insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: proto.GetMockAddress(),
						},
					},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"}},
						},
					},
				},
			},
		},
		{
			name: "Remove Insured not in policy | Name doesn't match",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Remove:      []string{"Insured 1"},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalNamedInsured,
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{
								Name: &insurancecoreproto.InsuredName{
									BusinessName: "Insured 2",
								},
								Type: insurancecoreproto.InsuredType_InsuredType_AdditionalNamedInsured,
							},
						},
					},
				},
			},
			expectedError: "Insured change is not valid for policy number AL123: Item to remove does not exist: Insured 1",
		},
		{
			name: "Remove Insured not in policy | Type doesn't match",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					Remove:      []string{"Insured 1"},
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{
								Name: &insurancecoreproto.InsuredName{
									BusinessName: "Insured 1",
								},
								Type: insurancecoreproto.InsuredType_InsuredType_DesignatedInsured,
							},
						},
					},
				},
			},
			expectedError: "Insured change is not valid for policy number AL123: Item to remove does not exist: Insured 1",
		},
		{
			name: "Valid InsuredChange",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
					Add: []*insurancecoreproto.Insured{
						{
							Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 2"},
							Type: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: &proto.Address{
								Nation:     pointer_utils.String("US"),
								State:      pointer_utils.String("CA"),
								City:       pointer_utils.String("San Francisco"),
								CountyCode: pointer_utils.String("123"),
								Street:     pointer_utils.String("123 Main St"),
								ZipCode:    pointer_utils.String("12345"),
							},
						},
					},
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"}},
						},
					},
				},
			},
			expectedError: "",
		},
		{
			name: "Invalid InsuredType on Change: PrimaryInsured",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_PrimaryInsured,
					Add: []*insurancecoreproto.Insured{
						{
							Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 2"},
							Type: insurancecoreproto.InsuredType_InsuredType_PrimaryInsured,
							Address: &proto.Address{
								Nation:     pointer_utils.String("US"),
								State:      pointer_utils.String("CA"),
								City:       pointer_utils.String("San Francisco"),
								CountyCode: pointer_utils.String("123"),
								Street:     pointer_utils.String("123 Main St"),
								ZipCode:    pointer_utils.String("12345"),
							},
						},
					},
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"}},
						},
					},
				},
			},
			expectedError: "insured type InsuredType_PrimaryInsured is not allowed to change",
		},
		{
			name: "Invalid InsuredType on Change: LossPayee",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_LossPayee,
					Add: []*insurancecoreproto.Insured{
						{
							Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 2"},
							Type: insurancecoreproto.InsuredType_InsuredType_LossPayee,
							Address: &proto.Address{
								Nation:     pointer_utils.String("US"),
								State:      pointer_utils.String("CA"),
								City:       pointer_utils.String("San Francisco"),
								CountyCode: pointer_utils.String("123"),
								Street:     pointer_utils.String("123 Main St"),
								ZipCode:    pointer_utils.String("12345"),
							},
						},
					},
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"}},
						},
					},
				},
			},
			expectedError: "insured type InsuredType_LossPayee is not allowed to change",
		},
		{
			name: "Valid InsuredType | Insured type to Add does not match InsuredType on Change",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
					Add: []*insurancecoreproto.Insured{
						{
							Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 2"},
							Type: insurancecoreproto.InsuredType_InsuredType_SpecifiedAdditionalInsured,
							Address: &proto.Address{
								Nation:     pointer_utils.String("US"),
								State:      pointer_utils.String("CA"),
								City:       pointer_utils.String("San Francisco"),
								CountyCode: pointer_utils.String("123"),
								Street:     pointer_utils.String("123 Main St"),
								ZipCode:    pointer_utils.String("12345"),
							},
						},
					},
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"}},
						},
					},
				},
			},
			expectedError: "",
		},
		{
			name: "Valid InsuredType | Multiple Insured types to Add",
			insuredChange: &CoreChange_InsuredChange{
				InsuredChange: &InsuredChange{
					InsuredType: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
					Add: []*insurancecoreproto.Insured{
						{
							Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 2"},
							Type: insurancecoreproto.InsuredType_InsuredType_SpecifiedAdditionalInsured,
							Address: &proto.Address{
								Nation:     pointer_utils.String("US"),
								State:      pointer_utils.String("CA"),
								City:       pointer_utils.String("San Francisco"),
								CountyCode: pointer_utils.String("123"),
								Street:     pointer_utils.String("123 Main St"),
								ZipCode:    pointer_utils.String("12345"),
							},
						},
						{
							Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 2"},
							Type: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
							Address: &proto.Address{
								Nation:     pointer_utils.String("US"),
								State:      pointer_utils.String("CA"),
								City:       pointer_utils.String("San Francisco"),
								CountyCode: pointer_utils.String("123"),
								Street:     pointer_utils.String("123 Main St"),
								ZipCode:    pointer_utils.String("12345"),
							},
						},
					},
				},
			},
			ibSegmentToUpdate: &model.InsuranceBundleSegment{
				Policies: map[string]*model.Policy{
					"AL123": {
						NonPrimaryInsureds: []*insurancecoreproto.Insured{
							{Name: &insurancecoreproto.InsuredName{BusinessName: "Insured 1"}},
						},
					},
				},
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.insuredChange.Validate([]string{"AL123"}, tt.ibSegmentToUpdate)
			if tt.expectedError == "" {
				require.NoError(t, err)
			} else {
				require.Error(t, err)
				assert.ErrorContains(t, err, tt.expectedError)
			}
		})
	}
}
