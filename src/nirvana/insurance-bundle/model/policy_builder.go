package model

import (
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	fleetmodel "nirvanatech.com/nirvana/fleet/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/charges"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	nfmodel "nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type PolicyBuilder struct {
	policy *Policy
}

// NewPolicyBuilder gives you a new PolicyBuilder with default values. You can override these values by using the
// With* methods.
func NewPolicyBuilder(programType insurancecoreproto.ProgramType) *PolicyBuilder {
	var programData *ProgramData
	switch programType { //exhaustive:enforce
	case insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted:
		programData = &ProgramData{
			Data: &ProgramData_NonFleetData{NonFleetData: nfmodel.NewNFAdmittedProgramDataV1Builder().Build()},
		}
	case insurancecoreproto.ProgramType_ProgramType_Fleet:
		programData = &ProgramData{
			Data: &ProgramData_FleetData{FleetData: fleetmodel.NewFleetProgramDataBuilder().Build()},
		}
	case insurancecoreproto.ProgramType_ProgramType_CanopiusNRB,
		insurancecoreproto.ProgramType_ProgramType_BusinessAuto,
		insurancecoreproto.ProgramType_ProgramType_Invalid:
		// Do nothing
	}

	charge1 := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("5", timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime())).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
		Build()
	charge2 := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("20", timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime())).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
		Build()

	chargeAdjustment := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("1", timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime())).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
		Build()

	nonPrimaryInsuredID := uuid.NewString()
	return &PolicyBuilder{policy: &Policy{
		Id:           uuid.NewString(),
		PolicyNumber: "NNFTK0012345-24",
		State:        PolicyState_PolicyState_Active,
		Coverages: []*Coverage{
			{
				Id:          "CoverageAutoLiability",
				DisplayName: "Auto Liability",
				SubCoverages: []*SubCoverage{
					{
						Id:          "CoverageBodilyInjury",
						DisplayName: "Bodily Injury",
					},
					{
						Id:          "CoveragePropertyDamage",
						DisplayName: "Property Damage",
					},
					{
						Id:          "CoverageUIM",
						DisplayName: "UIM",
					},
					{
						Id:          "CoverageUM",
						DisplayName: "UM",
					},
				},
			},
			{
				Id:          "CoverageAutoPhysicalDamage",
				DisplayName: "Auto Physical Damage",
				SubCoverages: []*SubCoverage{
					{
						Id:          "CoverageCollision",
						DisplayName: "Collision",
					},
					{
						Id:          "CoverageComprehensive",
						DisplayName: "Comprehensive",
					},
					{
						Id:          "CoverageTowingLaborAndStorage",
						DisplayName: "Towing Labor And Storage",
					},
					{
						Id:          "CoverageRentalReimbursement",
						DisplayName: "Rental Reimbursement",
					},
				},
			},
		},
		EffectiveInterval: &proto.Interval{
			Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
			End:   timestamppb.New(time_utils.NewDate(2025, 5, 1).ToTime()),
		},
		Metadata: &PolicyMetadata{
			BindableSubmissionId: uuid.NewString(),
			ApplicationId:        uuid.NewString(),
		},
		NonPrimaryInsureds: []*insurancecoreproto.Insured{
			{
				Id:   nonPrimaryInsuredID,
				Type: insurancecoreproto.InsuredType_InsuredType_AdditionalInsured,
				Name: &insurancecoreproto.InsuredName{
					BusinessName: "Nirvana India Trucking Solutions",
				},
				Address: &proto.Address{
					Nation:  pointer_utils.ToPointer("US"),
					State:   pointer_utils.ToPointer("CA"),
					City:    pointer_utils.ToPointer("San Francisco"),
					Street:  pointer_utils.ToPointer("1234 Main St"),
					ZipCode: pointer_utils.ToPointer("94111"),
				},
			},
		},
		ProgramData: programData,
		WrittenExposure: &Exposure{
			Exposures: &Exposure_NonFleetExposure{
				NonFleetExposure: &nfmodel.Exposure{},
			},
		},
		FormInfo: &insurancecoreproto.FormInfo{
			CoreForm: &insurancecoreproto.FormCore{
				FormCompilationId:   uuid.NewString(),
				FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
				DocumentHandleId:    uuid.NewString(),
				FormCodes:           []string{"al-form-1", "al-form-2"},
			},
			AdditionalForms: []*insurancecoreproto.FormCore{
				{
					FormCompilationId:   uuid.NewString(),
					FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeEndorsement,
					FormCodes:           []string{"endorsement-change-doc-1"},
				},
			},
		},
		Charges: &charges.ChargeList{
			Charges: []*ptypes.Charge{charge1, charge2},
		},
		Clauses: &insurancecoreproto.ClauseList{
			Clauses: []*insurancecoreproto.Clause{
				{
					Id: &insurancecoreproto.ClauseId{
						Id: insurancecoreproto.ClauseType_ClauseTypeWaiverOfSubrogation.String(),
					},
					Type: insurancecoreproto.ClauseType_ClauseTypeWaiverOfSubrogation,
					ParticipantScope: &insurancecoreproto.ParticipantScope{
						Type: insurancecoreproto.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket,
					},
				},
				{
					Id: &insurancecoreproto.ClauseId{
						Id: insurancecoreproto.ClauseType_ClauseTypeSpecifiedAdditionalInsured.String(),
					},
					Type: insurancecoreproto.ClauseType_ClauseTypeSpecifiedAdditionalInsured,
					ParticipantScope: &insurancecoreproto.ParticipantScope{
						Type: insurancecoreproto.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeSpecific,
						ApplicableParticipants: []*insurancecoreproto.Participant{{
							Type: insurancecoreproto.ParticipantType_ParticipantTypeInsured,
							Id:   nonPrimaryInsuredID,
						}},
					},
				},
			},
		},
		ChargeAdjustments: &ChargeAdjustments{
			Adjustments: []*ChargeAdjustment{
				{
					Charges: &charges.ChargeList{
						Charges: []*ptypes.Charge{
							chargeAdjustment,
						},
					},
					Reason: "Legacy System Mismatch",
				},
			},
		},
	}}
}

func (p *PolicyBuilder) Build() *Policy {
	return p.policy
}

func (p *PolicyBuilder) WithPolicyNumber(policyNumber string) *PolicyBuilder {
	p.policy.PolicyNumber = policyNumber
	return p
}

func (p *PolicyBuilder) WithApplicationId(appId uuid.UUID) *PolicyBuilder {
	p.policy.Metadata.ApplicationId = appId.String()
	return p
}

func (p *PolicyBuilder) WithBindableSubmissionId(subId uuid.UUID) *PolicyBuilder {
	p.policy.Metadata.BindableSubmissionId = subId.String()
	return p
}

func (p *PolicyBuilder) WithCancellationInfo(cancellationInfo *CancellationInfo) *PolicyBuilder {
	p.policy.Metadata.CancellationInfo = cancellationInfo
	return p
}

func (p *PolicyBuilder) WithCoverages(coverages []*Coverage) *PolicyBuilder {
	p.policy.Coverages = coverages
	return p
}

func (p *PolicyBuilder) WithNFProgramData(programData *nfmodel.NFAdmittedProgramDataV1) *PolicyBuilder {
	p.policy.ProgramData = &ProgramData{
		Data: &ProgramData_NonFleetData{NonFleetData: programData},
	}
	return p
}

func (p *PolicyBuilder) WithClauses(clauseList *insurancecoreproto.ClauseList) *PolicyBuilder {
	p.policy.Clauses = clauseList
	return p
}

func (p *PolicyBuilder) WithCharges(chargeList []*ptypes.Charge) *PolicyBuilder {
	p.policy.Charges = &charges.ChargeList{Charges: chargeList}
	return p
}

func (p *PolicyBuilder) WithEffectiveInterval(interval *proto.Interval) *PolicyBuilder {
	p.policy.EffectiveInterval = interval
	return p
}

func (p *PolicyBuilder) WithId(id string) *PolicyBuilder {
	p.policy.Id = id
	return p
}

func (p *PolicyBuilder) WithCoreForm(coreForm *insurancecoreproto.FormCore) *PolicyBuilder {
	p.policy.FormInfo.CoreForm = coreForm
	return p
}

func (p *PolicyBuilder) WithAdditionalForms(additionalForms []*insurancecoreproto.FormCore) *PolicyBuilder {
	p.policy.FormInfo.AdditionalForms = additionalForms
	return p
}

// WithState sets the policy state.
func (p *PolicyBuilder) WithState(state PolicyState) *PolicyBuilder {
	p.policy.State = state
	return p
}

// UnsetNonPrimaryInsureds clears any non-primary insureds on the policy.
func (p *PolicyBuilder) UnsetNonPrimaryInsureds() *PolicyBuilder {
	p.policy.NonPrimaryInsureds = nil
	return p
}

// UnsetChargeAdjustments removes any charge adjustments that may have been set by default.
func (p *PolicyBuilder) UnsetChargeAdjustments() *PolicyBuilder {
	p.policy.ChargeAdjustments = nil
	return p
}

func (p *PolicyBuilder) WithWrittenExposure(exposure *Exposure) *PolicyBuilder {
	p.policy.WrittenExposure = exposure
	return p
}
