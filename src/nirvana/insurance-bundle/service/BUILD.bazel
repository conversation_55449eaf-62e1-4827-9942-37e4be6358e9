load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "service_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/insurance-bundle/service",
    proto = "//proto/insurance_bundle/service:service_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
    ],
)

go_library(
    name = "service",
    srcs = [
        "change_utils.go",
        "deps.go",
        "fx.go",
        "impl.go",
        "mock_insurance_bundle_service_client.go",
        "validations.go",
    ],
    embed = [":service_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/insurance-bundle/service",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/grpc/grpc-errors",
        "//nirvana/common-go/grpc/libgrpc",
        "//nirvana/common-go/grpc/middleware",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/db-api/db_models/insurance_bundle",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/insurance-bundle/endorsement/change",
        "//nirvana/db-api/db_wrappers/insurance-bundle/endorsement/change_container",
        "//nirvana/db-api/db_wrappers/insurance-bundle/endorsement/request",
        "//nirvana/db-api/db_wrappers/insurance-bundle/ib-segment",
        "//nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle",
        "//nirvana/db-api/db_wrappers/insurance-bundle/policyv2",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/config",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-bundle/model/helper",
        "//nirvana/insurance-bundle/segmenter",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
        "//nirvana/insured/service",
        "//nirvana/policy",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

# keep
go_proto_link(
    name = "service_go_proto_link",
    dep = ":service_go_proto",
    version = "v1",
)

go_test(
    name = "service_test",
    srcs = [
        "endorsement_test.go",
        "fx_test.go",
        "impl_test.go",
        "validations_test.go",
    ],
    embed = [":service"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/insurance-bundle/endorsement/change/mock",
        "//nirvana/db-api/db_wrappers/insurance-bundle/endorsement/change_container/mock",
        "//nirvana/db-api/db_wrappers/insurance-bundle/endorsement/request/mock",
        "//nirvana/db-api/db_wrappers/insurance-bundle/ib-segment",
        "//nirvana/db-api/db_wrappers/insurance-bundle/ib-segment/mocks",
        "//nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle",
        "//nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle/mocks",
        "//nirvana/db-api/db_wrappers/insurance-bundle/policyv2/mocks",
        "//nirvana/infra/fx/testloader",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-core/proto",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
