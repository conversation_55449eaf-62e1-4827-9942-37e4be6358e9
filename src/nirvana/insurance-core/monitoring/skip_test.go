package monitoring

import (
	"errors"
	"testing"
)

func TestShouldSkipTriggeringAlert(t *testing.T) {
	tests := []struct {
		name      string
		req       *TriggerPagerDutyAlertRequest
		isTestApp bool
		want      bool
	}{
		{
			name: "should not skip for prod app with GeoZipEnum issue",
			req: &TriggerPagerDutyAlertRequest{
				Err: errors.New("Value 12345 is not a valid enum &{{GeoZipEnum 0}"),
			},
			isTestApp: false,
			want:      false,
		},
		{
			name: "should skip for test app with GeoZipEnum issue",
			req: &TriggerPagerDutyAlertRequest{
				Err: errors.New("Value 12345 is not a valid enum &{{GeoZipEnum 0}"),
			},
			isTestApp: true,
			want:      true,
		},
		{
			name: "should not skip for test app with different error",
			req: &TriggerPagerDutyAlertRequest{
				Err: errors.New("some other error"),
			},
			isTestApp: true,
			want:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := shouldSkipTriggeringAlert(tt.req, tt.isTestApp); got != tt.want {
				t.Errorf("shouldSkipTriggeringAlert() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsGeoZipEnumIssueForTestApp(t *testing.T) {
	tests := []struct {
		name    string
		errMsg  string
		want    bool
		wantErr bool
	}{
		{
			name:   "matches simple zip code error",
			errMsg: "Value 12345 is not a valid enum &{{GeoZipEnum 0}",
			want:   true,
		},
		{
			name:   "matches zip code with extension",
			errMsg: "Value 12345-6789 is not a valid enum &{{GeoZipEnum 0}",
			want:   true,
		},
		{
			name:   "matches when there is text after the substring",
			errMsg: "Value 12345 is not a valid enum &{{GeoZipEnum 0} - Additional error information follows",
			want:   true,
		},
		{
			name:   "does not match different error message",
			errMsg: "some other error",
			want:   false,
		},
		{
			name:   "does not match similar but incorrect format",
			errMsg: "Value 12345 is not a valid enum for GeoZipEnum",
			want:   false,
		},
		{
			name:   "does not match invalid zip code format",
			errMsg: "Value 1234 is not a valid enum &{{GeoZipEnum 0}",
			want:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &TriggerPagerDutyAlertRequest{
				Err: errors.New(tt.errMsg),
			}
			got := isGeoZipEnumIssueForTestApp(req)
			if got != tt.want {
				t.Errorf("isGeoZipEnumIssueForTestApp() = %v, want %v", got, tt.want)
			}
		})
	}
}
