package main

import (
	"flag"
	"fmt"
	"log"

	"go.uber.org/fx"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/common-go/grpc/middleware"
	"nirvanatech.com/nirvana/common-go/metrics"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/appfx"
	"nirvanatech.com/nirvana/jobber/internal/proto"
	"nirvanatech.com/nirvana/jobber/monitor"
)

const servicePort = 56666 // JOMON

func main() {
	var host string
	var port int

	flag.StringVar(
		&host, "host", "",
		"Host on which to start listening. Defaults to listening on all interfaces",
	)
	flag.IntVar(
		&port, "port", servicePort, "Port on which to start grpc server",
	)

	addr := fmt.Sprintf("%s:%d", host, port)
	NewApp(config.CurrentEnv(), addr).Build().Run()
}

func NewApp(env config.Env, addr string) appfx.Builder {
	serverOptions, err := grpcServerOptions()
	if err != nil {
		log.Fatalf("error creating metrics client: %v", err)
	}

	return appfx.NewGRPCAppBuilder(
		constants.JobberMonitor,
		env,
		addr,
		serverOptions,
		fxOptions()...,
	)
}

func grpcServerOptions() ([]grpc.ServerOption, error) {
	metricsClient, err := metrics.NewClient(string(constants.JobberMonitor))
	if err != nil {
		return nil, err
	}

	return []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(
			append(
				middleware.DefaultServerUnaryInterceptors(metricsClient),
				middleware.GetUnaryLoggerMiddleware(),
			)...,
		),
		grpc.ChainStreamInterceptor(middleware.DefaultServerStreamInterceptors(metricsClient)...),
	}, nil
}

func fxOptions(extra ...fx.Option) []fx.Option {
	return append(extra,
		fx.Provide(fx.Annotate(monitor.NewDbMonitorServer, fx.As(new(proto.MonitorServer)))),
		fx.Invoke(proto.RegisterMonitorServer),
	)
}
