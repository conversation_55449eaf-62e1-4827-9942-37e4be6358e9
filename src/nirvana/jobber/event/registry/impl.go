package registry

import (
	"github.com/cockroachdb/errors"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/jobber/event"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

const indexedSubsGroupFxAnnotation = `group:"indexedSubs"`

// EventRegistration stores the parameteres needed to register an event
// to a registry.
// U is the type of event payload.
//
// See test_event.go for an example
type EventRegistration[U jtypes.Message] struct {
	Kind               event.Kind
	PayloadUnmarshalFn jtypes.UnmarshalMessageFn[U]
}

// RegisterEvent must be called for an event Registration before subscriptions
// can be added to it.
// Generally, this function is called in init() function in the file
// containing EventRegistration and various AddSubscription* calls
// follow it.
func RegisterEvent[U jtypes.Message](e *EventRegistration[U]) {
	if e.Kind == "" || e.PayloadUnmarshalFn == nil {
		panic(errors.Newf("required fields in event(%s) are not set", e.Kind))
	}
	if _, ok := events[e.Kind]; ok {
		panic(errors.Newf("event %s already added to registry", e.Kind))
	}
	spec := eventSpec{
		fxOptions: []fx.Option{
			fx.Invoke(fx.Annotate(func(
				reg *event.Registry,
				iSubs []*indexedSub[U],
			) error {
				return event.RegisterEventKind(
					reg, e.Kind, e.PayloadUnmarshalFn, serializeIndexedSubs(iSubs)...,
				)
			}, fx.ParamTags(``, indexedSubsGroupFxAnnotation))),
		},
		seenSubIndexes: make(map[int]bool),
	}
	events[e.Kind] = &spec
}

// AddSubscription adds a subscription to an event.
// idx is the index at which this subscription occurs
// in subscription list of the event.
// All the subscriptions of an event must occupy an unique
// integer idx between [0,len(subscriptions)).
//
// *IMPORTANT*
// IF A SUBSCRIPTION NEEDS TO BE REMOVED, IT'S ACTION *MUST* BE REPLACED WITH
// A NOOP ACTION WHILE STILL PRESERVING THE SUBSCRIPTION INDEX AND NAME.
// *IMPORTANT*
//
// Generally These Functions Are Called In The Init() Of
// The File Which Defines Eventregistration For An Event.
//
// NOTE: AddSubscription by itself is not very useful since
// most non-trivial actions will have dependencies (like the jobber on
// which to spawn job) which might need to be provided by fx.
// AddSubscriptionFxProviderFn can be used for that.
//
// See test_event.go for an example
//
//nolint:unused
func AddSubscription[U jtypes.Message](
	e *EventRegistration[U],
	idx int,
	sub *event.Subscription[U],
) {
	validateAndAddFxOpts(e.Kind, idx,
		fx.Provide(
			fx.Private,
			fx.Annotate(func() *indexedSub[U] {
				return &indexedSub[U]{idx: idx, sub: sub}
			}, fx.ResultTags(indexedSubsGroupFxAnnotation))),
	)
}

// AddSubscriptionFxProviderFn is similar to AddSubscription
// except it takes a function which when provided to an fx App
// will produce a Subscription[U]
// Refer to AddSubscription and event.SubscriptionFxProviderFn for more details.
func AddSubscriptionFxProviderFn[U jtypes.Message, D any](
	e *EventRegistration[U],
	idx int,
	subName string,
	provider event.SubscriptionFxProviderFn[U, D],
) {
	validateAndAddFxOpts(e.Kind, idx,
		fx.Provide(
			fx.Private,
			fx.Annotate(func(deps D) (*indexedSub[U], error) {
				sub, err := provider(deps)
				if err != nil {
					return nil, err
				}
				if subName != sub.Name {
					return nil, errors.Newf(
						"%s: subName(%s) in arguments doesn't match the one provided via fx",
						e.Kind, subName,
					)
				}
				return &indexedSub[U]{idx: idx, sub: sub}, nil
			}, fx.ResultTags(indexedSubsGroupFxAnnotation))),
	)
}

// DeletedSubscription is used to disable subscriptions since we still need to
// reserve the deleted subscription index and name for backwards compatiblity.
// nolint:unused
func DeletedSubscription[U jtypes.Message](
	e *EventRegistration[U], idx int, subName string,
) {
	// okay to do in init because:
	// - this just uses a NoOp action underneath so no deps
	// - Only called in events binary or in UTs requiring full event jobber
	sub, err := event.NewSubscription(subName, nil, event.WithNilAction[U]())
	if err != nil {
		panic(err)
	}
	AddSubscription(e, idx, sub)
}

// FxOptions returns an fx option that when added to an fx App will populate
// *event.Registry with jobs for all the event registrations and subscriptions
// in this directory.
// TODO: Take a map[Kind][]subName and only enable those, replacing
// all other subscriptions of all events with Noop
// TODO: After than we can just remove TestClient and implement those
// Pull APIs via store directly.
// Implementation details:
//  1. Create one fx module for each event.
//  2. Privately provide EventRegistration in RegisterEvent.
//  3. Privately provide all the subscribers for that event with a fx group annotation.
//     We do this inside AddSubscription* functions, where we provide *indexedSub[U]. We have to provide
//     indexedSub to serialize the collected subs since groups in fx are collected in arbitrary order.
//  4. Inside the module, Invoke event.RegisterEventKind using subscriptions collected
//     using group annotation used in 2 and (Kind,UnmarshalFn) collected from EventRegistration
//     provided in RegisterEvent. We add this invoke in RegisterEvent.
func FxOptions() fx.Option {
	var opts []fx.Option
	for k, v := range events {
		validateSeenSubIndexes(k, v.seenSubIndexes)
		opts = append(opts, fx.Module(string(k), v.fxOptions...))
	}
	return fx.Options(opts...)
}

// validateAndAddFxOpts does some pre-fx validations and then adds the
// fx Options corresponding to subscription at idx for event kind.
func validateAndAddFxOpts(kind event.Kind, idx int, opts ...fx.Option) {
	spec, ok := events[kind]
	if !ok {
		panic(errors.Newf("event %s not added to registry yet", kind))
	}
	if spec.seenSubIndexes[idx] {
		panic(errors.Newf("%s: index %d used for multiple subscriptions", kind, idx))
	}
	spec.seenSubIndexes[idx] = true
	spec.fxOptions = append(spec.fxOptions, opts...)
}

// no concurrency is needed because all RegisterEvent and AddSubscription happen
// in init and that is not concurrent.
var events = make(map[event.Kind]*eventSpec)

type indexedSub[U jtypes.Message] struct {
	idx int
	sub *event.Subscription[U]
}

type eventSpec struct {
	fxOptions      []fx.Option
	seenSubIndexes map[int]bool
}

func serializeIndexedSubs[U jtypes.Message](iSubs []*indexedSub[U]) (subs []*event.Subscription[U]) {
	subs = make([]*event.Subscription[U], len(iSubs))
	for i := range iSubs {
		subs[iSubs[i].idx] = iSubs[i].sub
	}
	return
}

func validateSeenSubIndexes(e event.Kind, seenIdx map[int]bool) {
	for k := range seenIdx {
		if k < 0 || k >= len(seenIdx) {
			panic(errors.Newf("%s: index %d is out of bounds", e, k))
		}
	}
}
