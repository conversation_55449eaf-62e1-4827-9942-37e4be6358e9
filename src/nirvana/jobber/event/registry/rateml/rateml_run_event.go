package rateml

import (
	"nirvanatech.com/nirvana/jobber/event"
	"nirvanatech.com/nirvana/jobber/event/registry"
	"nirvanatech.com/nirvana/quoting/jobs"
)

var rateMLRunEvent = &registry.EventRegistration[jobs.ReportRiskMetricsArgs]{
	Kind:               event.RateMLRunEvent,
	PayloadUnmarshalFn: jobs.ReportRiskMetricsUnmarshalFn,
}

func init() {
	registry.RegisterEvent(rateMLRunEvent)
	registry.AddSubscriptionFxProviderFn(
		rateMLRunEvent,
		0,
		jobs.ReportRiskMetricsSubscriptionName,
		jobs.ReportRiskMetricsSubscriptionFxProvider(),
	)
}
