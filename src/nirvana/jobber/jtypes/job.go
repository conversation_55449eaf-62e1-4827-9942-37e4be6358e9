package jtypes

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
)

const jobRunIdSeparator = "::"

var ErrNotAValidJobRunIdString = errors.Newf("not a valid jobRunId string")

type (
	JobId string
	RunId int
)

type JobRunId struct {
	JobId JobId `json:"jobId"`
	RunId RunId `json:"runId"`
}

func CreateJobId(registryKey, jobIdSuffix string) JobId {
	return JobId(registryKey + jobRunIdSeparator + jobIdSuffix)
}

// Suffix extracts the jobSuffix attached to this job id if present, otherwise returns zero value
func (j JobId) Suffix() string {
	_, suffix, err := j.getSubComponents()
	if err != nil {
		return ""
	}
	return suffix
}

func (j JobId) getSubComponents() (string, string, error) {
	registryKey, suffix, found := strings.Cut(string(j), "::")
	if !found {
		return "", "", errors.Newf("could not split jobId '%s' by ::", j)
	}
	return registryKey, suffix, nil
}

func (j JobRunId) String() string {
	return fmt.Sprintf("%s::%d", j.JobId, j.RunId)
}

func JobRunIdFromString(s string) (JobRunId, error) {
	if subs := strings.Split(s, jobRunIdSeparator); len(subs) == 3 {
		runId, err := strconv.Atoi(subs[2])
		if err != nil {
			return InvalidJobRunId, errors.Wrap(err, "could not parse runId")
		}
		return NewJobRunId(CreateJobId(subs[0], subs[1]), RunId(runId)), nil
	}
	return InvalidJobRunId, ErrNotAValidJobRunIdString
}

func NewJobRunId(jobId JobId, runId RunId) JobRunId {
	return JobRunId{JobId: jobId, RunId: runId}
}

type UnmarshalMessageFn[T Message] func(data []byte, m MessageVersion) (T, error)

// ResourceHintFn is the hint function provided by job writers. This func
// returns the resources( of the associated kind) used for a particular job.
// See function ResourceHint in this package.
type ResourceHintFn[T Message] func(context.Context, T) int64

// Job contains everything necessary for execution of one instance of a job.
type Job[T Message] struct {
	RegistryKey  string
	TaskCreators []TaskCreator[T]
	// UnmarshalMessageFn should unmarshal data into correct struct based on m.
	// Returned Message is passed to all the tasks of this job while executing.
	// If this method returns an error then currently we just log an error and
	// stop processing the job, but don't mark it as failed.
	UnmarshalMessageFn              UnmarshalMessageFn[T]
	ResourceHints                   map[ResourceType]ResourceHintFn[T]
	NextScheduledRunMessageRenderer NextScheduledRunMessageRendererFn[T]
	// CustomResourceCreators can be used to pass in custom resources which are acquired
	// in JobRunStatusRequired before any processor resources are acquired.
	CustomResourceCreators []CustomResourceCreator
}

type JobOption[T Message] func(*Job[T])

func NewJob[T Message](
	id string,
	taskCreators []TaskCreator[T],
	unmarshalMessageFn UnmarshalMessageFn[T],
	options ...JobOption[T],
) (*Job[T], error) {
	if unmarshalMessageFn == nil {
		return nil, errors.New("unmarshalMessageFn cannot be nil")
	}
	jb := &Job[T]{
		RegistryKey:                     id,
		TaskCreators:                    taskCreators,
		UnmarshalMessageFn:              unmarshalMessageFn,
		ResourceHints:                   make(map[ResourceType]ResourceHintFn[T]),
		NextScheduledRunMessageRenderer: DefaultNextScheduledRunMessageRenderer[T](),
	}
	for _, o := range options {
		o(jb)
	}
	return jb, nil
}

// NextScheduledRunMessageRenderer returns a JobOption that can be used to
// provide a custom NextScheduledRunMessageRendererFn to a Job.
func NextScheduledRunMessageRenderer[T Message](
	fn NextScheduledRunMessageRendererFn[T],
) JobOption[T] {
	return func(j *Job[T]) {
		j.NextScheduledRunMessageRenderer = fn
	}
}

// ResourceHint returns a JobOption which can be used to provide hints to
// Jobber about allocation of resoure r for this job.
// fn *MUST* be a pure function
// fn *MUST* be very very light weight, since we aren't very judicial about calling this
// multiple times for a single jobRun.
func ResourceHint[T Message](r ResourceType, fn ResourceHintFn[T]) JobOption[T] {
	return func(j *Job[T]) {
		j.ResourceHints[r] = fn
	}
}

// AllCustomResources returns a JobOption which can used to provide all the custom
// resources of a job. All is used to signify that allCustomResources should be
// entirety of custom resources for this job definition. If multiple JobOption
// returned by this function are used, only the last one is considered.
func AllCustomResources[T Message](allCustomResources []CustomResourceCreator) JobOption[T] {
	return func(j *Job[T]) {
		j.CustomResourceCreators = allCustomResources
	}
}

type JobRun struct {
	JobRunId
	Metadata
	State
	CreatorUuid uuid.UUID
}

func NewJobRun(
	jobRunId JobRunId,
	metadata Metadata,
	state State,
	creatorUuid uuid.UUID,
) *JobRun {
	return &JobRun{
		JobRunId:    jobRunId,
		Metadata:    metadata,
		State:       state,
		CreatorUuid: creatorUuid,
	}
}

func (j *JobRun) DeepCopy() (*JobRun, error) {
	cpy := &JobRun{
		JobRunId:    j.JobRunId,
		CreatorUuid: j.CreatorUuid,
	}
	var err error
	if cpy.Metadata, err = j.Metadata.DeepCopy(); err != nil {
		return nil, err
	}
	if cpy.State, err = j.State.DeepCopy(); err != nil {
		return nil, err
	}
	return cpy, nil
}
