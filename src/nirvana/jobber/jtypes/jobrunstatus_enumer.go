// Code generated by "enumer -type=JobRunStatus -json"; DO NOT EDIT.

package jtypes

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _JobRunStatusName = "JobRunStatusInvalidJobRunStatusEnqueuedJobRunStatusAcquiredJobRunStatusRunningJobRunStatusSucceededJobRunStatusPausedForRetryJobRunStatusAcquiredForRetryJobRunStatusPausedForUndoJobRunStatusAcquiredForUndoJobRunStatusUndoingJobRunStatusFailedJobRunStatusCancelledJobRunStatusKilled"

var _JobRunStatusIndex = [...]uint16{0, 19, 39, 59, 78, 99, 125, 153, 178, 205, 224, 242, 263, 281}

const _JobRunStatusLowerName = "jobrunstatusinvalidjobrunstatusenqueuedjobrunstatusacquiredjobrunstatusrunningjobrunstatussucceededjobrunstatuspausedforretryjobrunstatusacquiredforretryjobrunstatuspausedforundojobrunstatusacquiredforundojobrunstatusundoingjobrunstatusfailedjobrunstatuscancelledjobrunstatuskilled"

func (i JobRunStatus) String() string {
	if i < 0 || i >= JobRunStatus(len(_JobRunStatusIndex)-1) {
		return fmt.Sprintf("JobRunStatus(%d)", i)
	}
	return _JobRunStatusName[_JobRunStatusIndex[i]:_JobRunStatusIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _JobRunStatusNoOp() {
	var x [1]struct{}
	_ = x[JobRunStatusInvalid-(0)]
	_ = x[JobRunStatusEnqueued-(1)]
	_ = x[JobRunStatusAcquired-(2)]
	_ = x[JobRunStatusRunning-(3)]
	_ = x[JobRunStatusSucceeded-(4)]
	_ = x[JobRunStatusPausedForRetry-(5)]
	_ = x[JobRunStatusAcquiredForRetry-(6)]
	_ = x[JobRunStatusPausedForUndo-(7)]
	_ = x[JobRunStatusAcquiredForUndo-(8)]
	_ = x[JobRunStatusUndoing-(9)]
	_ = x[JobRunStatusFailed-(10)]
	_ = x[JobRunStatusCancelled-(11)]
	_ = x[JobRunStatusKilled-(12)]
}

var _JobRunStatusValues = []JobRunStatus{JobRunStatusInvalid, JobRunStatusEnqueued, JobRunStatusAcquired, JobRunStatusRunning, JobRunStatusSucceeded, JobRunStatusPausedForRetry, JobRunStatusAcquiredForRetry, JobRunStatusPausedForUndo, JobRunStatusAcquiredForUndo, JobRunStatusUndoing, JobRunStatusFailed, JobRunStatusCancelled, JobRunStatusKilled}

var _JobRunStatusNameToValueMap = map[string]JobRunStatus{
	_JobRunStatusName[0:19]:         JobRunStatusInvalid,
	_JobRunStatusLowerName[0:19]:    JobRunStatusInvalid,
	_JobRunStatusName[19:39]:        JobRunStatusEnqueued,
	_JobRunStatusLowerName[19:39]:   JobRunStatusEnqueued,
	_JobRunStatusName[39:59]:        JobRunStatusAcquired,
	_JobRunStatusLowerName[39:59]:   JobRunStatusAcquired,
	_JobRunStatusName[59:78]:        JobRunStatusRunning,
	_JobRunStatusLowerName[59:78]:   JobRunStatusRunning,
	_JobRunStatusName[78:99]:        JobRunStatusSucceeded,
	_JobRunStatusLowerName[78:99]:   JobRunStatusSucceeded,
	_JobRunStatusName[99:125]:       JobRunStatusPausedForRetry,
	_JobRunStatusLowerName[99:125]:  JobRunStatusPausedForRetry,
	_JobRunStatusName[125:153]:      JobRunStatusAcquiredForRetry,
	_JobRunStatusLowerName[125:153]: JobRunStatusAcquiredForRetry,
	_JobRunStatusName[153:178]:      JobRunStatusPausedForUndo,
	_JobRunStatusLowerName[153:178]: JobRunStatusPausedForUndo,
	_JobRunStatusName[178:205]:      JobRunStatusAcquiredForUndo,
	_JobRunStatusLowerName[178:205]: JobRunStatusAcquiredForUndo,
	_JobRunStatusName[205:224]:      JobRunStatusUndoing,
	_JobRunStatusLowerName[205:224]: JobRunStatusUndoing,
	_JobRunStatusName[224:242]:      JobRunStatusFailed,
	_JobRunStatusLowerName[224:242]: JobRunStatusFailed,
	_JobRunStatusName[242:263]:      JobRunStatusCancelled,
	_JobRunStatusLowerName[242:263]: JobRunStatusCancelled,
	_JobRunStatusName[263:281]:      JobRunStatusKilled,
	_JobRunStatusLowerName[263:281]: JobRunStatusKilled,
}

var _JobRunStatusNames = []string{
	_JobRunStatusName[0:19],
	_JobRunStatusName[19:39],
	_JobRunStatusName[39:59],
	_JobRunStatusName[59:78],
	_JobRunStatusName[78:99],
	_JobRunStatusName[99:125],
	_JobRunStatusName[125:153],
	_JobRunStatusName[153:178],
	_JobRunStatusName[178:205],
	_JobRunStatusName[205:224],
	_JobRunStatusName[224:242],
	_JobRunStatusName[242:263],
	_JobRunStatusName[263:281],
}

// JobRunStatusString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func JobRunStatusString(s string) (JobRunStatus, error) {
	if val, ok := _JobRunStatusNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _JobRunStatusNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to JobRunStatus values", s)
}

// JobRunStatusValues returns all values of the enum
func JobRunStatusValues() []JobRunStatus {
	return _JobRunStatusValues
}

// JobRunStatusStrings returns a slice of all String values of the enum
func JobRunStatusStrings() []string {
	strs := make([]string, len(_JobRunStatusNames))
	copy(strs, _JobRunStatusNames)
	return strs
}

// IsAJobRunStatus returns "true" if the value is listed in the enum definition. "false" otherwise
func (i JobRunStatus) IsAJobRunStatus() bool {
	for _, v := range _JobRunStatusValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for JobRunStatus
func (i JobRunStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for JobRunStatus
func (i *JobRunStatus) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("JobRunStatus should be a string, got %s", data)
	}

	var err error
	*i, err = JobRunStatusString(s)
	return err
}
