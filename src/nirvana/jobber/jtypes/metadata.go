package jtypes

import (
	"encoding/json"
	"time"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/log"
)

// Metadata contains configuration, scheduling and other non-unique static details
// for a job run like priority, job_type(one-off, scheduled or on-demand),
// schedule(if applicable), job-size(as of now undefined) etc.
// Different runs of same job can have different Metadata.
type Metadata struct {
	// Owners stores the identifier for the process/app/person
	// that added this job run.
	Owner       null.String `json:"owner"`
	RegistryKey string      `json:"registryKey"`
	RunType     RunType     `json:"runType"`
	// RequestedStartTime is the time at which user wants this
	// job to start. It should be nil for Immediate jobs
	RequestedStartTime null.Time         `json:"requestedStartTime"`
	Message            SerializedMessage `json:"message"`
}

func NewMetadata(runType RunType) Metadata {
	return Metadata{RunType: runType}
}

func (m Metadata) WithOwner(owner string) Metadata {
	m.Owner = null.StringFrom(owner)
	return m
}

func (m Metadata) WithRequestedStartTime(t time.Time) Metadata {
	m.RequestedStartTime = null.TimeFrom(t)
	return m
}

var (
	_ json.Marshaler      = (*Metadata)(nil)
	_ json.Unmarshaler    = (*Metadata)(nil)
	_ log.ObjectMarshaler = (*Metadata)(nil)
)

func (p *Metadata) MarshalJSON() ([]byte, error) {
	type Alias Metadata
	return json.Marshal(&struct {
		*Alias
	}{
		Alias: (*Alias)(p),
	})
}

func (p *Metadata) UnmarshalJSON(data []byte) error {
	type Alias Metadata
	aux := &struct {
		*Alias
	}{
		Alias: (*Alias)(p),
	}
	return json.Unmarshal(data, aux)
}

func (m Metadata) MarshalLogObject(encoder log.ObjectEncoder) error {
	if m.Owner.Valid {
		encoder.AddString("owner", m.Owner.String)
	}
	encoder.AddString("registryKey", m.RegistryKey)
	encoder.AddString("runType", m.RunType.String())
	if m.RequestedStartTime.Valid {
		encoder.AddTime("requestedStartTime", m.RequestedStartTime.Time)
	}
	// do not log Message Base64 as it can create unbounded length log lines
	return nil
}

func (p *Metadata) DeepCopy() (Metadata, error) {
	cpy := Metadata{}
	if p == nil {
		return cpy, nil
	}
	mBytes, err := p.MarshalJSON()
	if err != nil {
		return cpy, err
	}
	if err := cpy.UnmarshalJSON(mBytes); err != nil {
		return cpy, err
	}
	return cpy, nil
}
