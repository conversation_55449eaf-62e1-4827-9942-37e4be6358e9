load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "grpc_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/metaflow/grpc",
    proto = "//proto/metaflow:metaflow_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "grpc",
    embed = [":grpc_go_proto"],  # keep
    visibility = ["//visibility:public"],
)

# keep
go_proto_link(
    name = "grpc_go_proto_link",
    dep = ":grpc_go_proto",
    version = "v1",
)
