package main

import (
	"context"
	"encoding/json"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/batch"
	batchtypes "github.com/aws/aws-sdk-go-v2/service/batch/types"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ecs"
	"github.com/aws/aws-sdk-go-v2/service/ecs/types"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/metrics"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/metaflow/lambda/schema/aws/batch/batchjobstatechange"
	"nirvanatech.com/nirvana/metaflow/lambda/schema/aws/ecs/ecscontainerinstancestatechange"
)

type Input struct {
	Source  Source          `json:"source"`
	Payload json.RawMessage `json:"payload"`
}

// HandleEvent is the entrypoint for the lambda. Depending upon the input.Source specified it will
// emit some specific metaflow-related metrics.
func HandleEvent(ctx context.Context, input Input) error {
	cfg, err := aws_utils.LoadConfig(ctx)
	if err != nil {
		return err
	}
	statter, err := metrics.NewClient("metaflow")
	if err != nil {
		return err
	}
	switch input.Source {
	case Scheduled:
		return errors.Join(
			emitInstanceMetrics(ctx, cfg, statter),
			killStuckJobs(ctx, cfg),
		)
	case ECSContainerInstanceStateChange:
		var payload ecscontainerinstancestatechange.AWSEvent
		if err := json.Unmarshal(input.Payload, &payload); err != nil {
			return errors.Wrap(err, "could not parse event payload")
		}
		return emitBootTimeMetrics(ctx, cfg, statter, &payload)
	case BatchJobStateChange:
		var payload batchjobstatechange.AWSEvent
		if err := json.Unmarshal(input.Payload, &payload); err != nil {
			return errors.Wrap(err, "could not parse event payload")
		}
		return emitJobMetrics(ctx, cfg, statter, &payload)
	default:
		return errors.Newf("invalid source: %s", input.Source)
	}
}

type Source string

const (
	Scheduled                       Source = "Scheduled"
	ECSContainerInstanceStateChange Source = "ECS Container Instance State Change"
	BatchJobStateChange             Source = "Batch Job State Change"
)

const (
	// EnvIsMetaflowTagKey should be set with the tag key name applied to metaflow resources.
	// This will be used by lambda to identify if the given batch CE/cluster are metaflow-related.
	// Example ("module", "metaflow-poc")
	EnvIsMetaflowTagKey   = "METAFLOW_TAG_KEY"
	EnvIsMetaflowTagValue = "METAFLOW_TAG_VALUE"
	// EnvClusterNameTagKey should be set with the tag key name which stores the cluster name.
	// This can be used to disambiguate if an environment has multiple clusters.
	EnvClusterNameTagKey = "METAFLOW_CLUSTER_NAME_TAG_KEY"
	// EnvEnvironmentTagKey should be set with the tag key name which stores the environment to which a resource belongs
	EnvEnvironmentTagKey = "METAFLOW_ENVIRONMENT_TAG_KEY"
	// EnvEnvironmentValue should be set with the environment name for which this lambda applies.
	EnvEnvironmentValue = "METAFLOW_ENVIRONMENT"
	// EnvMaxWaitTime configures the maximum amount of time allowed for a job before it's considered stuck and forcibly killed.
	EnvMaxWaitTime     = "METAFLOW_BATCH_MAX_WAIT_TIME"
	DefaultMaxWaitTime = 15 * time.Minute
)

var (
	isMetaflowTagKey     = os.Getenv(EnvIsMetaflowTagKey)
	isMetaflowTagValue   = os.Getenv(EnvIsMetaflowTagValue)
	ecsClusterNameTagKey = os.Getenv(EnvClusterNameTagKey)
	envTagKey            = os.Getenv(EnvEnvironmentTagKey)
	envValue             = os.Getenv(EnvEnvironmentValue)
	maxWaitTimeStr       = os.Getenv(EnvMaxWaitTime)
)

// emitInstanceMetrics will be invoked periodically, and emits following GAUGEs dimensioned by (cluster, instance-type)
// cluster.cpu.total
// cluster.cpu.remaining
// cluster.memory.total
// cluster.memory.remaining
// cluster.runningTasks
func emitInstanceMetrics(ctx context.Context, cfg aws.Config, statter statsd.StatSender) error {
	ecsClient := ecs.NewFromConfig(cfg)
	batchClient := batch.NewFromConfig(cfg)

	// Get a list of all compute environments belonging to metaflow.
	// And then get the corresponding ECS cluster ARNs.
	var metaflowClusterArns []string
	batchCEs, err := batchClient.DescribeComputeEnvironments(ctx, &batch.DescribeComputeEnvironmentsInput{
		MaxResults: pointer_utils.Int32(100),
	})
	if err != nil {
		return errors.Wrapf(err, "Batch/DescribeComputeEnvironments")
	}
	for _, ce := range batchCEs.ComputeEnvironments {
		if val, ok := ce.Tags[isMetaflowTagKey]; !ok || val != isMetaflowTagValue {
			continue
		}
		if val, ok := ce.Tags[envTagKey]; !ok || val != envValue {
			continue
		}
		if ce.EcsClusterArn == nil {
			log.Error(ctx, "Batch/DescribeComputeEnvironments: CE doesn't have ECS cluster ARN",
				log.Stringp("batchComputeEnvironmentARN", ce.ComputeEnvironmentArn))
			continue
		}
		metaflowClusterArns = append(metaflowClusterArns, *ce.EcsClusterArn)
	}
	if len(metaflowClusterArns) == 0 {
		log.Warn(ctx, "Batch/DescribeComputeEnvironments: No metaflow-related compute environments found!")
		return nil
	}

	// Verify that given metaflow clusters exist
	describe, err := ecsClient.DescribeClusters(ctx, &ecs.DescribeClustersInput{
		Clusters: metaflowClusterArns,
		Include:  []types.ClusterField{types.ClusterFieldTags},
	})
	if err != nil {
		return errors.Wrapf(err, "ECS/DescribeClusters")
	}
	if len(describe.Failures) > 0 {
		for _, f := range describe.Failures {
			log.Error(ctx, "Failed ECS/DescribeClusters",
				log.Stringp("arn", f.Arn),
				log.Stringp("reason", f.Reason),
				log.Stringp("detail", f.Detail))
		}
		return errors.Newf("ECS/DescribeClusters: failed for %d clusters (check logs)", len(describe.Failures))
	}

clusterLoop:
	for _, c := range describe.Clusters {
		// We use a special tag in our ecs clusters to distinguish between multiple clusters if present
		// in the same environment.
		// The tag key is controlled by CDK/TF and will be passed to us via environment variable.
		// We just parse and read the value of this tag.
		clusterNameTag := slice_utils.Filter(c.Tags, func(t types.Tag) bool {
			return t.Key != nil && *t.Key == ecsClusterNameTagKey
		})
		if len(clusterNameTag) == 0 || clusterNameTag[0].Value == nil {
			log.Sugared.Warnf("Metaflow cluster %s has no tag with key '%s' (expected)",
				*c.ClusterArn, ecsClusterNameTagKey,
			)
			continue
		}
		clusterName := *clusterNameTag[0].Value

		// For each cluster fetch details about each container instance
		if c.RegisteredContainerInstancesCount == 0 {
			log.Info(ctx, "No container instances running in cluster",
				log.String("clusterARN", *c.ClusterArn))
			continue
		}

		// All metrics grouped by instance type
		instancesTotalCPU := make(map[string]int)
		instancesRemainingCPU := make(map[string]int)
		instancesTotalMemory := make(map[string]int)
		instancesRemainingMemory := make(map[string]int)
		instancesRunningTasks := make(map[string]int)

		firstIter := true
		var paginationToken *string
		for ; firstIter || (paginationToken != nil); firstIter = false {
			listInstances, err := ecsClient.ListContainerInstances(ctx, &ecs.ListContainerInstancesInput{
				Cluster:    c.ClusterArn,
				Status:     types.ContainerInstanceStatusActive,
				MaxResults: aws.Int32(100), // we use 100 as the DescribeContainerInstances API we use below can only accept a maximum of 100 at a time
				NextToken:  paginationToken,
			})
			if err != nil {
				return errors.Wrapf(err, "ECS/ListContainerInstances (clusterARN=%s)", *c.ClusterArn)
			}
			paginationToken = listInstances.NextToken
			if len(listInstances.ContainerInstanceArns) == 0 {
				log.Info(ctx, "No container instances running in cluster",
					log.String("clusterARN", *c.ClusterArn))
				continue clusterLoop
			}
			describeInstances, err := ecsClient.DescribeContainerInstances(ctx, &ecs.DescribeContainerInstancesInput{
				ContainerInstances: listInstances.ContainerInstanceArns,
				Cluster:            c.ClusterArn,
			})
			if err != nil {
				return errors.Wrapf(err, "ECS/DescribeContainerInstances (clusterARN=%s)", *c.ClusterArn)
			}
			if len(describeInstances.Failures) > 0 {
				for _, f := range describeInstances.Failures {
					log.Error(ctx, "Failed ECS/DescribeContainerInstances",
						log.Stringp("arn", f.Arn),
						log.Stringp("reason", f.Reason),
						log.Stringp("detail", f.Detail))
				}
				return errors.Newf("ECS/DescribeContainerInstances: failed for %d instances (check logs)", len(describe.Failures))
			}

			for _, instance := range describeInstances.ContainerInstances {
				var registeredCPU, registeredMemory int32
				for _, resource := range instance.RegisteredResources {
					if resource.Name == nil {
						continue
					}
					switch *resource.Name {
					case "CPU":
						registeredCPU = resource.IntegerValue
					case "MEMORY":
						registeredMemory = resource.IntegerValue
					}
				}
				if registeredCPU == 0 || registeredMemory == 0 {
					log.Error(ctx, "ECS/DescribeContainerInstances: can not detect CPU/Memory values",
						log.Any("detectedResources", instance.RegisteredResources))
					return errors.Newf("ECS/DescribeContainerInstances: can not detect CPU/Memory values")
				}
				var remainingCPU, remainingMemory int32
				for _, resource := range instance.RemainingResources {
					if resource.Name == nil {
						continue
					}
					switch *resource.Name {
					case "CPU":
						remainingCPU = resource.IntegerValue
					case "MEMORY":
						remainingMemory = resource.IntegerValue
					}
				}

				instanceTypeAttribute := slice_utils.Filter(instance.Attributes, func(attribute types.Attribute) bool {
					return *attribute.Name == "ecs.instance-type"
				})
				if len(instanceTypeAttribute) == 0 {
					log.Error(ctx, "ECS/DescribeContainerInstances: can not detect instance type",
						log.Any("detectedAttributes", instance.Attributes))
					return errors.Newf("ECS/DescribeContainerInstances: can not detect instance type")
				}
				instanceType := *instanceTypeAttribute[0].Value
				instancesTotalCPU[instanceType] += int(registeredCPU)
				instancesRemainingCPU[instanceType] += int(remainingCPU)
				instancesTotalMemory[instanceType] += int(registeredMemory)
				instancesRemainingMemory[instanceType] += int(remainingMemory)
				instancesRunningTasks[instanceType] += int(instance.RunningTasksCount)
			}
		}

		gauges := map[string]map[string]int{
			"cluster.cpu.total":        instancesTotalCPU,
			"cluster.cpu.remaining":    instancesRemainingCPU,
			"cluster.memory.total":     instancesTotalMemory,
			"cluster.memory.remaining": instancesRemainingMemory,
			"cluster.runningTasks":     instancesRunningTasks,
		}
		for instanceType := range instancesTotalCPU {
			tags := []statsd.Tag{
				{"env", envValue},
				{"cluster", clusterName},
				{"instance", instanceType},
			}
			logFields := slice_utils.Map(tags, func(tag statsd.Tag) log.Field {
				return log.String(tag[0], tag[1])
			})
			for gauge, gaugeValueGetter := range gauges {
				_ = statter.Gauge(gauge, int64(gaugeValueGetter[instanceType]), 1, tags...)
				// We also log the metrics for ad-hoc analytics through cloudwatch insights.
				log.Info(ctx, gauge, append(logFields, log.Int("value", gaugeValueGetter[instanceType]))...)
			}
			// We also log utilisation separately for cloudwatch insights
			log.Info(ctx, "cluster.cpu.utilisation", append(logFields,
				log.Float32("value", 1-float32(instancesRemainingCPU[instanceType])/
					float32(instancesTotalCPU[instanceType])))...)
			log.Info(ctx, "cluster.memory.utilisation", append(logFields,
				log.Float32("value", 1-float32(instancesRemainingMemory[instanceType])/
					float32(instancesTotalMemory[instanceType])))...)
		}
	}
	return nil
}

func killStuckJobs(
	ctx context.Context,
	cfg aws.Config,
) error {
	batchClient := batch.NewFromConfig(cfg)

	maxWaitTime := DefaultMaxWaitTime
	if maxWaitTimeStr != "" {
		maxWaitTimeParsed, err := time.ParseDuration(maxWaitTimeStr)
		if err != nil {
			log.Error(ctx, "failed to parse max wait time from env, using default",
				log.Err(err),
				log.Duration("default", maxWaitTime))
		} else {
			maxWaitTime = maxWaitTimeParsed
		}
	}

	// Get all batch job queues associated with metaflow
	// TODO: revise in future if number of queues becomes more than 100
	queues, err := batchClient.DescribeJobQueues(ctx, &batch.DescribeJobQueuesInput{
		MaxResults: aws.Int32(100),
	})
	if err != nil {
		log.Error(ctx, "failed to list job queues in aws batch",
			log.Err(err))
		return errors.Wrap(err, "Batch/DescribeJobQueues")
	}
	for _, queue := range queues.JobQueues {
		if queue.Tags[isMetaflowTagKey] != isMetaflowTagValue {
			log.Info(ctx, "Ignoring job queue because metaflow tag key not set",
				log.Stringp("queueName", queue.JobQueueName),
				log.Stringp("queueArn", queue.JobQueueArn),
			)
			continue
		}
		if queue.Tags[envTagKey] != envValue {
			log.Info(ctx, "Ignoring job queue because it does not belong to appropriate environment",
				log.Stringp("queueName", queue.JobQueueName),
				log.Stringp("queueArn", queue.JobQueueArn),
				log.String("queueEnv", queue.Tags[envTagKey]),
			)
			continue
		}
		log.Info(ctx, "Scanning job queue for stuck jobs",
			log.Stringp("jobQueue", queue.JobQueueName))
		// Find stuck jobs based on MaxWaitTime threshold
		var paginationToken *string
		for {
			jobs, err := batchClient.ListJobs(ctx, &batch.ListJobsInput{
				JobQueue:  queue.JobQueueArn,
				JobStatus: batchtypes.JobStatusRunnable,
				NextToken: paginationToken,
			})
			if err != nil {
				log.Error(ctx, "failed to list jobs in the queue",
					log.Stringp("queueArn", queue.JobQueueArn),
					log.Err(err),
				)
				return errors.Wrap(err, "Batch/ListJobs")
			}
			paginationToken = jobs.NextToken
			for _, job := range jobs.JobSummaryList {
				if job.CreatedAt == nil {
					log.Warn(ctx, "Skipping job because CreatedAt is not set",
						log.Stringp("jobName", job.JobName),
						log.Stringp("jobId", job.JobId))
					continue
				}
				// A job can also transition to RUNNABLE state after the first attempt has failed,
				// in this case the startedAt is non-nil and equal to startedAt of the last attempt.
				// We skip such jobs because:
				// 1. Batch does not provide a reliable way to calculate stuck time for these
				// 2. Most likely these can not be stuck as one attempt has already happened
				if job.StartedAt != nil && (*job.StartedAt) > 0 {
					log.Info(ctx, "Skipping job because job is at a attempt number > 1",
						log.Stringp("jobName", job.JobName),
						log.Stringp("jobId", job.JobId))
					continue
				}
				if time.Since(time.UnixMilli(*job.CreatedAt)) > maxWaitTime {
					// This job is classified as stuck and must be cancelled
					_, err = batchClient.TerminateJob(ctx, &batch.TerminateJobInput{
						JobId:  job.JobId,
						Reason: aws.String("cancelled by lambda because job was stuck in RUNNABLE state"),
					})
					if err != nil {
						log.Error(ctx, "failed to cancel the stuck job",
							log.Stringp("jobName", job.JobName))
						return errors.Wrapf(err, "Batch/TerminateJob")
					}
					log.Info(ctx, "Cancelled stuck job",
						log.Stringp("jobName", job.JobName),
						log.Stringp("jobId", job.JobId),
						log.Stringp("statusReason", job.StatusReason),
						log.Duration("maxWaitTime", maxWaitTime),
						log.Duration("actualWaitTime", time.Since(time.UnixMilli(*job.CreatedAt))),
					)
				}
			}
			if paginationToken == nil {
				break
			}
		}
		log.Info(ctx, "Completed scanning jobQueue",
			log.Stringp("jobQueue", queue.JobQueueName))
	}
	return nil
}

// emitBootTimeMetrics will be invoked when a new container instance warms up. It
// emits the following metrics, dimensioned by instance-type.
// DURATION instances.boottime
// COUNTER instances.spawned
func emitBootTimeMetrics(
	ctx context.Context,
	cfg aws.Config,
	statter statsd.StatSender,
	payload *ecscontainerinstancestatechange.AWSEvent,
) error {
	detail := payload.Detail
	// this payload's detail has a "version" field which is an always incrementing integer set by ECS.
	// The version=1 indicates the first ever event for a container instance, which indicates that
	// container instance was just registered.
	// Note that this field is different from payload's schema version (payload.Version)
	if detail.Version != 1 {
		// Although not an error, we should try to avoid invoking the lambda for these to avoid wastage of resources.
		log.Warn(ctx, "Unexpected state change type, expecting state change type with version=1",
			log.Float64("actualVersion", detail.Version))
		return nil
	}

	ec2Client := ec2.NewFromConfig(cfg)
	describe, err := ec2Client.DescribeInstances(ctx, &ec2.DescribeInstancesInput{
		DryRun:      nil,
		Filters:     nil,
		InstanceIds: []string{detail.Ec2InstanceId},
	})
	if err != nil {
		return errors.Wrapf(err, "EC2/DescribeInstances")
	}
	if len(describe.Reservations) == 0 || len(describe.Reservations[0].Instances) == 0 {
		return errors.Newf("EC2/DescribeInstances: received no instance details in a OK response")
	}
	instance := describe.Reservations[0].Instances[0]

	bootTime := detail.RegisteredAt.Sub(*instance.LaunchTime)

	tags := []statsd.Tag{
		{"env", envValue},
		{"instance", string(instance.InstanceType)},
	}
	logFields := slice_utils.Map(tags, func(tag statsd.Tag) log.Field {
		return log.String(tag[0], tag[1])
	})
	_ = statter.TimingDuration("instances.boottime", bootTime, 1, tags...)
	log.Info(ctx, "instances.boottime", append(logFields, log.Duration("value", bootTime))...)
	_ = statter.Inc("instances.spawned", 1, 1, tags...)
	log.Info(ctx, "instances.spawned", append(logFields, log.Duration("value", 1))...)
	return nil
}

// emitJobMetrics will be invoked when a batch job terminates. It emits:
// DURATION jobs.startuptime
func emitJobMetrics(
	ctx context.Context,
	cfg aws.Config,
	statter statsd.StatSender,
	payload *batchjobstatechange.AWSEvent,
) error {
	detail := payload.Detail
	expectedTerminalStatus := []string{string(batchtypes.JobStatusSucceeded), string(batchtypes.JobStatusFailed)}
	if !slice_utils.Exists(detail.Status, expectedTerminalStatus) {
		// Although not an error we should avoid lambda invocations for such cases
		log.Warn(ctx, "Received job status change event for unexpected state",
			log.Strings("expectedStates", expectedTerminalStatus),
			log.String("actualState", detail.Status))
		return nil
	}

	startedAt := payload.Time
	if len(detail.Attempts) > 0 && detail.Attempts[0].StartedAt > 0 {
		startedAt = time.UnixMilli(detail.Attempts[0].StartedAt)
	} else if detail.StartedAt > 0 {
		startedAt = time.UnixMilli(detail.StartedAt)
	}

	// The event payload does not contain information around tags attached to the job run.
	// So we make an API call to batch.
	batchClient := batch.NewFromConfig(cfg)
	describeJobs, err := batchClient.DescribeJobs(ctx, &batch.DescribeJobsInput{
		Jobs: []string{detail.JobId},
	})
	if err != nil {
		return errors.Wrap(err, "Batch/DescribeJobs")
	}
	tags := describeJobs.Jobs[0].Tags
	if tags["app"] != "metaflow" {
		log.Warn(ctx, "Does not look like a metaflow run, skipping",
			log.String("jobName", detail.JobName))
		return nil
	}
	// We emit the datadog metric with only the queue name as the name.
	// But for logs we increase the dimensionality for richer queries.
	startupTime := startedAt.Sub(time.UnixMilli(detail.CreatedAt))
	// Actually due to tag sanitisation we can not use job queue arn.
	// Once we actually have multiple clusters can revise this to add cluster name instead.
	metricTags := []statsd.Tag{
		{"env", envValue},
	}
	_ = statter.TimingDuration("jobs.startuptime", startupTime, 1.0, metricTags...)
	log.Info(ctx, "jobs.startuptime",
		log.String("job-queue", detail.JobQueue),
		log.String("job-id", detail.JobId),
		log.String("job-name", detail.JobName),
		log.String("flow", tags["metaflow.flow_name"]),
		log.String("step", tags["metaflow.step_name"]),
		log.Duration("value", startupTime),
	)
	return nil
}

func main() {
	lambda.Start(HandleEvent)
}
