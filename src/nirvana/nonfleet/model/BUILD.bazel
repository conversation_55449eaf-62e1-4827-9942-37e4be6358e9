load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "nonfleet_model_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/nonfleet/model",
    proto = "//proto/nonfleet/model:model_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/insurance-core/proto",
    ],
)

go_library(
    name = "model",
    srcs = [
        "business_owner.go",
        "driver.go",
        "exposure.go",
        "program_data_builder.go",
        "utils.go",
        "validations.go",
        "vehicle.go",
    ],
    embed = [":nonfleet_model_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/nonfleet/model",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/time_utils",
        "//nirvana/rating/rtypes",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

# keep
go_proto_link(
    name = "nonfleet_model_go_proto_link",
    dep = ":nonfleet_model_go_proto",
    version = "v1",
)
