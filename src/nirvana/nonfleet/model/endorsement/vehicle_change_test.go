package endorsement

import (
	"testing"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	nfmodel "nirvanatech.com/nirvana/nonfleet/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/insurance-bundle/model"
)

func TestVehicleChange_Validate(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name           string
		vehicleChange  *NonFleetChange_VehicleChange
		policyNumbers  []string
		policies       map[string]*model.Policy
		isErrExpected  bool
		expectedErrMsg string
	}{
		{
			name:           "nil vehicle change | outer struct",
			vehicleChange:  nil,
			isErrExpected:  true,
			expectedErrMsg: "VehicleChange cannot be nil",
		},
		{
			name:           "nil vehicle change | inner struct",
			vehicleChange:  nil,
			isErrExpected:  true,
			expectedErrMsg: "VehicleChange cannot be nil",
		},
		{
			name: "self validation failed",
			vehicleChange: &NonFleetChange_VehicleChange{
				VehicleChange: &VehicleChange{},
			},
			isErrExpected:  true,
			expectedErrMsg: "failed to self validate vehicle change",
		},
		{
			name: "invalid for policy failure",
			vehicleChange: &NonFleetChange_VehicleChange{
				VehicleChange: &VehicleChange{
					Remove: []string{"vin-1"},
				},
			},
			policyNumbers: []string{"pn-1"},
			policies: map[string]*model.Policy{
				"NNFTK4140010-22": model.NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).Build(),
				"pn-1": model.NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
					WithPolicyNumber("pn-1").Build(),
			},
			isErrExpected:  true,
			expectedErrMsg: "Vehicle change is not valid for policy number pn-1",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.vehicleChange.Validate(
				tc.policyNumbers,
				model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
					WithPolicies(tc.policies).Build(),
			)
			if tc.isErrExpected {
				require.Error(t, err)
				assert.ErrorContains(t, err, tc.expectedErrMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestVehicleChange_Apply(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name             string
		vehicleChange    *NonFleetChange_VehicleChange
		policyNumbers    []string
		nfProgramData    map[string]*nfmodel.NFAdmittedProgramDataV1
		expectedVehicles map[string][]*nfmodel.NFAdmittedVehicleDataV1
	}{
		{
			name: "add vehicle",
			vehicleChange: &NonFleetChange_VehicleChange{
				VehicleChange: &VehicleChange{
					Add: []*nfmodel.NFAdmittedVehicleDataV1{
						nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-3").Build(),
					},
				},
			},
			policyNumbers: []string{"pn-1"},
			nfProgramData: map[string]*nfmodel.NFAdmittedProgramDataV1{
				"NNFTK4140010-22": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
				"pn-1": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
			},
			expectedVehicles: map[string][]*nfmodel.NFAdmittedVehicleDataV1{
				"NNFTK4140010-22": getVehicleListForTesting(),
				"pn-1": append(
					getVehicleListForTesting(), nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-3").Build(),
				),
			},
		},
		{
			name: "remove vehicle",
			vehicleChange: &NonFleetChange_VehicleChange{
				VehicleChange: &VehicleChange{
					Remove: []string{"vin-1"},
				},
			},
			policyNumbers: []string{"pn-1"},
			nfProgramData: map[string]*nfmodel.NFAdmittedProgramDataV1{
				"NNFTK4140010-22": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
				"pn-1": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
			},
			expectedVehicles: map[string][]*nfmodel.NFAdmittedVehicleDataV1{
				"NNFTK4140010-22": getVehicleListForTesting(),
				"pn-1":            slice_utils.RemoveIndex(getVehicleListForTesting(), 0),
			},
		},
		{
			name: "update vehicle",
			vehicleChange: &NonFleetChange_VehicleChange{
				VehicleChange: &VehicleChange{
					Update: []*nfmodel.NFAdmittedVehicleDataV1{
						nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-1").WithStatedValue(50000).
							Build(),
					},
				},
			},
			policyNumbers: []string{"pn-1"},
			nfProgramData: map[string]*nfmodel.NFAdmittedProgramDataV1{
				"NNFTK4140010-22": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
				"pn-1": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
			},
			expectedVehicles: map[string][]*nfmodel.NFAdmittedVehicleDataV1{
				"NNFTK4140010-22": getVehicleListForTesting(),
				"pn-1": {
					nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-1").WithStatedValue(50000).
						Build(),
					getVehicleListForTesting()[1],
				},
			},
		},
		{
			name: "multiple policies",
			vehicleChange: &NonFleetChange_VehicleChange{
				VehicleChange: &VehicleChange{
					Add: []*nfmodel.NFAdmittedVehicleDataV1{
						nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-3").Build(),
					},
					Remove: []string{"vin-1"},
					Update: []*nfmodel.NFAdmittedVehicleDataV1{
						nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-2").WithStatedValue(50000).
							Build(),
					},
				},
			},
			policyNumbers: []string{"pn-1", "NNFTK4140010-22"},
			nfProgramData: map[string]*nfmodel.NFAdmittedProgramDataV1{
				"NNFTK4140010-22": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
				"pn-1": nfmodel.NewNFAdmittedProgramDataV1Builder().WithVehicles(getVehicleListForTesting()).
					Build(),
			},
			expectedVehicles: map[string][]*nfmodel.NFAdmittedVehicleDataV1{
				"NNFTK4140010-22": {
					nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-2").WithStatedValue(50000).
						Build(),
					nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-3").Build(),
				},
				"pn-1": {
					nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-2").WithStatedValue(50000).
						Build(),
					nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-3").Build(),
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			policies := make(map[string]*model.Policy)
			for pNumber, pd := range tc.nfProgramData {
				policies[pNumber] = model.NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
					WithNFProgramData(pd).Build()
			}
			ibSegment := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
				WithPolicies(policies).Build()

			tc.vehicleChange.Apply(tc.policyNumbers, ibSegment)
			for policyNumber, expectedVehicles := range tc.expectedVehicles {
				actualVehicles := ibSegment.GetPolicies()[policyNumber].GetProgramData().GetNonFleetData().Vehicles
				assert.ElementsMatchf(t, expectedVehicles, actualVehicles, policyNumber)
			}
		})
	}
}

func getVehicleListForTesting() []*nfmodel.NFAdmittedVehicleDataV1 {
	return []*nfmodel.NFAdmittedVehicleDataV1{
		nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-1").Build(),
		nfmodel.NewNFAdmittedVehicleDataV1Builder().WithVin("vin-2").Build(),
	}
}
