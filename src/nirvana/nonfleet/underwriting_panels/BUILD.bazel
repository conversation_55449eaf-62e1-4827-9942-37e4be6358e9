load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "underwriting_panels",
    srcs = [
        "admitted_panels_manager.go",
        "deps.go",
        "equipments.go",
        "fx.go",
        "losses.go",
        "packages.go",
        "safety.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/utils",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/type_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/utils",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/nonfleet/underwriting_panels/summary",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/underwriting/app_review/utils",
        "//nirvana/underwriting/app_review/widgets/global",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "//nirvana/underwriting/common",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_uber_go_fx//:fx",
    ],
)
