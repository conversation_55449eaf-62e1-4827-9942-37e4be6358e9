package underwriting_panels

import (
	"go.uber.org/fx"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/summary"
)

type AdmittedPanelsManager struct {
	fx.In

	AdmittedBasePanel       *base_panel.BasePanel[*admitted_app.AdmittedApp]
	AdmittedDriversPanel    *driver.DriversPanel[*admitted_app.AdmittedApp]
	AdmittedPackagesPanel   *PackagesPanel[*admitted_app.AdmittedApp]
	AdmittedLossesPanel     *LossesPanel[*admitted_app.AdmittedApp]
	AdmittedSummaryPanel    *summary.SummaryPanel[*admitted_app.AdmittedApp]
	AdmittedSafetyPanel     *SafetyPanel[*admitted_app.AdmittedApp]
	AdmittedEquipmentPanel  *EquipmentsPanel[*admitted_app.AdmittedApp]
	AdmittedOperationsPanel *operations.OperationsPanel[*admitted_app.AdmittedApp]
	AdmittedAppWrapper      nf_app.Wrapper[*admitted_app.AdmittedApp]
}
