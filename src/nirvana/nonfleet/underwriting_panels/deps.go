package underwriting_panels

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/safety/scorev2"
	"nirvanatech.com/nirvana/underwriting/common"
)

type Deps[T nf_app.AppInfo] struct {
	fx.In

	AppReviewWrapper         nf_app_review.Wrapper
	AppWrapper               nf_app.Wrapper[T]
	UWSafetyFetcher          *common.UWSafetyFetcher
	AuthWrapper              auth.DataWrapper
	AgencyWrapper            agency.DataWrapper
	Jobber                   quoting_jobber.Client
	AdmittedWrapper          nf_app.Wrapper[*admitted.AdmittedApp]
	SafetyScoreWidgetFactory scorev2.SafetyScoreWidgetFactory
}
