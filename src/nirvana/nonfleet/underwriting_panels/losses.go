package underwriting_panels

import (
	"context"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	file_upload_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

type LossesPanel[T application.AppInfo] struct {
	deps Deps[T]
}

func NewLossesPanel[T application.AppInfo](deps Deps[T]) *LossesPanel[T] {
	return &LossesPanel[T]{deps: deps}
}

func (l *LossesPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := l.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.SetPanelReview(nf_app_review.LossesPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}
			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (l *LossesPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsPanelReviewed(nf_app_review.LossesPanelType)
}

func (*LossesPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Losses
}

func (l *LossesPanel[T]) LossRuns(
	input *base_panel.PanelInput[T],
) []oapi_common.FileMetadata {
	var lrfs []oapi_common.FileMetadata
	for _, lrf := range input.Application.Info.GetLossRunFiles() {
		lrfs = append(lrfs, oapi_common.FileMetadata{
			Handle: pointer_utils.UUIDStringOrNil(lrf.Handle),
			Name:   lrf.Name,
		})
	}
	for _, lrf := range input.ApplicationReview.GetReviewDocuments().Files {
		if lrf.FileType != nil && *lrf.FileType == file_upload_enums.FileTypeLossRun {
			lrfs = append(lrfs, oapi_common.FileMetadata{
				Handle: pointer_utils.UUIDStringOrNil(lrf.Handle),
				Name:   lrf.Name,
			})
		}
	}
	return lrfs
}

func (l *LossesPanel[T]) GetLossInfo(
	input *base_panel.PanelInput[T],
) *oapi_uw.ApplicationReviewLosses {
	retval := input.Application.Info.GetLossPanelInfo()
	retval.IsReviewed = l.IsReviewed(input)
	retval.LossRunFiles = pointer_utils.ToPointer(l.LossRuns(input))

	return &retval
}
