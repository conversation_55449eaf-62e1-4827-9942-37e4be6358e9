package underwriting_panels

import (
	"context"

	"nirvanatech.com/nirvana/api-server/handlers/utils"
	enums2 "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/safety/scorev2"

	"github.com/cockroachdb/errors"
	openapi_types "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

type SafetyPanel[T application.AppInfo] struct {
	deps Deps[T]
}

var _ base_panel.Panel[application.AppInfo] = &SafetyPanel[application.AppInfo]{}

func NewSafetyPanel[T application.AppInfo](deps Deps[T]) *SafetyPanel[T] {
	return &SafetyPanel[T]{deps: deps}
}

func (s *SafetyPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := s.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.SetPanelReview(nf_app_review.SafetyPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}
			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (s *SafetyPanel[T]) SetSafetyCredit(ctx context.Context, appReviewID string, safetyCredit int) error {
	return s.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.UpdateOverrides(
				func(override *nf_app_review.Overrides) (nf_app_review.Overrides, error) {
					override.SafetyCredit = pointer_utils.Int(safetyCredit)
					return *override, nil
				})

			return review, err
		})
}

func (s *SafetyPanel[T]) GetSafetyCredit(
	input *base_panel.PanelInput[T],
) *int {
	return input.ApplicationReview.GetOverrides().SafetyCredit
}

func (s *SafetyPanel[T]) GetSafetyScore(ctx context.Context, reviewId string) (*underwriting.ApplicationReviewSafetyScoreV2, error) {
	safetyScoreWidget, err := s.deps.SafetyScoreWidgetFactory.NewSafetyScoreWidget(enums2.ProgramTypeNonFleetAdmitted)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create safety score service for review enum %s", enums2.ProgramTypeNonFleetAdmitted)
	}
	res, err := safetyScoreWidget.GetSafetyScoreWidgetData(ctx, reviewId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch SafetyScore data for review %v", reviewId)
	}
	response, err := utils.MapRiskScoreToOAPIResponse(ctx, *res)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to map risk score to OAPI response for review %v", reviewId)
	}
	return response, nil
}

func (s *SafetyPanel[T]) SetSafetyScore(
	ctx context.Context,
	appReviewId string,
	form underwriting.ApplicationReviewSafetyScoreFormV2,
) error {
	safetyScoreWidget, err := s.deps.SafetyScoreWidgetFactory.NewSafetyScoreWidget(enums2.ProgramTypeNonFleetAdmitted)
	if err != nil {
		return errors.Wrapf(err, "failed to create safety score service for review enum %s", enums2.ProgramTypeNonFleetAdmitted)
	}
	meta := utils.MapOAPIMetaToInternalModel(form.Meta)
	var selectScoreRequest *scorev2.SelectNewSafetyScoreRequest
	if form.Form != nil {
		selectScoreRequest = &scorev2.SelectNewSafetyScoreRequest{
			NewScoreTimestamp: form.Form.NewScoreTimestamp,
			ChangeReason:      form.Form.ChangeReason,
		}
	}
	return safetyScoreWidget.SetSafetyScore(ctx, appReviewId, selectScoreRequest, meta)
}

func (s *SafetyPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsPanelReviewed(nf_app_review.SafetyPanelType)
}

func (s *SafetyPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Safety
}

func (s *SafetyPanel[T]) CrashRecords(ctx context.Context, input *base_panel.PanelInput[T]) (
	oapi_common.CrashRecordHistory,
	*oapi_common.CrashRecordSummary,
	error,
) {
	crashHistory, crashSummary, err := s.deps.UWSafetyFetcher.GetCrashRecords(
		ctx,
		input.Application.Info.GetDot(),
		input.Application.Info.GetPowerUnitCount(),
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to get crash records")
	}
	return crashHistory, crashSummary, nil
}

func (s *SafetyPanel[T]) BasicScores(ctx context.Context, input *base_panel.PanelInput[T]) (oapi_common.BasicScoreThresholds, error) {
	return s.deps.UWSafetyFetcher.GetBasicScoresFromComputedMeasures(ctx, input.Application.Info.GetDot())
}

func (s *SafetyPanel[T]) OOSViolations(ctx context.Context, input *base_panel.PanelInput[T]) (oapi_common.OOSViolations, error) {
	oosViolations, _, _, err := s.deps.UWSafetyFetcher.OOSViolations(ctx, input.Application.Info.GetDot(), nil)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get OOS violations")
	}
	return oosViolations, nil
}

func (s *SafetyPanel[T]) DOTRating(ctx context.Context, input *base_panel.PanelInput[T]) (oapi_uw.SafetyRating, *openapi_types.Date, error) {
	rating, ratingDate, err := s.deps.UWSafetyFetcher.DOTRating(ctx, input.Application.Info.GetDot(), false)
	if err != nil {
		return oapi_uw.UnRated, nil, errors.Wrap(err, "unable to get DOT rating")
	}
	var safetyRating oapi_uw.SafetyRating
	if rating == nil {
		safetyRating = oapi_uw.UnRated
	} else {
		switch *rating {
		case "S":
			safetyRating = oapi_uw.Satisfactory
		case "U":
			safetyRating = oapi_uw.Unsatisfactory
		case "C":
			safetyRating = oapi_uw.Conditional
		case "Unrated":
			safetyRating = oapi_uw.UnRated
		default:
			return "", nil, errors.New("invalid DOT rating")
		}
	}
	return safetyRating, ratingDate, nil
}

func (s *SafetyPanel[T]) SevereViolations(ctx context.Context, input *base_panel.PanelInput[T]) (oapi_common.SevereViolations, error) {
	severeViolations, err := s.deps.UWSafetyFetcher.SevereViolations(
		ctx,
		input.Application.Info.GetDot(),
		input.ApplicationReview.GetEffectiveDate(),
	)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get severe violations")
	}
	return severeViolations, nil
}
