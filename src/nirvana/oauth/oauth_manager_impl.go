package oauth

import (
	"context"

	"github.com/google/uuid"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"nirvanatech.com/nirvana/common-go/log"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_models"
)

// EmbeddedOAuthManager implements OAuthManager. We need OAuthIntegration here to
// be able to inject it in OAuthHandleImpl when getting or creating the handle.
type EmbeddedOAuthManager struct {
	db          db_api.NirvanaRW
	clk         clock.Clock
	integration OAuthIntegration
}

func NewEmbeddedOAuthManager(
	db db_api.NirvanaRW,
	clk clock.Clock,
	integration OAuthIntegration,
) OAuthManager {
	return &EmbeddedOAuthManager{
		db:          db,
		clk:         clk,
		integration: integration,
	}
}

func (p *EmbeddedOAuthManager) GenerateRedirectURI(ctx context.Context, handleId, state string) (string, error) {
	err := p.createOauthHandleImpl(ctx, handleId)
	if err != nil {
		return "", errors.Wrapf(err, "Failed to create new Oauth Handle %s", handleId)
	}
	return p.integration.GenerateOAuthUrl(state), nil
}

func (p *EmbeddedOAuthManager) GetConnectionInfo(ctx context.Context, handleId string) (*OAuthConnectionInfo, error) {
	ctx = p.decorateCtx(ctx, handleId)
	return p.fetchOauthConnectionInfo(ctx, handleId)
}

func (p *EmbeddedOAuthManager) GetBulkConnectionInfo(ctx context.Context, handleIds []string) ([]*OAuthConnectionInfo, error) {
	return p.fetchBulkOauthConnectionInfo(ctx, handleIds...)
}

func (p *EmbeddedOAuthManager) Activate(ctx context.Context, handleId, authCode string) error {
	ctx = p.decorateCtx(ctx, handleId)
	return p.activate(ctx, handleId, authCode)
}

func (p *EmbeddedOAuthManager) GetAccessToken(ctx context.Context, handleId string) (string, error) {
	ctx = p.decorateCtx(ctx, handleId)
	return p.getAccessToken(ctx, handleId)
}

func (p *EmbeddedOAuthManager) ForceRefresh(ctx context.Context, handleId string) error {
	ctx = p.decorateCtx(ctx, handleId)
	return p.forceRefresh(ctx, handleId, p.clk.Now())
}

func (p *EmbeddedOAuthManager) RefreshStatus(ctx context.Context, handleId string) (OauthHandleStatus, error) {
	ctx = p.decorateCtx(ctx, handleId)
	info, err := p.GetConnectionInfo(ctx, handleId)
	if err != nil {
		return OauthHandleStatus(0), err
	}
	if info.State != OAuthHandleStatusActive && info.State != OAuthHandleStatusExpired {
		return info.State, nil
	}
	_, err = p.GetAccessToken(ctx, handleId)
	if err == nil {
		return OAuthHandleStatusActive, nil
	}
	if !errors.Is(err, ErrAccessRevoked) {
		return OauthHandleStatus(0), err
	}
	// Token is revoked, so we also update the state in DB
	_, err = (&db_models.OauthCredential{
		HandleID:      handleId,
		Discarded:     null.BoolFrom(true),
		LastUpdatedAt: p.clk.Now(),
	}).Update(
		ctx,
		p.db,
		boil.Whitelist(
			db_models.OauthCredentialColumns.Discarded,
			db_models.OauthCredentialColumns.LastUpdatedAt,
		),
	)
	if err != nil {
		return OauthHandleStatus(0), errors.Wrapf(err, "failed to update new state in db")
	}
	return OAuthHandleStatusRevoked, nil
}

func (p *EmbeddedOAuthManager) decorateCtx(ctx context.Context, handleId string) context.Context {
	return log.ContextWithFields(ctx,
		log.String("handleId", handleId),
		log.Stringer("provider", p.integration.Provider()),
	)
}

func (p *EmbeddedOAuthManager) Begin(ctx context.Context, handleId, state string) (string, error) {
	// The state must be more than 8 characters
	if len(state) <= 8 {
		return "", errors.New("The state must be more than 8 character.")
	}
	url, err := p.GenerateRedirectURI(ctx, handleId, state)
	if err == nil || errors.Is(err, ErrHandleAlreadyCreated) {
		// we permit ErrHandleAlreadyCreated to enable retries in case we fail to persist connection state
		connState := db_models.OauthConnectionState{
			HandleID: handleId,
			Value:    state,
			Provider: null.StringFrom(p.integration.Provider().String()),
		}
		err = connState.Insert(ctx, p.db, boil.Infer())
	}
	if err != nil {
		log.Error(ctx, ErrInitializeOAuth.Error(),
			log.String("provider", p.integration.Provider().String()),
			log.String("handleId", handleId),
			log.Err(err),
		)
		err = errors.Mark(err, ErrInitializeOAuth)
	}
	return url, err
}

func (p *EmbeddedOAuthManager) Finalize(ctx context.Context, connData *ConnCompleteData) (uuid.UUID, error) {
	log.Info(
		ctx, "Finalizing OAuth connection",
		log.Any("connData", connData),
	)

	handleId, err := func() (uuid.UUID, error) {
		if err := validateOAuthCompleteRequest(connData); err != nil {
			return uuid.Nil, errors.Wrap(err, "missing value(s) in response")
		}
		connState, err := db_models.OauthConnectionStates(
			db_models.OauthConnectionStateWhere.Value.EQ(*connData.State),
		).One(ctx, p.db)
		if err != nil {
			return uuid.Nil, errors.Wrap(err, "no handle for state (possibly malicious request)")
		}
		// MustParse is OK because HandleID is literally of UUID type in postgres table schema
		return uuid.MustParse(connState.HandleID), p.Activate(ctx, connState.HandleID, *connData.AuthCode)
	}()
	if err != nil {
		log.Error(ctx, ErrFinalizeOAuth.Error(),
			log.Any("connData", connData),
			log.Err(err))
		err = errors.Mark(err, ErrFinalizeOAuth)
	}
	return handleId, err
}

func validateOAuthCompleteRequest(connData *ConnCompleteData) error {
	switch {
	// Missing incoming data
	case connData == nil:
		return errors.New("missing connection data")
	// Callback error
	case connData.Error != nil:
		return errors.Newf("received error as param in url: %s", *connData.Error)
	// Missing incoming state
	case connData.State == nil:
		return errors.New("missing state")
	// Missing incoming auth code
	case connData.AuthCode == nil:
		return errors.New("missing auth code")
	}
	return nil
}

var _ OAuthManager = &EmbeddedOAuthManager{}
