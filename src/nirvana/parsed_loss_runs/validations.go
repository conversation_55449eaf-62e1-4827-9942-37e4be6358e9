package parsed_loss_runs

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/pibit_ai"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func validateParsedPolicies(parsedPolicies []pibit_ai.LossRunPolicy) error {
	// add validation logic for parsed policies here
	return nil
}

// validateMappedDBResponse validates the mapped database response
func validateMappedDBResponse(mappedDBObjects pibit.MappedDBObjects) error {
	if len(mappedDBObjects.Claims) > 0 && len(mappedDBObjects.Losses) == 0 {
		return errors.Wrap(ValidationError, "losses must exist if claims are present")
	}
	return nil
}
