package NIS_IM_014_01_24

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill"
	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "NIS_IM_014_01_24.pdf"

type Request struct {
	PolicyNumber  fields.StringM          `json:"PolicyNumber"`
	CompanyOnForm fields.InsuranceCarrier `json:"Company"`
}

var _ pdffill.Request = (*Request)(nil)

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
