package CUS_CA_400_11_22

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "CUS_CA_400_11_22.pdf"

type Request struct {
	NamedInsured        fields.StringXL    `json:"NamedInsured"`
	PolicyNumber        fields.StringS     `json:"PolicyNumber"`
	PolicyEffectiveDate fields.NonZeroDate `json:"PolicyEffectiveDate"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
