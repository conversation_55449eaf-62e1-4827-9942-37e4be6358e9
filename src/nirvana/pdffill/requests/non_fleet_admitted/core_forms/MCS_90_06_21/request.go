package MCS_90_06_21

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "MCS_90_06_21.pdf"

type Request struct {
	DotNumber                fields.StringL          `json:"DotNumber"`
	CurrentDate              fields.NonZeroDate      `json:"CurrentDate"`
	CompanyName              fields.StringXL         `json:"CompanyName"`
	CompanyState             fields.StringM          `json:"CompanyState"`
	IssueTime                fields.StringM          `json:"IssueTime"`
	IssueDay                 fields.StringM          `json:"IssueDay"`
	IssueMonth               fields.StringM          `json:"IssueMonth"`
	IssueYear                fields.StringM          `json:"IssueYear"`
	PolicyNumber             fields.StringM          `json:"PolicyNumber"`
	PolicyEffectiveDate      fields.NonZeroDate      `json:"PolicyEffectiveDate"`
	InsuranceCompany         fields.InsuranceCarrier `json:"InsuranceCompany"`
	LimitOfInsuranceAL       fields.OptionalStringM  `json:"LimitOfInsuranceAL"`
	PrimaryInsurance         fields.OptionalStringS  `json:"PrimaryInsurance"`
	ExcessInsurance          fields.OptionalStringS  `json:"ExcessInsurance"`
	ExcessLimitOfInsuranceAL fields.OptionalStringM  `json:"ExcessLimitOfInsuranceAL"`
	CompanyOnForm            fields.InsuranceCarrier `json:"Company"`
	PhoneNumber              fields.StringM          `json:"PhoneNumber"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
