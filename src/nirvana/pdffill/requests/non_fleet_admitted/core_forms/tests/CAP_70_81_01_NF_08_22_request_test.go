package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/CAP_70_81_01_NF_08_22"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCAP_70_81_01_NF_08_22Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request CAP_70_81_01_NF_08_22.Request
		wantRaw []byte
		wantErr bool
	}{
		{
			name: "pass",
			request: CAP_70_81_01_NF_08_22.Request{
				PolicyNumber:  "ABC",
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"PolicyNumber","value":"ABC"},
								{"name":"Company","value":"SiriusPoint America Insurance Company"}]`),
		},
		{
			name: "policy number too long",
			request: CAP_70_81_01_NF_08_22.Request{
				PolicyNumber: fields.StringM(random_utils.GenerateRandomString(40)),
			},
			wantErr: true,
		},
		{
			name: "policy number empty",
			request: CAP_70_81_01_NF_08_22.Request{
				PolicyNumber: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
