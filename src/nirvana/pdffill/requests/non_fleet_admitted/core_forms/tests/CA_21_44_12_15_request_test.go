package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/CA_21_44_12_15"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCA_21_44_12_15Request_InputValidations(t *testing.T) {
	validDate := fields.NonZeroDate{
		Time: time_utils.ParseOrDie("2060-01-01", time_utils.ISOLayout),
	}
	tests := []struct {
		name    string
		request CA_21_44_12_15.Request
		wantRaw []byte
		wantErr bool
	}{
		{
			name: "pass",
			request: CA_21_44_12_15.Request{
				PolicyNumber:             "ABC",
				NamedInsured:             "JOHN",
				EndorsementEffectiveDate: validDate,
				LimitOfInsuranceUMBI:     50000,
				CompanyOnForm:            fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"PolicyNumber","value":"ABC"},
								{"name":"NamedInsured","value":"JOHN"},
								{"name":"EndorsementEffectiveDate","value":"01/01/2060"},
								{"name":"LimitOfInsuranceUMBI","value":"50,000"},{"name":"Company","value":"SiriusPoint America Insurance Company"}]`),
		},
		{
			name: "policy number too long",
			request: CA_21_44_12_15.Request{
				PolicyNumber:             fields.StringM(random_utils.GenerateRandomString(40)),
				NamedInsured:             "JOHN",
				EndorsementEffectiveDate: validDate,
				LimitOfInsuranceUMBI:     50000,
			},
			wantErr: true,
		},
		{
			name: "policy number empty",
			request: CA_21_44_12_15.Request{
				PolicyNumber:             "",
				NamedInsured:             "JOHN",
				EndorsementEffectiveDate: validDate,
				// TODO: Find limit for this field.
				LimitOfInsuranceUMBI: 50000,
			},
			wantErr: true,
		},
		{
			name: "name insured is empty",
			request: CA_21_44_12_15.Request{
				PolicyNumber:             "ABC",
				NamedInsured:             "",
				EndorsementEffectiveDate: validDate,
				// TODO: Find limit for this field.
				LimitOfInsuranceUMBI: 50000,
			},
			wantErr: true,
		},
		{
			name: "limit of insurance UMBI is empty",
			request: CA_21_44_12_15.Request{
				PolicyNumber:             "ABC",
				NamedInsured:             "",
				EndorsementEffectiveDate: validDate,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
