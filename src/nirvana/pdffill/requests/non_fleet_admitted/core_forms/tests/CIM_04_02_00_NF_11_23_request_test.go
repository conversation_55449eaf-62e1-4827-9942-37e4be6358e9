package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/CIM_04_02_00_NF_11_23"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCIM_04_02_00_NF_11_23Request_InputValidations(t *testing.T) {
	validDate := fields.NonZeroDate{
		Time: time_utils.ParseOrDie("2060-01-01", time_utils.ISOLayout),
	}
	tests := []struct {
		name    string
		request CIM_04_02_00_NF_11_23.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: CIM_04_02_00_NF_11_23.Request{
				PolicyNumber:     "ABC123",
				NamedInsured:     "<PERSON>",
				PolicyPeriodFrom: validDate,
				PolicyPeriodTo:   validDate,
				CompanyOnForm:    fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[
					{"name": "PolicyNumber", "value": "ABC123"},
					{"name": "NamedInsured", "value": "John Doe"},
					{"name": "PolicyEffectiveDate", "value": "01/01/2060"},
					{"name": "PolicyExpirationDate", "value": "01/01/2060"},
					{"name": "Company", "value": "SiriusPoint America Insurance Company"}
				]`),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
