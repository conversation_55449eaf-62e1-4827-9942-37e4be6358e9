package tests

import (
	"testing"

	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/CIM_DS_10_NF_ES_10_24"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCIM_DS_10_NF_ES_10_24Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request CIM_DS_10_NF_ES_10_24.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: CIM_DS_10_NF_ES_10_24.Request{
				PolicyNumber:                    "ABC",
				Company:                         fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
				Producer:                        fields.InsuranceProducer(constants.InsuranceProducerNirvana),
				ProducerMailingAddress:          "1234 Main St",
				AgentNumber:                     "123-4",
				TXSLTaxMTC:                      "10,000",
				TXSLStampingFeeMTC:              "10,000",
				Commodities:                     "List of commodities",
				CargoLimit:                      250000,
				CargoHandlingEquipment:          10000,
				EarnedChargesLimit:              2500,
				FireDeptServiceCharges:          100000,
				PollutantCleanUpandRemovalLimit: 100000,
				CargoDeductible:                 5000,
				CargoLossMitigationExpenses:     5000,
				RemovalExpenses:                 100000,
				ReloadExpenses:                  100000,
				TrafficAndSecurityExpense:       5000,
				CargoRate:                       "5.647",
				CargoTotalPremium:               "32,000",
				SpecialProvisions:               "MOTOR TRUCK CARGO PREMIUMS AND EXPOSURES WILL BE AUDITED ON AN ANNUAL BASIS.",
				PropertyInOrOnAnyOneAutomobile:  "Yes",
				CompanyMailingAddress:           "1234 Main St",
				Broker:                          "John Doe",
				BrokerMailingAddress:            "1234 Main St",
				CompanyOnForm:                   fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name": "TXSLTaxMTC", "value": "10,000"},{"name": "TXSLStampingFeeMTC", "value": "10,000"},{"name": "ProducerName", "value": "Nirvana Insurance Services LLC"},{"name": "Broker", "value": "John Doe"},{"name": "BrokerMailingAddress", "value": "1234 Main St"},{"name": "CompanyMailingAddress", "value": "1234 Main St"},{"name": "ProducerMailingAddress", "value": "1234 Main St"},{"name": "AgentNumber", "value": "123-4"},{"name":"PolicyNumber","value":"ABC"},{"name":"Company","value":"SiriusPoint America Insurance Company"},{"name":"CompanyName","value":"SiriusPoint America Insurance Company"},{"name":"Commodities","value":"List of commodities"},{"name":"CargoLimit","value":"250,000"},{"name":"CargoHandlingEquipment","value":"10,000"},{"name":"EarnedChargesLimit","value":"2,500"},{"name":"FireDeptServiceCharges","value":"100,000"},{"name":"PollutantCleanUpandRemoval","value":"100,000"},{"name":"CargoDeductible","value":"5,000"},{"name":"CargoLossMitigationExpense","value":"5,000"},{"name":"RemovalExpenses","value":"100,000"},{"name":"ReloadExpenses","value":"100,000"},{"name":"TrafficAndSecurityExpense","value":"5,000"},{"name":"CargoRate","value":"5.647"},{"name":"CargoTotalPremium","value":"32,000"},{"name":"SpecialProvisions","value":"MOTOR TRUCK CARGO PREMIUMS AND EXPOSURES WILL BE AUDITED ON AN ANNUAL BASIS."},{"name":"PropertyInOrOnAnyOneAutomobile","value":"Yes"}]`),
		},
		{
			name:    "fields are empty",
			request: CIM_DS_10_NF_ES_10_24.Request{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
