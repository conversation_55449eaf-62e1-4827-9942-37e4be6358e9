package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/IL_70_08_42_NF_10_24"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestIL_70_08_42_NF_10_24Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request IL_70_08_42_NF_10_24.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: IL_70_08_42_NF_10_24.Request{
				PolicyNumber:  "ABC",
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"PolicyNumber","value":"ABC"},{"name":"Company","value":"SiriusPoint America Insurance Company"}]`),
		},
		{
			name: "policy number too long",
			request: IL_70_08_42_NF_10_24.Request{
				PolicyNumber:  fields.StringM(random_utils.GenerateRandomString(40)),
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: true,
		},
		{
			name: "policy number empty",
			request: IL_70_08_42_NF_10_24.Request{
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
