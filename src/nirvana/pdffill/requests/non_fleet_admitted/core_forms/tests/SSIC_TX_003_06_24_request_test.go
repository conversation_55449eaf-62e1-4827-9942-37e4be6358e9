package tests

import (
	"testing"

	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/SSIC_TX_003_06_24"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestSSIC_TX_003_06_24Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request SSIC_TX_003_06_24.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: SSIC_TX_003_06_24.Request{
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"Company","value":"SiriusPoint America Insurance Company"}]`),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
