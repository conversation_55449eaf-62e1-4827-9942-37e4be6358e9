package CA_04_30_11_20

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"

	"nirvanatech.com/nirvana/pdffill"
)

const FormName = "CA_04_30_11_20.pdf"

type Request struct {
	PolicyNumber             fields.StringM          `json:"PolicyNumber"`
	NamedInsured             fields.StringXL         `json:"NamedInsured"`
	EndorsementEffectiveDate fields.NonZeroDate      `json:"EndorsementEffectiveDate"`
	CompanyName              fields.InsuranceCarrier `json:"CompanyName"`
	CompanyOnForm            fields.InsuranceCarrier `json:"Company"`
}

var _ pdffill.Request = (*Request)(nil)

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
