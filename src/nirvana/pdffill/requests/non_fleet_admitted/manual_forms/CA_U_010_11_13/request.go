package CA_U_010_11_13

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill"
	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "CA_U_010_11_13.pdf"

type Request struct {
	PolicyNumber           fields.StringM           `json:"PolicyNumber"`
	CompanyName            fields.InsuranceCarrier  `json:"CompanyName"`
	Producer               fields.InsuranceProducer `json:"Producer"`
	ApplicantOrNameInsured fields.StringXL          `json:"ApplicantOrNameInsured"`
	PolicyEffectiveDate    fields.NonZeroDate       `json:"PolicyEffectiveDate"`
}

var _ pdffill.Request = (*Request)(nil)

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
