package IL_00_31_01_06

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill"
	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "IL_00_31_01_06.pdf"

type Request struct {
	PolicyNumber  fields.StringM          `json:"PolicyNumber"`
	SFPStates     fields.StringL          `json:"SFPStates"`
	CompanyOnForm fields.InsuranceCarrier `json:"CompanyName"`
}

var _ pdffill.Request = (*Request)(nil)

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
