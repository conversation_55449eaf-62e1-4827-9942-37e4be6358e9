package IL_12_01_11_85

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "IL_12_01_11_85.pdf"

type Request struct {
	PolicyNumber             fields.StringM           `json:"PolicyNumber"`
	PolicyChangeNumber       fields.StringM           `json:"PolicyChangeNumber"`
	PolicyChangesEffective   fields.NonZeroDate       `json:"PolicyChangesEffective"`
	AuthorizedRepresentative fields.StringL           `json:"AuthorizedRepresentative"`
	NamedInsured             fields.StringXXL         `json:"NamedInsured"`
	CoverageAffected         fields.OptionalStringXXL `json:"CoverageAffected"`
	Changes                  string                   `json:"Changes"`
	ModificationText         string                   `json:"ModificationText"`
	CompanyOnForm            fields.InsuranceCarrier  `json:"Company"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
