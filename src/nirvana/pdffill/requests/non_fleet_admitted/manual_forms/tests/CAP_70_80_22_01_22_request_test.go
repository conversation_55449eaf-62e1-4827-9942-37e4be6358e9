package tests

import (
	"testing"

	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/manual_forms/CAP_70_80_22_01_22"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCAP_70_80_22_01_22Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request CAP_70_80_22_01_22.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: CAP_70_80_22_01_22.Request{
				PolicyNumber:  "XYZ",
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"Company","value":"SiriusPoint America Insurance Company"},{"name":"PolicyNumber","value":"XYZ"}]`),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
