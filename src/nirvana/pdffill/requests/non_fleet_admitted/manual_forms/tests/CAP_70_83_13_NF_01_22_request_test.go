package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/manual_forms/CAP_70_83_13_NF_01_22"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCAP_70_83_13_NF_01_22Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request CAP_70_83_13_NF_01_22.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: CAP_70_83_13_NF_01_22.Request{
				PolicyNumber:               "ABC",
				NameOfPersonOrOrganisation: "Blanket applies where required by written contract",
				CompanyOnForm:              fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"PolicyNumber","value":"ABC"},{"name":"NameOfPersonOrOrganisation", "value":"Blanket applies where required by written contract"},{"name":"Company", "value":"SiriusPoint America Insurance Company"}]`),
		},
		{
			name: "fields are too long",
			request: CAP_70_83_13_NF_01_22.Request{
				PolicyNumber:               fields.StringM(random_utils.GenerateRandomString(40)),
				NameOfPersonOrOrganisation: "Blanket applies where required by written contract",
				CompanyOnForm:              fields.InsuranceCarrier(constants.InsuranceCarrierSiriusPoint),
			},
			wantErr: true,
		},
		{
			name:    "fields are empty",
			request: CAP_70_83_13_NF_01_22.Request{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
