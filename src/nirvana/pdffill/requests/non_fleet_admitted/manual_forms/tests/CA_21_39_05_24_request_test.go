package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/manual_forms/CA_21_39_05_24"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCA_21_39_05_24Request_InputValidations(t *testing.T) {
	validDate := fields.NonZeroDate{
		Time: time_utils.ParseOrDie("2060-01-01", time_utils.ISOLayout),
	}
	tests := []struct {
		name    string
		request CA_21_39_05_24.Request
		wantRaw []byte
		wantErr bool
	}{
		{
			name: "pass",
			request: CA_21_39_05_24.Request{
				PolicyNumber:                     "ABC",
				NamedInsured:                     "JOHN",
				EndorsementEffectiveDate:         validDate,
				CompanyOnForm:                    fields.InsuranceCarrier(constants.InsuranceCarrierFalseLake),
				LimitOfInsuranceUMBIEachAccident: 50000,
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"PolicyNumber","value":"ABC"},
								{"name":"NamedInsured","value":"JOHN"},
								{"name":"EndorsementEffectiveDate","value":"01/01/2060"},
								{"name":"Company","value":"Falls Lake National Insurance Company"},
								{"name":"LimitOfInsuranceUMBIEachAccident","value":"50,000"}]`),
		},
		{
			name: "policy number too long",
			request: CA_21_39_05_24.Request{
				PolicyNumber:                     fields.StringM(random_utils.GenerateRandomString(40)),
				NamedInsured:                     "JOHN",
				EndorsementEffectiveDate:         validDate,
				LimitOfInsuranceUMBIEachAccident: 50000,
			},
			wantErr: true,
		},
		{
			name: "policy number empty",
			request: CA_21_39_05_24.Request{
				PolicyNumber:             "",
				NamedInsured:             "JOHN",
				EndorsementEffectiveDate: validDate,
				// TODO: Find limit for this field.
				LimitOfInsuranceUMBIEachAccident: 50000,
			},
			wantErr: true,
		},
		{
			name: "name insured is empty",
			request: CA_21_39_05_24.Request{
				PolicyNumber:             "ABC",
				NamedInsured:             "",
				EndorsementEffectiveDate: validDate,
				// TODO: Find limit for this field.
				LimitOfInsuranceUMBIEachAccident: 50000,
			},
			wantErr: true,
		},
		{
			name: "limit of insurance UMBI is empty",
			request: CA_21_39_05_24.Request{
				PolicyNumber:             "ABC",
				NamedInsured:             "",
				EndorsementEffectiveDate: validDate,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
