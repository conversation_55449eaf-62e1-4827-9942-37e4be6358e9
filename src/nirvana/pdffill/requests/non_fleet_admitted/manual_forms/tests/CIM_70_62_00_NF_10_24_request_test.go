package tests

import (
	"testing"

	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/manual_forms/CIM_70_62_00_NF_10_24"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestCIM_70_62_00_NF_10_24Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request CIM_70_62_00_NF_10_24.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: CIM_70_62_00_NF_10_24.Request{
				PolicyNumber:  "ABC",
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierFalseLake),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"PolicyNumber","value":"ABC"},{"name":"Company","value":"Falls Lake National Insurance Company"}]`),
		},
		{
			name: "policy number too long",
			request: CIM_70_62_00_NF_10_24.Request{
				PolicyNumber:  fields.StringM(random_utils.GenerateRandomString(40)),
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierFalseLake),
			},
			wantErr: true,
		},
		{
			name:    "policy number empty",
			request: CIM_70_62_00_NF_10_24.Request{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
