package tests

import (
	"testing"

	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/manual_forms/IL_00_31_01_06"
	"nirvanatech.com/nirvana/pdffill/test_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestIL_00_31_01_06Request_InputValidations(t *testing.T) {
	tests := []struct {
		name    string
		request IL_00_31_01_06.Request
		wantErr bool
		wantRaw []byte
	}{
		{
			name: "successfully marshal for valid",
			request: IL_00_31_01_06.Request{
				PolicyNumber:  "XYZ",
				SFPStates:     "Joe",
				CompanyOnForm: fields.InsuranceCarrier(constants.InsuranceCarrierFalseLake),
			},
			wantErr: false,
			wantRaw: []byte(`[{"name":"CompanyName","value":"Falls Lake National Insurance Company"},{"name":"SFPStates","value":"<PERSON>"},{"name":"PolicyNumber","value":"XYZ"}]`),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test_utils.ExecuteSimpleTestCase(t, &tt.request, tt.wantRaw, tt.wantErr)
		})
	}
}
