load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "CA_U_012_07_20",
    srcs = ["request.go"],
    importpath = "nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/signed_quote_forms/CA_U_012_07_20",
    visibility = ["//visibility:public"],
    deps = ["//nirvana/pdffill/fields"],
)

go_test(
    name = "CA_U_012_07_20_test",
    srcs = ["request_test.go"],
    embed = [":CA_U_012_07_20"],
    deps = [
        "//nirvana/common-go/random_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/pdffill/fields",
        "//nirvana/pdffill/test_utils",
        "//nirvana/policy_common/constants",
    ],
)
