package CIN_00_02_00_NF_01_21

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "CIN_00_02_00_NF_01_21.pdf"

type Request struct {
	PolicyNumberAL         fields.StringM          `json:"PolicyNumberAL"`
	PolicyNumberGL         fields.OptionalStringM  `json:"PolicyNumberGL"`
	PolicyNumberMTC        fields.OptionalStringM  `json:"PolicyNumberMTC"`
	CompanyName            fields.InsuranceCarrier `json:"CompanyName"`
	ApplicantOrNameInsured fields.StringXL         `json:"ApplicantOrNameInsured"`
	CompanyOnForm          fields.InsuranceCarrier `json:"Company"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
