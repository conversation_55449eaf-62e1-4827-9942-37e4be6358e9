package IL_U_041_12_08

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "IL_U_041_12_08.pdf"

type Request struct {
	PolicyNumber           fields.StringM           `json:"PolicyNumber"`
	CompanyName            fields.InsuranceCarrier  `json:"CompanyName"`
	CompanyOnForm          fields.InsuranceCarrier  `json:"Company"`
	Producer               fields.InsuranceProducer `json:"Producer"`
	ApplicantOrNameInsured fields.StringXL          `json:"NamedInsured"`
	PolicyEffectiveDate    fields.NonZeroDate       `json:"PolicyEffectiveDate"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
