import { Style } from '@react-pdf/types';
import { getFormattedDate } from '../../helpers/date';

// ===========================
// FORMATTING UTILITIES
// ===========================

/**
 * Format currency values - handles both numbers and special strings
 */
export const formatCurrency = (value: number | string): string => {
  // Handle special strings like "Incl.", "N/A", "Included"
  if (typeof value === 'string' && isNaN(Number(value))) {
    return value;
  }

  const amount = typeof value === 'string' ? Number(value) : value;
  if (amount == null || isNaN(amount)) return '0';

  return amount.toLocaleString();
};

/**
 * Format limit values with optional metadata
 */
export const formatLimit = (limit: number | string, limitMeta?: string): string => {
  // Handle string limits like "See Specific Unit"
  if (typeof limit === 'string' && isNaN(Number(limit))) {
    return limit;
  }

  const amount = typeof limit === 'string' ? Number(limit) : limit;
  if (isNaN(amount)) return '0';

  const formatted = amount.toLocaleString();
  const withSign = `$${formatted}`;

  return limitMeta ? `${withSign} ${limitMeta}` : withSign;
};

/**
 * Format vehicle display name consistently
 */
export const formatVehicleName = (
  year: string | number,
  make: string,
  model: string,
  vin?: string
): string => {
  const baseString = `${year} ${make.toUpperCase()} ${model.toUpperCase()}`;
  return vin ? `${baseString} (${vin})` : baseString;
};

// ===========================
// STYLE UTILITIES
// ===========================

/**
 * Generate padded unit numbers
 */
export const generateUnitNumber = (index: number): string => {
  return (index + 1).toString().padStart(2, '0');
};

/**
 * Create column style with width and padding
 */
export const createColumn = (
  width: string,
  paddingHorizontal: number = 12,
  justifyContent: 'flex-start' | 'center' | 'flex-end' = 'center'
): Style => ({
  width,
  paddingHorizontal,
  justifyContent,
});

/**
 * Create row style with height and spacing
 */
export const createRow = (
  height: number = 44,
  paddingHorizontal: number = 8,
  borderColor?: string
): Style => ({
  height,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingHorizontal,
  ...(borderColor && {
    borderBottomWidth: 1,
    borderBottomColor: borderColor,
  }),
});

// ===========================
// VALIDATION UTILITIES
// ===========================

/**
 * Check if array has items
 */
export const hasItems = <T>(array: T[] | undefined | null): array is T[] => {
  return Array.isArray(array) && array.length > 0;
};

// ===========================
// COMPONENT UTILITIES
// ===========================

/**
 * Create consistent page footer data
 */
export const createFooterData = (
  policyStartDate: string,
  policyNumber: string,
  pageNumber: string,
  timestamp?: string
) => {
  // Use timestamp if available, otherwise use policy start date
  // Always use the date formatter for consistent UTC timestamp handling
  const dateToFormat = timestamp || policyStartDate;
  const dateFormatted = getFormattedDate(dateToFormat, 'MM.dd.yyyy');

  return {
    left: `${dateFormatted} | #${policyNumber.toUpperCase()}`,
    right: 'nirvanatech.com',
    pageNumber: pageNumber.padStart(2, '0'),
  };
};
