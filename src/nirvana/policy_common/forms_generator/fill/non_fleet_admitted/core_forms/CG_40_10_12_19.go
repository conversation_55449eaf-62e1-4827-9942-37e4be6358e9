package core_forms

import (
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/CG_40_10_12_19"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms/generated/non_fleet_admitted/general_liability"

	"github.com/cockroachdb/errors"
)

func genCG40101219Req(formCode string, inputs CoreFormsInputs) (*CG_40_10_12_19.Request, error) {
	if formCode != general_liability.CG40101219.Code {
		return nil, errors.Newf(
			"unexpected form code received, supported code %s",
			general_liability.CG40101219.Code,
		)
	}
	return &CG_40_10_12_19.Request{
		CompanyOnForm: fields.InsuranceCarrier(inputs.CompanyOnForm),
	}, nil
}
