package core_forms

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/core_forms/IL_02_76_09_08"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms/generated/non_fleet_admitted/auto_liability"
)

func genIL02760908Req(formCode string, inputs CoreFormsInputs) (*IL_02_76_09_08.Request, error) {
	if formCode != auto_liability.IL02760908.Code {
		return nil, errors.Newf(
			"unexpected form code received, supported code %s",
			auto_liability.IL02760908.Code,
		)
	}
	return &IL_02_76_09_08.Request{
		CompanyOnForm: fields.InsuranceCarrier(inputs.CompanyOnForm),
	}, nil
}
