package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"

	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	pricing_schema "nirvanatech.com/nirvana/db-api/db_models/pricing"
	components "nirvanatech.com/nirvana/openapi-specs/components/simulation"
	"nirvanatech.com/nirvana/pricing/simulation/db"
	simulation_common_files "nirvanatech.com/nirvana/pricing/simulation/engine/common/files"
	"nirvanatech.com/nirvana/pricing/simulation/enums"
)

type HandleGetSimulationOutputDownloadUrlDeps struct {
	fx.In

	SimulationWrapper db.Wrapper
	S3Client          s3_utils.Client
}

func HandleGetSimulationOutputDownloadUrl(
	ctx context.Context,
	deps *HandleGetSimulationOutputDownloadUrlDeps,
	simulationId uuid.UUID,
) (*components.SimulationOutputDownloadUrlResponse, *common.HttpHandlerError) {
	simulationRun, err := deps.SimulationWrapper.GetSimulationRun(ctx, pricing_schema.SimulationRunWhere.ID.EQ(simulationId.String()))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, common.NewHttpErrorf(http.StatusNotFound, "simulation not found")
		}

		return nil, common.NewHttpErrorf(http.StatusInternalServerError, "failed to get simulation: %v", err)
	}

	if simulationRun.Status != enums.RunStatusSucceeded {
		return nil, common.NewHttpErrorf(http.StatusBadRequest, "simulation is not completed")
	}

	if simulationRun.OutputFileKey == nil || *simulationRun.OutputFileKey == "" {
		return nil, common.NewHttpErrorf(http.StatusInternalServerError, "simulation output file location is not set or is empty")
	}

	presignedURL, err := simulation_common_files.GetSimulationRunOutputPresignedURL(ctx, deps.S3Client, *simulationRun.OutputFileKey)
	if err != nil {
		return nil, common.NewHttpErrorf(http.StatusInternalServerError, "failed to generate presigned URL: %v", err)
	}

	return &components.SimulationOutputDownloadUrlResponse{
		Url: presignedURL,
	}, nil
}
