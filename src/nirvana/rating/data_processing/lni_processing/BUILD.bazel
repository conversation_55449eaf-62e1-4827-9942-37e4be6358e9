load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "lni_processing",
    srcs = [
        "get_bipd_continuous_coverage_years_v1.go",
        "get_bipd_unique_carriers_since_v1.go",
        "get_years_in_business_from_authority_history_v1.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/data_processing/lni_processing",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/rating/data_fetching/lni_fetching",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
