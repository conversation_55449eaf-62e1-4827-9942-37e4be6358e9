//this file houses all the lookup tables that are required for establishing rate factors

lookup_table IsoLCM {
    inputs {
        dummyKey range[number.integer]
    }
    outputs {
        lcmLiab number.decimal
        lcmColl number.decimal
        lcmComp number.decimal
        lcmLiabZone number.decimal
        lcmCollZone number.decimal
        lcmCompZone number.decimal
    }
}

lookup_table ExpModElrs {
    inputs {
        dummyKey range[number.integer]
    }
    outputs {
        elrLiab number.decimal
        elrColl number.decimal
        elrComp number.decimal
    }
}

lookup_table IsoBaseRate {
    inputs {
        isoTerritory IsoTerritoryEnum
    }
    outputs {
        baseRateLiab number.decimal
        baseRateColl number.decimal
        baseRateComp number.decimal
    }
}

lookup_table TerritoryAdjFactor {
    inputs {
        isoTerritory IsoTerritoryEnum
    }
    outputs {
        territoryAdjFactorLiab number.decimal
        territoryAdjFactorCollComp number.decimal
    }
}

lookup_table TerritoryAdjFactorLongHaul {
    inputs {
        vehicleEndZone IsoZoneEnum
    }
    outputs {
        territoryAdjFactorLongHaulLiab number.decimal
        territoryAdjFactorLongHaulCollComp number.decimal
    }
}

lookup_table LongHaulIsoBaseRate {
    inputs {
        vehicleStartZone IsoZoneEnum
        vehicleEndZone IsoZoneEnum
    }
    outputs {
        longHaulBaseRateLiab number.decimal
        longHaulBaseRateColl number.decimal
        longHaulBaseRateComp number.decimal
    }
}

lookup_table IsoPrimaryFactor {
    inputs {
        isoWeightGroup IsoWeightGroupEnum
        vehType VehTypeEnum
        isoRadiusGroup IsoRadiusGroupEnum
        isoOperationType IsoOperationTypeEnum
    }
    outputs {
        isoPrimaryFactorLiab number.decimal
        isoPrimaryFactorColl number.decimal
        isoPrimaryFactorComp number.decimal
        isoPrimaryFactorMedPay number.decimal
    }
}

lookup_table IsoSecondaryFactor {
    inputs {
        vehType VehTypeEnum
        commodityGroup CommodityGroupEnum
        commodityDetail CommodityDetailEnum
    }
    outputs {
        isoSecondaryFactorLiab number.decimal	
        isoSecondaryFactorColl number.decimal
        isoSecondaryFactorComp number.decimal
    }
}

lookup_table FleetSizeFactor {
    inputs {
        fleetPowerUnitCount range[number.integer]
        vehType VehTypeEnum
        isoWeightGroup IsoWeightGroupEnum
    }
    outputs {
        fleetSizeFactorLiab number.decimal
        fleetSizeFactorColl number.decimal
        fleetSizeFactorComp number.decimal
    }
}

lookup_table OriginalCostNewFactorLiab {
    inputs {
        costNew range[number.decimal]
        vehType VehTypeEnum
        isoWeightGroup IsoWeightGroupEnum
    }
    outputs {
        value number.decimal
    }
}

lookup_table StatedValueFactorLiab {
    inputs {
        statedValue range[number.decimal]
        vehType VehTypeEnum
        isoWeightGroup IsoWeightGroupEnum
    }
    outputs {
        value number.decimal
    }
}

lookup_table OriginalCostNewFactorCollComp {
    inputs {
        costNew range[number.decimal]
        vehType VehTypeEnum
        vehicleAge range[number.integer]
    }
    outputs {
        originalCostNewFactorColl number.decimal
        originalCostNewFactorComp number.decimal
    }
}

lookup_table LongHaulOriginalCostNewFactorCollComp {
    inputs {
        costNew range[number.decimal]
    }
    outputs {
        longHaulOriginalCostNewFactorColl number.decimal
        longHaulOriginalCostNewFactorComp number.decimal
    }
}

lookup_table StatedValueFactorCollComp {
    inputs {
        statedValue range[number.decimal]
        vehType VehTypeEnum
    }
    outputs {
        statedValueFactorColl number.decimal
        statedValueFactorComp number.decimal
    }
}

lookup_table LongHaulStatedValueFactorCollComp {
    inputs {
        statedValue range[number.decimal]
    }
    outputs {
        longHaulStatedValueFactorColl number.decimal
        longHaulStatedValueFactorComp number.decimal
    }
}

// TODO: define this
lookup_table VehAgeFactorLiab {
   inputs {
       vehicleAge range[number.integer]
   }
   outputs {
       value number.decimal
   }
}

lookup_table LongHaulVehicleAgeFactorCollComp {
    inputs {
        vehicleAge range[number.integer]
    }
    outputs {
        longHaulVehicleAgeFactorColl number.decimal
        longHaulVehicleAgeFactorComp number.decimal
    }
}

lookup_table NaicFactor {
    inputs {
        naic NaicEnum
        vehType VehTypeEnum
    }
    outputs {
        naicFactorLiab number.decimal
        naicFactorColl number.decimal
        naicFactorComp number.decimal
    }
}

lookup_table IncreasedLimitFactor {
    inputs {
        liabilityCSL LiabCslEnum
        vehType VehTypeEnum
        isoRadiusGroup IsoRadiusGroupEnum
        isoWeightGroup IsoWeightGroupEnum
    }
    outputs {
        value number.decimal
    }
}

lookup_table LiabDeductibleFactor {
    inputs {
        liabilityDeductible LiabDeductibleEnum
    }
    outputs {
        liabDeductibleFactor number.decimal
        longHaulLiabDeductibleFactor number.decimal
    }
}

lookup_table CollDeductibleFactor {
    inputs {
        collDeductible CollDeductibleEnum
        vehType VehTypeEnum
    }
    outputs {
        collDeductibleFactor number.decimal
        longHaulCollDeductibleFactor number.decimal
    }
}

lookup_table CompDeductibleFactor {
    inputs {
        compDeductible CompDeductibleEnum
        vehType VehTypeEnum
    }
    outputs {
        compDeductibleFactor number.decimal
        longHaulCompDeductibleFactor number.decimal
    }
}

lookup_table OptionalCoverageRates {
    inputs {
        dummyKey range[number.integer]
    }
    outputs {
        specifiedAdditionalInsuredRate number.decimal
        specifiedWaiverOfSubrogationRate number.decimal
        broadenedPollutionRate number.decimal
        trailerInterchangeLongHaulRate number.decimal //keep in for now, not changing gsheet but not being used
        trailerInterchangeIntermediateRate number.decimal //keep in for now, not changing gsheet but not being used
        trailerInterchangeLocalRate number.decimal //keep in for now, not changing gsheet but not being used
        blanketAdditionalInsuredRate number.decimal
        blanketWaiverOfSubrogationRate number.decimal
        standardPdPackageRate number.decimal
        completePdPackageRate number.decimal
        glRate number.decimal
        glMisdeliveryLiquidsRate number.decimal
        glContractualRate number.decimal
        glStopGapRate number.decimal
        glBlanketAdditionalInsuredRate number.decimal
        glBlanketWaiverOfSubrogationRate number.decimal
        glSpecifiedAdditionalInsuredRate number.decimal
        glSpecifiedWaiverOfSubrogationRate number.decimal
    }
}
