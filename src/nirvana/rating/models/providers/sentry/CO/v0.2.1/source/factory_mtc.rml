//----- Motor Cargo Insurance (aka MTC)
//----- Enum reference section

tag_enum MtcDeductibleEnum


//-----------Lookup table reference section

lookup_table BaseRateMTC {
    inputs {
        mtcLimit range[number.integer]
        mtcDeductible MtcDeductibleEnum
        combineDeductiblePhysMTC boolean
    }
    outputs {
        commodityClassA number.decimal
        commodityClassB number.decimal
        commodityClassC number.decimal
        commodityClassD number.decimal
        commodityClassE number.decimal
        commodityClassF number.decimal
    }
}

lookup_table TierMTC {
    inputs {
        tierScoreMTC range[number.decimal]
    }
    outputs {
        tierGroupMTC TierLiabEnum
        tierFactorMTC number.decimal
    }
}

lookup_table DriverTurnoverRateMTC {
    inputs {
        driverTurnover range[number.decimal]
        fleetPowerUnitCount range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgDriverAttractMTC {
    inputs {
        avgDriverAttractScore range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table FmcsaYearsMTC {
    inputs {
        fmcsaYears range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table CargoClaimFrqMTC {
    inputs {
        cargoClaimFreqMTC range[number.decimal]
        fleetPowerUnitCount range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table McsTierMTC {
    inputs {
        mcScore range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table MaintenanceVioMTC {
    inputs {
        fmcsaMaintenanceVioRatio range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgAgePowerUnitsMTC {
    inputs {
        avgAgePowerUnits range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgMovingViosMvrMTC {
    inputs {
        avgDriverVios range[number.decimal]
        driverCountTotalLastYear range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table FactorReeferBreakdownMTC {
    inputs {
        dummyKey range[number.integer]
    }
    outputs {
        factorReeferBreakdown number.decimal
        factorReeferBreakdownWithHumanError number.decimal
    }
}

//----- Derived Data

property LossHistory histClaimCountMTC number.integer {
    sum(yearlyLossSummaries, [YearlyLossSummary.claimCountMTC])
}

property LossHistory priorClaimFreqMTC number.decimal {
    number.decimal{histClaimCountMTC} / number.decimal{histPowerUnitCount}
}

property Vehicle vehicleAgeOnlyForPU number.integer {
    (isPowerUnit)*(company->quoteData->coverage->effectiveDateYear - yearMade)
}

property Company averageAgePowerUnit number.decimal {
    number.decimal{sum(vehicles, [Vehicle.vehicleAgeOnlyForPU])} / number.decimal{fleetPowerUnitCount}
}

output RaterMTC coverageMTCLimit number.decimal {
    number.decimal{quoteData->coverage->mtcLimit
    }
}

output RaterMTC companyPUFleetCount number.decimal {
    number.decimal{quoteData->company->fleetPowerUnitCount
    }
}

output RaterMTC scheduleModMTC number.decimal {
    number.decimal{quoteData->underwriting->scheduleModMTC
    }
}

output RaterMTC characteristicCompanyGarageState GeoStateAbbrEnum {
    quoteData->company->companyGarageState
}

output RaterMTC characteristicMTCDeductible MtcDeductibleEnum {
    quoteData->coverage->mtcDeductible
}

output RaterMTC characteristicCombineDeductiblePhysMTC boolean {
    quoteData->coverage->combineDeductiblePhysMTC
}

output RaterMTC characteristicHasReeferBreakdownWithHumanError boolean {
    quoteData->coverage->hasReeferBreakdownWithHumanError
}

//----- Premium Calculation Section

property Coverage coverageModPremiumMTC number.decimal {
    quoteData->raterMTC->totalModPremiumMTC
}

property RaterMTC totalModPremiumMTC number.decimal {
    totalManualPremiumMTC
    * scheduleModMTC
}

property RaterMTC totalManualPremiumMTC number.decimal {
    (number.decimal{coverageMTCLimit} * number.decimal{companyPUFleetCount} / 100.0) *
    ((commodityClassAFactor * commodityClassAPercent / 100.0) +
     (commodityClassBFactor * commodityClassBPercent / 100.0) +
     (commodityClassCFactor * commodityClassCPercent / 100.0) +
     (commodityClassDFactor * commodityClassDPercent / 100.0) +
     (commodityClassEFactor * commodityClassEPercent / 100.0) +
     (commodityClassFFactor * commodityClassFPercent / 100.0)
    )
}

property RaterMTC commodityClassAFactor number.decimal {
    lookup([BaseRateMTC], [BaseRateMTC.commodityClassA],
        quoteData->coverage->mtcLimit,
        quoteData->coverage->mtcDeductible,
        quoteData->coverage->combineDeductiblePhysMTC
    )
}

property RaterMTC commodityClassAPercent number.decimal {
    quoteData->company->percentCommodityClassA
}

property RaterMTC commodityClassBFactor number.decimal {
    lookup([BaseRateMTC], [BaseRateMTC.commodityClassB],
        quoteData->coverage->mtcLimit,
        quoteData->coverage->mtcDeductible,
        quoteData->coverage->combineDeductiblePhysMTC
    )
}

property RaterMTC commodityClassBPercent number.decimal {
    quoteData->company->percentCommodityClassB
}

property RaterMTC commodityClassCFactor number.decimal {
    lookup([BaseRateMTC], [BaseRateMTC.commodityClassC],
        quoteData->coverage->mtcLimit,
        quoteData->coverage->mtcDeductible,
        quoteData->coverage->combineDeductiblePhysMTC
    )
}

property RaterMTC commodityClassCPercent number.decimal {
    quoteData->company->percentCommodityClassC
}

property RaterMTC commodityClassDFactor number.decimal {
    lookup([BaseRateMTC], [BaseRateMTC.commodityClassD],
        quoteData->coverage->mtcLimit,
        quoteData->coverage->mtcDeductible,
        quoteData->coverage->combineDeductiblePhysMTC
    )
}

property RaterMTC commodityClassDPercent number.decimal {
    quoteData->company->percentCommodityClassD
}

property RaterMTC commodityClassEFactor number.decimal {
    lookup([BaseRateMTC], [BaseRateMTC.commodityClassE],
        quoteData->coverage->mtcLimit,
        quoteData->coverage->mtcDeductible,
        quoteData->coverage->combineDeductiblePhysMTC
    )
}

property RaterMTC commodityClassEPercent number.decimal {
    quoteData->company->percentCommodityClassE
}

property RaterMTC commodityClassFFactor number.decimal {
    lookup([BaseRateMTC], [BaseRateMTC.commodityClassF],
        quoteData->coverage->mtcLimit,
        quoteData->coverage->mtcDeductible,
        quoteData->coverage->combineDeductiblePhysMTC
    )
}

property RaterMTC commodityClassFPercent number.decimal {
    quoteData->company->percentCommodityClassF
}

property Company tierFactorMTC number.decimal {
    lookup([TierMTC], [TierMTC.tierFactorMTC],
        tierScoreMTC
    )
}

property Company tierGroupMTC TierLiabEnum {
    lookup([TierMTC], [TierMTC.tierGroupMTC],
        tierScoreMTC
    )
}

property Coverage factorReeferBreakdown number.decimal {
    lookup([FactorReeferBreakdownMTC], [FactorReeferBreakdownMTC.factorReeferBreakdown],
        1
        )
}

property Coverage factorReeferBreakdownWithHumanError number.decimal {
    lookup([FactorReeferBreakdownMTC], [FactorReeferBreakdownMTC.factorReeferBreakdownWithHumanError],
        1
        )
}

property Company tierScoreMTC number.decimal {
    driverTurnoverRateMTCFactor *
    avgDriverAttractMTCFactor *
    fmcsaYearsMTCFactor *
    CargoClaimFrqMTCFactor *
    mcsTierMTCFactor *
    maintenanceVioMTCFactor *
    avgAgePowerUnitsMTCFactor *
    avgMovingViosMvrMTCFactor
}

property Company driverTurnoverRateMTCFactor number.decimal {
    lookup([DriverTurnoverRateMTC], [DriverTurnoverRateMTC.value],
        driverTurnover,
        number.decimal{fleetPowerUnitCount}
    )
}

property Company avgDriverAttractMTCFactor number.decimal {
    lookup([AvgDriverAttractMTC], [AvgDriverAttractMTC.value],
        avgDriverAttractScore
    )
}

property Company fmcsaYearsMTCFactor number.decimal {
    lookup([FmcsaYearsMTC], [FmcsaYearsMTC.value],
        number.decimal{fmcsaYears}
    )
}

property Company CargoClaimFrqMTCFactor number.decimal {
    lookup([CargoClaimFrqMTC], [CargoClaimFrqMTC.value],
        quoteData->lossHistory->priorClaimFreqMTC,
        number.decimal{fleetPowerUnitCount}
    )
}

property Company mcsTierMTCFactor number.decimal {
    lookup([McsTierMTC], [McsTierMTC.value],
        mcScore
    )
}

property Company maintenanceVioMTCFactor number.decimal {
    lookup([MaintenanceVioMTC],[MaintenanceVioMTC.value],
        fmcsaMaintenanceVioRatio
    )
}

property Company avgAgePowerUnitsMTCFactor number.decimal {
    lookup([AvgAgePowerUnitsMTC],[AvgAgePowerUnitsMTC.value],
        averageAgePowerUnit
    )
}

property Company avgMovingViosMvrMTCFactor number.decimal {
    lookup([AvgMovingViosMvrMTC], [AvgMovingViosMvrMTC.value],
        avgDriverVios,
        number.decimal{driverCountTotalLastYear}
    )
}

//----- MTC Package Calculations

property RaterMTC totalPackageBasicMTC number.decimal {
    0.0
}

property RaterMTC totalPackageStandardMTC number.decimal {
    0.0
}

property RaterMTC totalPackageCompleteMTC number.decimal {
    // Human Error, Temperature Change And Loss Due To FDA Regulation Coverage
    // The factor will be the average Travelers' and Canal (7.5% and 30% respectively)
    // Tier should not apply, but schedule mod should apply
    switch(quoteData->coverage->hasReeferBreakdownWithHumanError) {
        case True:
            quoteData->coverage->factorReeferBreakdownWithHumanError *
            totalManualPremiumMTC *
            quoteData->underwriting->scheduleModMTC
        case False:
            quoteData->coverage->factorReeferBreakdown *
            totalManualPremiumMTC *
            quoteData->underwriting->scheduleModMTC
    }
}

property Coverage basicMTCPackageRate number.decimal {
    quoteData->raterMTC->totalPackageBasicMTC
}

property Coverage standardMTCPackageRate number.decimal {
    quoteData->raterMTC->totalPackageStandardMTC
}

property Coverage completeMTCPackageRate number.decimal {
    quoteData->raterMTC->totalPackageCompleteMTC
}

output RaterMTC totalPolicyPremiumBasicMTC number.decimal {
    quoteData->outputsQuote->mtcBasicPolicyPremium
}

output RaterMTC totalPolicyPremiumStandardMTC number.decimal {
    quoteData->outputsQuote->mtcStandardPolicyPremium
}

output RaterMTC totalPolicyPremiumCompleteMTC number.decimal {
    quoteData->outputsQuote->mtcCompletePolicyPremium
}
