//this file houses all the lookup tables that are required for establishing derived data groups

lookup_table IsoRadiusGroup {
    inputs {
        radius range[number.decimal]
    }
    outputs {
        isoRadiusGroup IsoRadiusGroupEnum
    }
}

lookup_table PowerUnitFlag {
    inputs {
        vehType VehTypeEnum
    }
    outputs {
        value number.integer
    }
}

lookup_table IsoTerritory {
    inputs {
        zipCode GeoZipEnum
    }
    outputs {
        value IsoTerritoryEnum
    }
}
