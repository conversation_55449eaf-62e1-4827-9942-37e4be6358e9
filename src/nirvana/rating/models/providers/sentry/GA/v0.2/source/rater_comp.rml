//---- Comprehensive

//---- Premiums

output Coverage coverageModPremiumComp number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehModPremiumComp])
}

output Coverage coverageManualPremiumComp number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumComp])
}

property Vehicle vehModPremiumComp number.decimal {
    raterCOMP->totalVehPremiumCOMP
}

property Vehicle vehPremiumComp number.decimal {
    raterCOMP->compVehPremium
}

property Coverage coveragePremiumCompBasicLimitComp number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumCompBasicLimitComp])
}

property Coverage avgPuPremiumCompBasicLimitComp number.decimal {
    coveragePremiumCompBasicLimitComp / number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle vehPremiumCompBasicLimitComp number.decimal {
    raterCOMP->compVehPremium * elrComp
}

//---- Rater Calculation

property RaterCOMP totalVehPremiumCOMP number.decimal {
    compVehPremium *
    totalScheduleModCOMP *
    totalExpModCOMP
}

property RaterCOMP totalScheduleModCOMP number.decimal {
    vehicle->company->quoteData->underwriting->scheduleModComp
}

property RaterCOMP totalExpModCOMP number.decimal {
    vehicle->company->quoteData->underwriting->expModComp
}

property RaterCOMP compVehPremium number.decimal {
    compBaseRate *
    compLCM *
    compISOPrimaryFactor *
    compISOSecondaryFactor *
    compFleetSizeFactor *
    number_max((compFactor1_StatedValue - compFactor2_Deductible), 0.1) *
    compNAICFactor *
    compTierFactor *
    compVehicleAgeFactor
}

property RaterCOMP compBaseRate number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->longHaulBaseRateComp
            case False:
                vehicle->baseRateComp
        }
    }
}

property RaterCOMP compLCM number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->lcmCompZone
            case False:
                vehicle->lcmComp
        }
    }
}

property RaterCOMP compISOPrimaryFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->isoPrimaryFactorComp
            case False:
                vehicle->isoPrimaryFactorComp
        }
    }
}

property RaterCOMP compISOSecondaryFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                1.0
            case False:
                vehicle->isoSecondaryFactorComp
        }
    }
}

property RaterCOMP compFleetSizeFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                1.0
            case False:
                vehicle->fleetSizeFactorComp
        }
    }
}

property RaterCOMP compFactor1_StatedValue number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->longHaulStatedValueFactorComp
                case False:
                    vehicle->longHaulOriginalCostNewFactorComp
                            }
            case False:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->statedValueFactorComp
                case False:
                    vehicle->originalCostNewFactorComp
            }
        }
    }
}

property RaterCOMP compFactor2_Deductible number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->longHaulCompDeductibleFactor
                case False:
                    vehicle->longHaulCompDeductibleFactor
                            }
            case False:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->compDeductibleFactor
                case False:
                    vehicle->compDeductibleFactor
            }
        }
    }
}

property RaterCOMP compNAICFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                1.0
            case False:
                vehicle->naicFactorComp
        }
    }
}

property RaterCOMP compTierFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->company->tierFactorComp
            case False:
                vehicle->company->tierFactorComp
        }
    }
}

property RaterCOMP compVehicleAgeFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                    case True:
                        1.0
                    case False:
                        vehicle->longHaulVehicleAgeFactorComp
                }
            case False:
                1.0
        }
    }
}

//---- Rater Characteristics

output RaterCOMP characteristicBasedOnHasApdCoverage boolean {
    vehicle->hasApdCoverage
}

output RaterCOMP characteristicBasedOnUseZoneRating boolean {
    vehicle->useZoneRating
}

output RaterCOMP characteristicVehicleZoneStart IsoZoneEnum {
    vehicle->vehicleStartZone
}

output RaterCOMP characteristicVehicleZoneEnd IsoZoneEnum {
    vehicle->vehicleEndZone
}

output RaterCOMP characteristicStatedValue number.decimal {
    vehicle->statedValue
}

output RaterCOMP characteristicVehicleGarageIsoTerritory IsoTerritoryEnum {
    lookup([IsoTerritory], [IsoTerritory.value],
        vehicle->garageZip
        )
}

output RaterCOMP characteristicDeductibleComp CompDeductibleEnum{
    vehicle->company->quoteData->coverage->compDeductible
}

output RaterCOMP characteristicIsoPrimary_isoWeightGroup IsoWeightGroupEnum {
    vehicle->isoWeightGroup
}

output RaterCOMP characteristicIsoPrimary_vehType VehTypeEnum {
    vehicle->vehType
}

output RaterCOMP characteristicIsoPrimary_vehicleGpsIsoRadiusGroup IsoRadiusGroupEnum {
    vehicle->vehicleGpsIsoRadiusGroup
}

output RaterCOMP characteristicIsoPrimary_isoOperationType IsoOperationTypeEnum {
    vehicle->company->isoOperationType
}

output RaterCOMP characteristicIsoSecondary_commodityGroup CommodityGroupEnum {
    vehicle->company->commodityGroup
}

output RaterCOMP characteristicIsoSecondary_commodityDetail CommodityDetailEnum {
    vehicle->company->commodityDetail
}

//---- Vehicle Data For Rater

property Vehicle longHaulBaseRateComp number.decimal {
    lookup([LongHaulIsoBaseRate], [LongHaulIsoBaseRate.longHaulBaseRateComp],
        vehicleStartZone,
        vehicleEndZone
    )
}

property Vehicle baseRateComp number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateComp],
        vehicleGarageIsoTerritory
    )
}

property Vehicle lcmComp number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmComp],
        1
    )
}

property Vehicle lcmCompZone number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmCompZone],
        1
    )
}

property Vehicle isoPrimaryFactorComp number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorComp],
        isoWeightGroup,
        vehType,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle isoSecondaryFactorComp number.decimal {
    lookup ([IsoSecondaryFactor], [IsoSecondaryFactor.isoSecondaryFactorComp],
        vehType,
        company->commodityGroup,
        company->commodityDetail
    )
}

property Vehicle fleetSizeFactorComp number.decimal {
    lookup([FleetSizeFactor], [FleetSizeFactor.fleetSizeFactorComp],
        company->fleetPowerUnitCount,
        vehType,
        isoWeightGroup        
    )
}

property Vehicle longHaulStatedValueFactorComp number.decimal {
    lookup([LongHaulStatedValueFactorCollComp], [LongHaulStatedValueFactorCollComp.longHaulStatedValueFactorComp],
        statedValue
    )
}

property Vehicle longHaulOriginalCostNewFactorComp number.decimal {
    lookup([LongHaulOriginalCostNewFactorCollComp], [LongHaulOriginalCostNewFactorCollComp.longHaulOriginalCostNewFactorComp],
        costNew
    )
}

property Vehicle statedValueFactorComp number.decimal {
    lookup([StatedValueFactorCollComp], [StatedValueFactorCollComp.statedValueFactorComp],
        statedValue,
        vehType
    )
}

property Vehicle originalCostNewFactorComp number.decimal {
    lookup([OriginalCostNewFactorCollComp], [OriginalCostNewFactorCollComp.originalCostNewFactorComp],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle naicFactorComp number.decimal {
    lookup([NaicFactor], [NaicFactor.naicFactorComp],
        company->naic,
        vehType
    )
}

property Vehicle compDeductibleFactor number.decimal {
    lookup([CompDeductibleFactor], [CompDeductibleFactor.compDeductibleFactor],
        company->quoteData->coverage->compDeductible,
        vehType
    )
}

property Vehicle longHaulCompDeductibleFactor number.decimal {
    lookup([CompDeductibleFactor], [CompDeductibleFactor.longHaulCompDeductibleFactor],
        company->quoteData->coverage->compDeductible,
        vehType
    )
}

property Vehicle longHaulVehicleAgeFactorComp number.decimal {
    lookup([LongHaulVehicleAgeFactorCollComp], [LongHaulVehicleAgeFactorCollComp.longHaulVehicleAgeFactorComp],
        vehicleAge
    )
}

//---- expected loss ratio (elr) aka the target loss ratio, or permissible loss ratio
property Vehicle elrComp number.decimal {
    lookup([ExpModElrs], [ExpModElrs.elrComp],
        1
    )
}
