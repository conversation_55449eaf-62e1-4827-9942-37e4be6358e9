//This section defines the Tier calculation
//It consumes the MC Score along with other variables to arrive at a single rate factor

//-----------Calculation section

property Company tierFactorLiab number.decimal {
    lookup([TierLiab], [TierLiab.tierFactorLiab],
        tierScoreLiab
    )
}

property Company tierFactorColl number.decimal {
    lookup([TierCollComp], [TierCollComp.tierFactorCollComp],
        tierScoreCollComp
    )
}

property Company tierFactorComp number.decimal {
    lookup([TierCollComp], [TierCollComp.tierFactorCollComp],
        tierScoreCollComp
    )
}

property Company tierGroupLiab TierLiabEnum {
    lookup([TierLiab], [TierLiab.tierGroupLiab],
        tierScoreLiab
    )
}

property Company tierGroupColl TierCollCompEnum {
    lookup([TierCollComp], [TierCollComp.tierGroupCollComp],
        tierScoreCollComp
    )
}

property Company tierGroupComp TierCollCompEnum {
    lookup([TierCollComp], [TierCollComp.tierGroupCollComp],
        tierScoreCollComp
    )
}

property Company tierScoreLiab number.decimal {
    driverTurnoverLiabTierFactor *
    avgDriverAttractLiabTierFactor *
    mcsLiabTierFactor *
    priorClaimFreqLiabTierFactor *
    avgMovingViosMvrLiabTierFactor
}

property Company driverTurnoverLiabTierFactor number.decimal {
    lookup([DriverTurnoverLiabTierFactor], [DriverTurnoverLiabTierFactor.value],
        driverTurnover
    )
}

property Company avgDriverAttractLiabTierFactor number.decimal {
    lookup([AvgDriverAttractLiabTierFactor], [AvgDriverAttractLiabTierFactor.value],
        avgDriverAttractScore
    )
}

property Company mcsLiabTierFactor number.decimal {
    lookup([McsLiabTierFactor], [McsLiabTierFactor.value],
        mcScore
    )
}

property Company priorClaimFreqLiabTierFactor number.decimal {
    lookup([PriorClaimFreqLiabTierFactor], [PriorClaimFreqLiabTierFactor.value],
        quoteData->lossHistory->credibilityWeightedPriorClaimFreqLiab
    )
}

property LossHistory credibilityWeightedPriorClaimFreqLiab number.decimal {
    ( (priorClaimFreqLiab) * credibilityPriorFreqLiab + 0.1201 * (1.0 - credibilityPriorFreqLiab) )
}

property LossHistory credibilityPriorFreqLiab number.decimal {
    ( number.decimal{histPowerUnitCount} * 0.1201 * 0.3494 ) / ( 1.0 + ( number.decimal{histPowerUnitCount} * 0.1201 * 0.3494 ) )
}

property Company avgMovingViosMvrLiabTierFactor number.decimal {
    lookup([AvgMovingViosMvrLiabTierFactor], [AvgMovingViosMvrLiabTierFactor.value],
        avgDriverVios,
        driverCountTotalLastYear
    )
}

property Company tierScoreCollComp number.decimal {
    powerUnitCollCompTierFactor *
    maintenanceVioCollCompTierFactor *
    mcsCollCompTierFactor *
    priorClaimFreqCollCompFactor *
    fmcsaYearsCollCompTierFactor *
    vehInspectionRatioCollCompTierFactor *
    truckingCollCompTierFactor *
    avgDriverAttractCollCompTierFactor *
    avgMovingViosMvrCollCompTierFactor *
    driverTurnoverCollCompTierFactor
}

property Company powerUnitCollCompTierFactor number.decimal {
    lookup([PowerUnitCollCompTierFactor],[PowerUnitCollCompTierFactor.value],
        fleetPowerUnitCount
    )
}

property Company maintenanceVioCollCompTierFactor number.decimal {
    lookup([MaintenanceVioCollCompTierFactor],[MaintenanceVioCollCompTierFactor.value],
        fmcsaMaintenanceVioRatio        
    )
}

property Company mcsCollCompTierFactor number.decimal {
    lookup([McsCollCompTierFactor],[McsCollCompTierFactor.value],
        mcScore
    )
}

property Company priorClaimFreqCollCompFactor number.decimal {
    lookup([PriorClaimFreqCollCompTierFactor],[PriorClaimFreqCollCompTierFactor.value],
        quoteData->lossHistory->credibilityWeightedPriorClaimFreqCollComp
    )
}

property LossHistory credibilityWeightedPriorClaimFreqCollComp number.decimal {
    ( (priorClaimFreqCollComp) * credibilityPriorFreqCollComp + 0.04264 * (1.0 - credibilityPriorFreqCollComp) )
}

//2.0 in front of histPowerUnitCount helps to approximate vehicle count
property LossHistory credibilityPriorFreqCollComp number.decimal {
    ( 2.0 * number.decimal{histPowerUnitCount} * 0.04264 * 1.1713 ) / ( 1.0 + ( 2.0 * number.decimal{histPowerUnitCount} * 0.04264 * 1.1713 ) )
}

property Company fmcsaYearsCollCompTierFactor number.decimal {
    lookup([FmcsaYearsCollCompTierFactor],[FmcsaYearsCollCompTierFactor.value],
        fmcsaYears
    )
}

property Company vehInspectionRatioCollCompTierFactor number.decimal {
    lookup([VehInspectionRatioCollCompTierFactor],[VehInspectionRatioCollCompTierFactor.value],
        fmcsaVehInspectionRatio
    )
}

property Company truckingCollCompTierFactor number.decimal {
    lookup([TruckingCollCompTierFactor],[TruckingCollCompTierFactor.value],
        truckingNonTrucking
    )
}

property Company avgDriverAttractCollCompTierFactor number.decimal {
    lookup([AvgDriverAttractCollCompTierFactor],[AvgDriverAttractCollCompTierFactor.value],
        avgDriverAttractScore
    )
}

property Company avgMovingViosMvrCollCompTierFactor number.decimal {
    lookup([AvgMovingViosMvrCollCompTierFactor],[AvgMovingViosMvrCollCompTierFactor.value],
        avgDriverVios,
        driverCountTotalLastYear
    )
}

property Company driverTurnoverCollCompTierFactor number.decimal {
    lookup([DriverTurnoverCollCompTierFactor],[DriverTurnoverCollCompTierFactor.value],
        driverTurnover,
        fleetPowerUnitCount
    )
}

property Driver validAttractScore number.decimal{
    attractScore * number.decimal{hasValidAttractScore}
}

property Driver hasValidAttractScore number.integer {
    lookup([HasValidAttractScore], [HasValidAttractScore.hasValidAttractScore],
        attractScore
    )
}

property Company sumAttractScore number.decimal {
    sum(drivers, [Driver.validAttractScore])
}

property Company countAttractScore number.integer {
    sum(drivers, [Driver.hasValidAttractScore])
}

property Company pctAttractScore number.decimal {
    number.decimal{countAttractScore} / number.decimal{driverCountTotalLastYear}
}

property Company useAttractScore boolean {
    lookup([UseAttractScore], [UseAttractScore.useAttractScore],
        pctAttractScore
    )
}

property Company avgDriverAttractScore number.decimal {
    switch(useAttractScore) {
        case True:
            sumAttractScore / number.decimal{countAttractScore}
        case False:
            0.0 - 1.0
    }
}

//-----------Enum reference section

tag_enum TierLiabEnum

tag_enum TierCollCompEnum

tag_enum TruckingNonTruckingEnum

//-----------Lookup table reference section

lookup_table TierLiab {
    inputs {
        tierScoreLiab range[number.decimal]
    }
    outputs {
        tierGroupLiab TierLiabEnum
        tierFactorLiab number.decimal
    }
}

lookup_table DriverTurnoverLiabTierFactor {
    inputs {
        driverTurnover range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgDriverAttractLiabTierFactor {
    inputs {
        avgDriverAttractScore range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table McsLiabTierFactor {
    inputs {
        mcScore range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table PriorClaimFreqLiabTierFactor {
    inputs {
        priorClaimFreqLiab range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgMovingViosMvrLiabTierFactor {
    inputs {
        avgDriverVios range[number.decimal]
        driverCountTotalLastYear range[number.integer]
    }
    outputs {
        value number.decimal
    }
}

lookup_table TierCollComp {
    inputs {
        tierScoreCollComp range[number.decimal]
    }
    outputs {
        tierGroupCollComp TierCollCompEnum
        tierFactorCollComp number.decimal
    }
}

lookup_table PowerUnitCollCompTierFactor {
    inputs {
        fleetPowerUnitCount range[number.integer]
    }
    outputs {
        value number.decimal
    }
}

lookup_table MaintenanceVioCollCompTierFactor {
    inputs {
        fmcsaMaintenanceVioRatio range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table McsCollCompTierFactor {
    inputs {
        mcScore range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table PriorClaimFreqCollCompTierFactor {
    inputs {
        priorClaimFreqCollComp range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table FmcsaYearsCollCompTierFactor {
    inputs {
        fmcsaYears range[number.integer]
    }
    outputs {
        value number.decimal
    }
}

lookup_table VehInspectionRatioCollCompTierFactor {
    inputs {
        fmcsaVehInspectionRatio range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table TruckingCollCompTierFactor {
    inputs {
        truckingNonTrucking TruckingNonTruckingEnum
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgDriverAttractCollCompTierFactor {
    inputs {
        avgDriverAttractScore range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgMovingViosMvrCollCompTierFactor {
    inputs {
        avgDriverVios range[number.decimal]
        driverCountTotalLastYear range[number.integer]
    }
    outputs {
        value number.decimal
    }
}

lookup_table DriverTurnoverCollCompTierFactor {
    inputs {
        driverTurnover range[number.decimal]
        fleetPowerUnitCount range[number.integer]
    }
    outputs {
        value number.decimal
    }
}

lookup_table HasValidAttractScore {
    inputs {
        attractScore range[number.decimal]
    }
    outputs {
        hasValidAttractScore number.integer
    }
}

lookup_table UseAttractScore {
    inputs {
        pctAttractScore range[number.decimal]
    }
    outputs {
        useAttractScore boolean
    }
}
