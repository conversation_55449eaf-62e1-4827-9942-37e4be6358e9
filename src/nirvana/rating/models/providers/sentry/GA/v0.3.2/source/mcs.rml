//This section defines the calculation of the Motor Carrier Score (aka MCS or mcScore)
//MCS is an input into the Tier calculation

property Company mcScore number.decimal {
    number_min(999.9, 1000.0 - (mcFactor * 100.0) + nBasicAdj)
}

property Company mcFactor number.decimal {
    avgMilesMcsFactor *
    avgGrossWeightMcsFactor *
    fmcsaYearsMcsFactor *
    priorCrashFrequencyMcsFactor *
    vehicleInspectionRatioMcsFactor *
    maintainenceVioRatioMcsFactor *
    unsafeVioRatioMcsFactor *
    inspectionIndicatorMcsFactor *
    largeMachineCargoIndMcsFactor
}

//This section creates each MCS factor for the Company entity

property Company avgMilesMcsFactor number.decimal {
    lookup([AvgMilesMcs], [AvgMilesMcs.value],
        fmcsaAvgMiles
    )
}

property Company avgGrossWeightMcsFactor number.decimal {
    lookup([AvgGrossWeightMcs], [AvgGrossWeightMcs.value],
        fmcsaAvgGrossWeight
    )
}

property Company fmcsaYearsMcsFactor number.decimal {
    lookup([FmcsaYearsMcs], [FmcsaYearsMcs.value],
        number.decimal{fmcsaYears}
    )
}

property Company priorCrashFrequencyMcsFactor number.decimal {
    lookup([PriorCrashFrequencyMcs], [PriorCrashFrequencyMcs.value],
        fmcsaPriorFrequency,
        number.decimal{fmcsaYears}
    )
}

property Company vehicleInspectionRatioMcsFactor number.decimal {
    lookup([VehicleInspectionRatioMcs], [VehicleInspectionRatioMcs.value],
        fmcsaVehInspectionRatio
    )
}

property Company maintainenceVioRatioMcsFactor number.decimal {
    lookup([MaintainenceVioRatioMcs], [MaintainenceVioRatioMcs.value],
        fmcsaMaintenanceVioRatio
    )
}

property Company unsafeVioRatioMcsFactor number.decimal {
    lookup([UnsafeVioRatioMcs], [UnsafeVioRatioMcs.value],
        fmcsaUnsafeVioRatio
    )
}

property Company inspectionIndicatorMcsFactor number.decimal {
    lookup([InspectionIndicatorMcs], [InspectionIndicatorMcs.value],
        fmcsaInspectionIndicator,
        number.decimal{fmcsaPowerUnitCount}
    )
}

property Company largeMachineCargoIndMcsFactor number.decimal {
    lookup([LargeMachineCargoIndMcs], [LargeMachineCargoIndMcs.value],
        fmcsaLargeMachineCargoInd
    )
}

property Company nBasicAdj number.decimal {
    lookup([NBasic], [NBasic.nBasicAdj],
        nBasicGrade
    )
}

//-----------Enum reference section

tag_enum NBasicGradeEnum

//-----------Lookup table reference section

lookup_table AvgMilesMcs {
    inputs {
        fmcsaAvgMiles range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table AvgGrossWeightMcs {
    inputs {
        fmcsaAvgGrossWeight range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table FmcsaYearsMcs {
    inputs {
        fmcsaYears range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table PriorCrashFrequencyMcs {
    inputs {
        fmcsaPriorFrequency range[number.decimal]
        fmcsaYears range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table VehicleInspectionRatioMcs {
    inputs {
        fmcsaVehInspectionRatio range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table MaintainenceVioRatioMcs {
    inputs {
        fmcsaMaintainenceVioRatio range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table UnsafeVioRatioMcs {
    inputs {
        fmcsaUnsafeVioRatio range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table InspectionIndicatorMcs {
    inputs {
        fmcsaInspectionIndicator FmcsaInspectionIndicatorEnum
        fmcsaPowerUnitCount range[number.decimal]
    }
    outputs {
        value number.decimal
    }
}

lookup_table LargeMachineCargoIndMcs {
    inputs {
        fmcsaLargeMachineCargoInd boolean
    }
    outputs {
        value number.decimal
    }
}

lookup_table NBasic {
    inputs {
        nBasicGrade NBasicGradeEnum
    }
    outputs {
        nBasicAdj number.decimal
    }
}
