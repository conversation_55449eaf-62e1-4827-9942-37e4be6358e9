//---- Auto Liability

//---- Premiums

output Coverage coverageModPremiumLiab number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehModPremiumLiab])
}

output Coverage coverageManualPremiumLiab number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumLiab])
}

property Vehicle vehModPremiumLiab number.decimal {
    raterAL->totalVehPremiumLIAB
}

property Vehicle vehPremiumLiab number.decimal {
    raterAL->liabVehPremium
}

property Coverage coveragePremiumLiabBasicLimitLiab number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumLiabBasicLimitLiab])
}

property Coverage avgPuPremiumLiabBasicLimitLiab number.decimal {
    coveragePremiumLiabBasicLimitLiab / number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle vehPremiumLiabBasicLimitLiab number.decimal {
    raterAL->liabVehPremium * elrLiab
    / (raterAL->liabLimitFactorIncreased - raterAL->liabMinusDeductibleFactor)
}

//---- Rater Calculation

property RaterAL totalVehPremiumLIAB number.decimal {
    liabVehPremium *
    totalScheduleModLIAB *
    totalExpModLIAB
}

property RaterAL totalScheduleModLIAB number.decimal {
    vehicle->company->quoteData->underwriting->scheduleModLiab
}

property RaterAL totalExpModLIAB number.decimal {
    vehicle->company->quoteData->underwriting->expModLiab
}

property RaterAL liabVehPremium number.decimal {
    liabBaseRate *
    liabLCM *
    liabISOPrimaryFactor *
    liabISOSecondaryFactor *
    liabFleetSizeFactor *
    liabValueFactor *
    liabNAICFactor *
    liabTierFactor *
    (liabLimitFactorIncreased -
    liabMinusDeductibleFactor) *
    liabTerritoryAdjFactor * 
    liabTimeInBusinessFactor
}

property RaterAL liabBaseRate number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->longHaulBaseRateLiab
        case False:
            vehicle->baseRateLiab
    }
}

property RaterAL liabLCM number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->lcmLiabZone
        case False:
            vehicle->lcmLiab
    }
}

property RaterAL liabISOPrimaryFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->isoPrimaryFactorLiab
        case False:
            vehicle->isoPrimaryFactorLiab
    }
}

property RaterAL liabISOSecondaryFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            1.0
        case False:
            vehicle->isoSecondaryFactorLiab
    }
}

property RaterAL liabFleetSizeFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            1.0
        case False:
            vehicle->fleetSizeFactorLiab
    }
}

property RaterAL liabValueFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            1.0
        case False:
        switch(vehicle->company->quoteData->underwriting->useStatedValueLiab) {
            case True:
                vehicle->statedValueFactorLiab
            case False:
                (vehicle->originalCostNewFactorLiab * vehicle->vehicleAgeFactorLiab)
        }
    }
}

property RaterAL liabNAICFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            1.0
        case False:
            vehicle->naicFactorLiab
    }
}

property RaterAL liabTierFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->company->tierFactorLiab
        case False:
            vehicle->company->tierFactorLiab
    }
}

property RaterAL liabLimitFactorIncreased number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->increasedLimitFactor
        case False:
            vehicle->increasedLimitFactor
    }
}

property RaterAL liabMinusDeductibleFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->longHaulLiabDeductibleFactor
        case False:
            vehicle->liabDeductibleFactor
    }
}

property RaterAL liabTerritoryAdjFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->territoryAdjFactorLongHaulLiab
        case False:
            vehicle->territoryAdjFactorLiab
    }
}

property RaterAL liabTimeInBusinessFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->company->yearsInBusinessFactor
        case False:
            vehicle->company->yearsInBusinessFactor
    }
}

//---- Rater Characteristics

output RaterAL characteristicBasedOnUseZoneRating boolean {
    vehicle->useZoneRating
}

output RaterAL characteristicVehicleZoneStart IsoZoneEnum {
    vehicle->vehicleStartZone
}

output RaterAL characteristicVehicleZoneEnd IsoZoneEnum {
    vehicle->vehicleEndZone
}

output RaterAL characteristicStatedValue number.decimal {
    vehicle->statedValue
}

output RaterAL characteristicDeductibleLiab LiabDeductibleEnum{
    vehicle->company->quoteData->coverage->liabilityDeductible
}

output RaterAL characteristicVehicleGarageIsoTerritory IsoTerritoryEnum {
    lookup([IsoTerritory], [IsoTerritory.value],
        vehicle->garageZip
        )
}

output RaterAL characteristicCompanyPUFleetCount number.decimal {
    number.decimal{vehicle->company->fleetPowerUnitCount
    }
} 

output RaterAL characteristicCompanyGarageState GeoStateAbbrEnum {
    vehicle->company->companyGarageState
}

output RaterAL characteristicIsoPrimary_isoWeightGroup IsoWeightGroupEnum {
    vehicle->isoWeightGroup
}

output RaterAL characteristicIsoPrimary_vehType VehTypeEnum {
    vehicle->vehType
}

output RaterAL characteristicIsoPrimary_vehicleGpsIsoRadiusGroup IsoRadiusGroupEnum {
    vehicle->vehicleGpsIsoRadiusGroup
}

output RaterAL characteristicIsoPrimary_isoOperationType IsoOperationTypeEnum {
    vehicle->company->isoOperationType
}

output RaterAL characteristicIsoSecondary_commodityGroup CommodityGroupEnum {
    vehicle->company->commodityGroup
}

output RaterAL characteristicIsoSecondary_commodityDetail CommodityDetailEnum {
    vehicle->company->commodityDetail
}

output RaterAL characteristicFmcsaYears number.integer {
    vehicle->company->fmcsaYears
}

//---- Vehicle Data For Rater

property Vehicle longHaulBaseRateLiab number.decimal {
    lookup([LongHaulIsoBaseRate], [LongHaulIsoBaseRate.longHaulBaseRateLiab],
        vehicleStartZone,
        vehicleEndZone
    )
}

property Vehicle baseRateLiab number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateLiab],
        vehicleGarageIsoTerritory
    )
}

property Vehicle lcmLiab number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmLiab],
        1
    )
}

property Vehicle lcmLiabZone number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmLiabZone],
        1
    )
}

property Vehicle isoPrimaryFactorLiab number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorLiab],
        isoWeightGroup,
        vehType,
        //isoRadiusGroup,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle isoSecondaryFactorLiab number.decimal {
    lookup ([IsoSecondaryFactor], [IsoSecondaryFactor.isoSecondaryFactorLiab],
        vehType,
        company->commodityGroup,
        company->commodityDetail
    )
}

property Vehicle fleetSizeFactorLiab number.decimal {
    lookup([FleetSizeFactor], [FleetSizeFactor.fleetSizeFactorLiab],
        company->fleetPowerUnitCount,
        vehType,
        isoWeightGroup        
    )
}

property Vehicle statedValueFactorLiab number.decimal {
    lookup([StatedValueFactorLiab], [StatedValueFactorLiab.value],
        statedValue,
        vehType,
        isoWeightGroup
    )
}

property Vehicle originalCostNewFactorLiab number.decimal {
    lookup([OriginalCostNewFactorLiab], [OriginalCostNewFactorLiab.value],
        costNew,
        vehType,
        isoWeightGroup
    )
}

property Vehicle vehicleAgeFactorLiab number.decimal {
    lookup([VehAgeFactorLiab], [VehAgeFactorLiab.value],
       vehicleAge
    )
}

property Vehicle naicFactorLiab number.decimal {
    lookup([NaicFactor], [NaicFactor.naicFactorLiab],
        company->naic,
        vehType
    )
}

property Vehicle increasedLimitFactor number.decimal {
    lookup([IncreasedLimitFactor], [IncreasedLimitFactor.value],
        company->quoteData->coverage->liabilityCSL,
        vehType,
        vehicleGpsIsoRadiusGroup,
        isoWeightGroup
    )
}

property Vehicle liabDeductibleFactor number.decimal {
    lookup([LiabDeductibleFactor], [LiabDeductibleFactor.liabDeductibleFactor],
        company->quoteData->coverage->liabilityDeductible
    )
}

property Vehicle longHaulLiabDeductibleFactor number.decimal {
    lookup([LiabDeductibleFactor], [LiabDeductibleFactor.longHaulLiabDeductibleFactor],
        company->quoteData->coverage->liabilityDeductible
    )
}

//---- Territory Adjustment Factor - state specific factor

property Vehicle territoryAdjFactorLiab number.decimal {
    lookup([TerritoryAdjFactor], [TerritoryAdjFactor.territoryAdjFactorLiab],
        vehicleGarageIsoTerritory
    )
}

property Vehicle territoryAdjFactorLongHaulLiab number.decimal {
    lookup([TerritoryAdjFactorLongHaul], [TerritoryAdjFactorLongHaul.territoryAdjFactorLongHaulLiab],
        vehicleEndZone
    )
}

//---- expected loss ratio (elr) aka the target loss ratio, or permissible loss ratio
property Vehicle elrLiab number.decimal {
    lookup([ExpModElrs], [ExpModElrs.elrLiab],
        1
    )
}
