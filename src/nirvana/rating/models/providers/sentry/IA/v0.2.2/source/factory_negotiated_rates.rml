// Negotiated Rate


// Outputs: Negotiated Rate Flag

output OutputsQuote negotiatedRateFlag boolean {
    rule15ExemptionFlag
}


// Outputs: Complete Package

output OutputsQuote traditionalNonFlatLiabCompletePolicyPremium number.decimal {
    nonFlatLiabCompletePolicyPremium -
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->coverageModPremiumLiab
}

output OutputsQuote negotiatedNonFlatLiabCompletePolicyPremium number.decimal {
    nonFlatLiabCompletePolicyPremium -
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->negotiatedModRateLiab
}

output OutputsQuote traditionalNonFlatPhysCompletePolicyPremium number.decimal {
    nonFlatPhysCompletePolicyPremium -
    quoteData->coverage->coverageFinalModPremiumPhys +
    quoteData->coverage->coverageModPremiumPhys
}

output OutputsQuote negotiatedNonFlatPhysCompletePolicyPremium number.decimal {
    nonFlatPhysCompletePolicyPremium -
    quoteData->coverage->coverageFinalModPremiumPhys +
    quoteData->coverage->negotiatedModRatePhys
}


// Outputs: Standard Package

output OutputsQuote traditionalNonFlatLiabStandardPolicyPremium number.decimal {
    nonFlatLiabStandardPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->coverageModPremiumLiab
}

output OutputsQuote negotiatedNonFlatLiabStandardPolicyPremium number.decimal {
    nonFlatLiabStandardPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->negotiatedModRateLiab
}

output OutputsQuote traditionalNonFlatPhysStandardPolicyPremium number.decimal {
    nonFlatPhysStandardPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumPhys +
    quoteData->coverage->coverageModPremiumPhys
}

output OutputsQuote negotiatedNonFlatPhysStandardPolicyPremium number.decimal {
    nonFlatPhysStandardPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumPhys +
    quoteData->coverage->negotiatedModRatePhys
}


// Outputs: Basic Package

output OutputsQuote traditionalNonFlatLiabBasicPolicyPremium number.decimal {
    nonFlatLiabBasicPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->coverageModPremiumLiab
}

output OutputsQuote negotiatedNonFlatLiabBasicPolicyPremium number.decimal {
    nonFlatLiabBasicPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->negotiatedModRateLiab
}

output OutputsQuote traditionalNonFlatPhysBasicPolicyPremium number.decimal {
    nonFlatPhysBasicPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumPhys +
    quoteData->coverage->coverageModPremiumPhys
}

output OutputsQuote negotiatedNonFlatPhysBasicPolicyPremium number.decimal {
    nonFlatPhysBasicPolicyPremium -
    quoteData->coverage->coverageFinalModPremiumPhys +
    quoteData->coverage->negotiatedModRatePhys
}


// Final Mods and Calculations

property Coverage coverageFinalModPremiumLiab number.decimal {
    switch(hasNegotiatedRate) {
        case True:
            negotiatedModRateLiab
        case False:
            coverageModPremiumLiab
    }
}

property Coverage coverageFinalModPremiumPhys number.decimal {
    switch(hasNegotiatedRate) {
        case True:
            negotiatedModRatePhys
        case False:
            coverageModPremiumPhys
    }
}

property Coverage coverageModPremiumPhys number.decimal {
    coverageModPremiumColl + coverageModPremiumComp
}


property Coverage negotiatedModRateColl number.decimal {
    // split based on Sentry indication for APD trends
    0.75 * negotiatedModRatePhys
}

property Coverage negotiatedModRateComp number.decimal {
    // split based on Sentry indication for APD trends
    0.25 * negotiatedModRatePhys
}


// Rule 15 Exemption


// Outputs: Flag, Threshold, Premium

output OutputsQuote rule15ExemptionFlag boolean {
    rule15ExemptionPremium > rule15ExemptionThreshold
}

output OutputsQuote rule15ExemptionThreshold number.decimal {
    // looked back to 2010 – threshold of 100k has not changed
    100000.0
}

output OutputsQuote rule15ExemptionPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->coveragePremiumCollBasicLimitColl / quoteData->company->tierFactorColl +
            quoteData->coverage->coveragePremiumCompBasicLimitComp / quoteData->company->tierFactorComp +
            quoteData->coverage->coveragePremiumLiabBasicLimitLiab / quoteData->company->tierFactorLiab
        case False:
            quoteData->coverage->coveragePremiumLiabBasicLimitLiab / quoteData->company->tierFactorLiab
    }
}
