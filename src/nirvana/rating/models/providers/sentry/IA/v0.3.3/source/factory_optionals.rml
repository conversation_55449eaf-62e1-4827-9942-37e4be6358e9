//----- Optional or Ancillary Coverages:  MedPay, UM, UIM, UMUIM
//----- P<PERSON>, PPI (MI only) will have their own factory.rml

//----- Enum reference section

tag_enum MedPayLimitEnum

tag_enum UmLimitEnum

tag_enum UimLimitEnum

tag_enum UmUimLimitEnum


//----- Lookup table reference section

lookup_table MedPayLimitFactor {
    inputs {
        medPayLimit MedPayLimitEnum
    }
    outputs {
        medPayLimitFactor number.decimal
        medPayLiabilityZoneFactor number.decimal
    }
}

lookup_table UmBaseRate {
    inputs {
        umLimit UmLimitEnum
        vehType VehTypeEnum
    }
    outputs {
        value number.decimal
    }
}

lookup_table UimBaseRate {
    inputs {
        uimLimit UimLimitEnum
        vehType VehTypeEnum
    }
    outputs {
        value number.decimal
    }
}

lookup_table UmUimBaseRate {
    inputs {
        umUimLimit UmUimLimitEnum
        vehType VehTypeEnum
    }
    outputs {
        value number.decimal
    }
}


//----- Premium Calculation Section

//---- Medical Payments (aka MP or MedPay) premium calculation section

property Coverage coverageManualPremiumMedPay number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumMedPay])
}

property Coverage coverageModPremiumMedPay number.decimal {
    coverageManualPremiumMedPay
    * quoteData->underwriting->scheduleModMedPay
    * quoteData->underwriting->expModMedPay
}

property Vehicle vehPremiumMedPay number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                medPayLiabilityZoneFactor *
                medPayLimitFactor *
                isoPrimaryFactorMedPay *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab
        case False:
            baseRateMedPay *
                medPayLimitFactor *
                isoPrimaryFactorMedPay *
                lcmLiab * 
                territoryAdjFactorLiab
    } 
}

property Vehicle medPayLiabilityZoneFactor number.decimal {
    lookup([MedPayLimitFactor], [MedPayLimitFactor.medPayLiabilityZoneFactor],
        company->quoteData->coverage->medPayLimit
    )
}

property Vehicle medPayLimitFactor number.decimal {
    lookup([MedPayLimitFactor], [MedPayLimitFactor.medPayLimitFactor],
        company->quoteData->coverage->medPayLimit
    )
}

property Vehicle isoPrimaryFactorMedPay number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorMedPay],
        isoWeightGroup,
        vehType,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle baseRateMedPay number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateMedPay],
        vehicleGarageIsoTerritory
    )
}


//----- Uninsured Motorists (aka UM) premium calculation section

property Coverage coverageManualPremiumUm number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumUm])
}

property Coverage coverageModPremiumUm number.decimal {
    coverageManualPremiumUm
    * quoteData->underwriting->scheduleModUms
    * quoteData->underwriting->expModUms
}

property Vehicle vehPremiumUm number.decimal {
    switch(useZoneRating) {
        case True:
            umBaseRate *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab
        case False:
            umBaseRate *
                lcmLiab *
                territoryAdjFactorLiab
    } 
}

property Vehicle umBaseRate number.decimal {
    lookup([UmBaseRate], [UmBaseRate.value],
        company->quoteData->coverage->umLimit,
        vehType
    )
}


//----- Under-insured Motorists (aka UIM) premium calculation section

property Coverage coverageManualPremiumUim number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumUim])
}

property Coverage coverageModPremiumUim number.decimal {
    coverageManualPremiumUim
    * quoteData->underwriting->scheduleModUms
    * quoteData->underwriting->expModUms
}

property Vehicle vehPremiumUim number.decimal {
    switch(useZoneRating) {
        case True:
            uimBaseRate *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab
        case False:
            uimBaseRate *
                lcmLiab *
                territoryAdjFactorLiab
    }
}

property Vehicle uimBaseRate number.decimal {
    lookup([UimBaseRate], [UimBaseRate.value],
        company->quoteData->coverage->uimLimit,
        vehType
    )
}


//----- Uninsured and Under-insured Motorists (aka UM UIM) premium calculation section
//----- State Specific for whether combined UM and UIM applies - include in all state builds

property Coverage coverageManualPremiumUmUim number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumUmUim])
}

property Coverage coverageModPremiumUmUim number.decimal {
    coverageManualPremiumUmUim
    * quoteData->underwriting->scheduleModUms
    * quoteData->underwriting->expModUms
}

property Vehicle vehPremiumUmUim number.decimal {
    switch(useZoneRating) {
        case True:
            umUimBaseRate *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab
        case False:
            umUimBaseRate *
                lcmLiab *
                territoryAdjFactorLiab
    } 
}

property Vehicle umUimBaseRate number.decimal {
    lookup([UmUimBaseRate], [UmUimBaseRate.value],
        company->quoteData->coverage->umUimLimit,
        vehType
    )
}
