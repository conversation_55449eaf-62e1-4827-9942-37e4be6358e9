//This section defines the experience modification calculation

//Derivation of the final modification

property Underwriting expModLiab number.decimal {
    (
        (quoteData->lossHistory->aerLiab - quoteData->lossHistory->eerLiab) / quoteData->lossHistory->eerLiab
    ) *
    quoteData->lossHistory->credibilityLiab
    + 1.0
}

property Underwriting expModCollComp number.decimal {
    (
        (quoteData->lossHistory->aerCollComp - quoteData->lossHistory->eerCollComp) / quoteData->lossHistory->eerCollComp
    ) *
    quoteData->lossHistory->credibilityCollComp
    + 1.0
}

property Underwriting expModColl number.decimal {
    expModCollComp
}

property Underwriting expModComp number.decimal {
    expModCollComp
}

//Derivation of the Expected Experience Ratio (aka EER)

//EER calculations
property YearlyLossSummary annualBasicLimitLossCostLiab number.decimal {
    number.decimal{powerUnitCount} *
    lossHistory->quoteData->coverage->avgPuPremiumLiabBasicLimitLiab *
    number.decimal{boolean_to_number(includePriorYearExpMod)}
}

property YearlyLossSummary annualBasicLimitLossCostCollComp number.decimal {
    number.decimal{powerUnitCount} *
    (
        lossHistory->quoteData->coverage->avgPuPremiumCollBasicLimitColl
    +   lossHistory->quoteData->coverage->avgPuPremiumCompBasicLimitComp
    ) *
    number.decimal{boolean_to_number(includePriorYearExpMod)}
}

property YearlyLossSummary detrendFactorLiab number.decimal {
    lookup([DetrendFactors], [DetrendFactors.detrendFactorLiab],
        priorYearLabel
    )
}

property YearlyLossSummary detrendFactorCollComp number.decimal {
    lookup([DetrendFactors], [DetrendFactors.detrendFactorCollComp],
        priorYearLabel
    )
}

property YearlyLossSummary annualBasicLimitLossCostDetrendedLiab number.decimal {
    annualBasicLimitLossCostLiab * detrendFactorLiab
}

property YearlyLossSummary annualBasicLimitLossCostDetrendedCollComp number.decimal {
    annualBasicLimitLossCostCollComp * detrendFactorCollComp
}

property LossHistory subjectLossCostLiab number.decimal {
    sum(yearlyLossSummaries, [YearlyLossSummary.annualBasicLimitLossCostDetrendedLiab])
}

property LossHistory subjectLossCostCollComp number.decimal {
    number_max(sum(yearlyLossSummaries, [YearlyLossSummary.annualBasicLimitLossCostDetrendedCollComp]),
               11111.1)
}

property LossHistory expModStateGroupLiab number.integer {
    lookup([ExpModStateGroup], [ExpModStateGroup.expModStateGroupLiab],
        quoteData->company->companyGarageState,
        quoteData->company->percentRadiusLong
    )
}

property LossHistory credibilityLiab number.decimal {
    lookup([ExpModCredEerMslLiab], [ExpModCredEerMslLiab.credibilityLiab],
        subjectLossCostLiab,
        expModStateGroupLiab
    )
}

property LossHistory credibilityCollComp number.decimal {
    lookup([ExpModCredEerMslCollComp], [ExpModCredEerMslCollComp.credibilityCollComp],
        subjectLossCostCollComp
    )
}

property LossHistory eerLiab number.decimal {
    lookup([ExpModCredEerMslLiab], [ExpModCredEerMslLiab.eerLiab],
        subjectLossCostLiab,
        expModStateGroupLiab
    )
}

property LossHistory eerCollComp number.decimal {
    lookup([ExpModCredEerMslCollComp], [ExpModCredEerMslCollComp.eerCollComp],
        subjectLossCostCollComp
    )
}

property LossHistory mslLiab number.decimal {
    lookup([ExpModCredEerMslLiab], [ExpModCredEerMslLiab.mslLiab],
        subjectLossCostLiab,
        expModStateGroupLiab
    )
}

property LossHistory mslCollComp number.decimal {
    lookup([ExpModCredEerMslCollComp], [ExpModCredEerMslCollComp.mslCollComp],
        subjectLossCostCollComp
    )
}

//EER lookups

lookup_table DetrendFactors {
    inputs {
        priorYearLabel PriorYearLabelEnum
    }
    outputs {
        detrendFactorLiab number.decimal
        detrendFactorCollComp number.decimal
    }
}

lookup_table ExpModStateGroup {
    inputs {
        companyGarageState GeoStateAbbrEnum
        percentRadiusLong range[number.decimal]
    }
    outputs {
        expModStateGroupLiab number.integer
        //placeholder for Phys Dam
    }
}

lookup_table ExpModCredEerMslLiab {
    inputs {
        subjectLossCostLiab range[number.decimal]
        expModStateGroupLiab range[number.integer]
    }
    outputs {
        credibilityLiab number.decimal
        eerLiab number.decimal
        mslLiab number.decimal
    }
}

lookup_table ExpModCredEerMslCollComp {
    inputs {
        subjectLossCostCollComp range[number.decimal]
    }
    outputs {
        credibilityCollComp number.decimal
        eerCollComp number.decimal
        mslCollComp number.decimal
    }
}

//Derivation of the Actual Experience Ratio (aka AER)

//AER calculations

property LargeLiabilityLoss totalIndemnityDccLiab number.decimal {
    indemnityAmount + dccAmount
}

property LargeLiabilityLoss cappedIndemnityAmountLiab number.decimal {
    number_min(100000.0, indemnityAmount)
}

property LargeLiabilityLoss cappedIndemnityPlusFullDccLiab number.decimal {
    cappedIndemnityAmountLiab + dccAmount
}

property LargeLiabilityLoss cappedIndemnityCappedDccLiab number.decimal {
    number_min(yearlyLossSummary->lossHistory->mslLiab, cappedIndemnityPlusFullDccLiab)
}

property LargeCollCompLoss cappedIndemnityCappedCollComp number.decimal {
    number_min(yearlyLossSummary->lossHistory->mslCollComp, indemnityAmount)
}

property YearlyLossSummary totalLargeLossAmountLiab number.decimal {
    sum(largeLiabilityLosses, [LargeLiabilityLoss.totalIndemnityDccLiab])
}

property YearlyLossSummary totalLargeLossAmountCollComp number.decimal {
    sum(largeCollCompLosses, [LargeCollCompLoss.indemnityAmount])
}

property YearlyLossSummary cappedLargeLossAmountLiab number.decimal {
    sum(largeLiabilityLosses, [LargeLiabilityLoss.cappedIndemnityCappedDccLiab])
}

property YearlyLossSummary cappedLargeLossAmountCollComp number.decimal {
    sum(largeCollCompLosses, [LargeCollCompLoss.cappedIndemnityCappedCollComp])
}

property YearlyLossSummary censoredLossAmountLiab number.decimal {
    (totalLargeLossAmountLiab - cappedLargeLossAmountLiab) * number.decimal{boolean_to_number(includePriorYearExpMod)}
}

property YearlyLossSummary censoredLossAmountCollComp number.decimal {
    (totalLargeLossAmountCollComp - cappedLargeLossAmountCollComp) * number.decimal{boolean_to_number(includePriorYearExpMod)}
}

property YearlyLossSummary includedUncappedLossAmountLiab number.decimal {
    lossesLiab * number.decimal{boolean_to_number(includePriorYearExpMod)}
}

property YearlyLossSummary includedUncappedLossAmountCollComp number.decimal {
    lossesCollComp * number.decimal{boolean_to_number(includePriorYearExpMod)}
}

property LossHistory actualCappedLossesLiab number.decimal {
    sum(yearlyLossSummaries, [YearlyLossSummary.includedUncappedLossAmountLiab])
    - sum(yearlyLossSummaries, [YearlyLossSummary.censoredLossAmountLiab])
}

property LossHistory actualCappedLossesCollComp number.decimal {
    sum(yearlyLossSummaries, [YearlyLossSummary.includedUncappedLossAmountCollComp])
    - sum(yearlyLossSummaries, [YearlyLossSummary.censoredLossAmountCollComp])
}

property YearlyLossSummary expModDevFactorLiab number.decimal {
    lookup([ExpModDevFactor], [ExpModDevFactor.value],
        reportAgeInMonths,
        lossHistory->expModStateGroupLiab
    )

}

property YearlyLossSummary expectedUnreportedAmountLiab number.decimal {
    annualBasicLimitLossCostDetrendedLiab * lossHistory->eerLiab * expModDevFactorLiab
}

property LossHistory expectedUnreportedTotalLiab number.decimal {
    sum(yearlyLossSummaries, [YearlyLossSummary.expectedUnreportedAmountLiab])
}

property LossHistory aeAmountLiab number.decimal {
    actualCappedLossesLiab + expectedUnreportedTotalLiab
}

property LossHistory aeAmountCollComp number.decimal {
    actualCappedLossesCollComp //no unreported
}

property LossHistory aerLiab number.decimal {
    aeAmountLiab / subjectLossCostLiab
}

property LossHistory aerCollComp number.decimal {
    aeAmountCollComp / subjectLossCostCollComp
}

//AER lookup tables

lookup_table ExpModDevFactor {
    inputs {
        reportAgeInMonths range[number.integer]
        expModStateGroupLiab range[number.integer]
    }
    outputs {
        value number.decimal
    }
}
