//---- Collision

//---- Premiums

output Coverage coverageModPremiumColl number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehModPremiumColl])
}

output Coverage coverageManualPremiumColl number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumColl])
}

property Vehicle vehModPremiumColl number.decimal {
    raterCOLL->totalVehPremiumCOLL
}

property Vehicle vehPremiumColl number.decimal {
    raterCOLL->collVehPremium
}

property Coverage coveragePremiumCollBasicLimitColl number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumCollBasicLimitColl])
}

property Coverage avgPuPremiumCollBasicLimitColl number.decimal {
    coveragePremiumCollBasicLimitColl / number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle vehPremiumCollBasicLimitColl number.decimal {
    raterCOLL->collVehPremium * elrColl
}

//---- Rater Calculation

property RaterCOLL totalVehPremiumCOLL number.decimal {
    collVehPremium *
    totalScheduleModCOLL *
    totalExpModCOLL
}

property RaterCOLL totalScheduleModCOLL number.decimal {
    vehicle->company->quoteData->underwriting->scheduleModColl
}

property RaterCOLL totalExpModCOLL number.decimal {
    vehicle->company->quoteData->underwriting->expModColl
}

property RaterCOLL collVehPremium number.decimal {
    collBaseRate *
    collLCM *
    collISOPrimaryFactor *
    collISOSecondaryFactor *
    collFleetSizeFactor *
    number_max((collFactor1_StatedValue - collFactor2_Deductible), 0.1) *
    collNAICFactor *
    collTierFactor *
    collTerritoryAdjFactor *
    collTimeInBusinessFactor
}

property RaterCOLL collBaseRate number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->longHaulBaseRateColl
            case False:
                vehicle->baseRateColl
        }
    }
}

property RaterCOLL collLCM number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->lcmCollZone
            case False:
                vehicle->lcmColl
        }
    }
}

property RaterCOLL collISOPrimaryFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->isoPrimaryFactorColl
            case False:
                vehicle->isoPrimaryFactorColl
        }
    }
}

property RaterCOLL collISOSecondaryFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                1.0
            case False:
                vehicle->isoSecondaryFactorColl
        }
    }
}

property RaterCOLL collFleetSizeFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                1.0
            case False:
                vehicle->fleetSizeFactorColl
        }
    }
}

property RaterCOLL collFactor1_StatedValue number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->longHaulStatedValueFactorColl
                case False:
                    vehicle->longHaulOriginalCostNewFactorColl
                            }
            case False:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->statedValueFactorColl
                case False:
                    vehicle->originalCostNewFactorColl
            }
        }
    }
}

property RaterCOLL collFactor2_Deductible number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->longHaulCollDeductibleFactor
                case False:
                    vehicle->longHaulCollDeductibleFactor
                            }
            case False:
            switch(vehicle->company->quoteData->underwriting->useStatedValueCollComp) {
                case True:
                    vehicle->collDeductibleFactor
                case False:
                    vehicle->collDeductibleFactor
            }
        }
    }
}

property RaterCOLL collNAICFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                1.0
            case False:
                vehicle->naicFactorColl
        }
    }
}

property RaterCOLL collTierFactor number.decimal {
    switch(vehicle->hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(vehicle->useZoneRating) {
            case True:
                vehicle->company->tierFactorColl
            case False:
                vehicle->company->tierFactorColl
        }
    }
}

property RaterCOLL collTerritoryAdjFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->territoryAdjFactorLongHaulColl
        case False:
            vehicle->territoryAdjFactorColl
    }
}

property RaterCOLL collTimeInBusinessFactor number.decimal {
    switch(vehicle->useZoneRating) {
        case True:
            vehicle->company->yearsInBusinessFactor
        case False:
            vehicle->company->yearsInBusinessFactor
    }
}

//---- Rater Characteristics

output RaterCOLL characteristicBasedOnHasApdCoverage boolean {
    vehicle->hasApdCoverage
}

output RaterCOLL characteristicBasedOnUseZoneRating boolean {
    vehicle->useZoneRating
}

output RaterCOLL characteristicVehicleZoneStart IsoZoneEnum {
    vehicle->vehicleStartZone
}

output RaterCOLL characteristicVehicleZoneEnd IsoZoneEnum {
    vehicle->vehicleEndZone
}

output RaterCOLL characteristicStatedValue number.decimal {
    vehicle->statedValue
}

output RaterCOLL characteristicVehicleGarageIsoTerritory IsoTerritoryEnum {
    lookup([IsoTerritory], [IsoTerritory.value],
        vehicle->garageZip
        )
}

output RaterCOLL characteristicDeductibleColl CollDeductibleEnum {
    vehicle->company->quoteData->coverage->collDeductible
}

output RaterCOLL characteristicIsoPrimary_isoWeightGroup IsoWeightGroupEnum {
    vehicle->isoWeightGroup
}

output RaterCOLL characteristicIsoPrimary_vehType VehTypeEnum {
    vehicle->vehType
}

output RaterCOLL characteristicIsoPrimary_vehicleGpsIsoRadiusGroup IsoRadiusGroupEnum {
    vehicle->vehicleGpsIsoRadiusGroup
}

output RaterCOLL characteristicIsoPrimary_isoOperationType IsoOperationTypeEnum {
    vehicle->company->isoOperationType
}

output RaterCOLL characteristicIsoSecondary_commodityGroup CommodityGroupEnum {
    vehicle->company->commodityGroup
}

output RaterCOLL characteristicIsoSecondary_commodityDetail CommodityDetailEnum {
    vehicle->company->commodityDetail
}

output RaterCOLL characteristicFmcsaYears number.integer {
    vehicle->company->fmcsaYears
}

//---- Vehicle Data For Rater

property Vehicle longHaulBaseRateColl number.decimal {
    lookup([LongHaulIsoBaseRate], [LongHaulIsoBaseRate.longHaulBaseRateColl],
        vehicleStartZone,
        vehicleEndZone
    )
}

property Vehicle baseRateColl number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateColl],
        vehicleGarageIsoTerritory
    )
}

property Vehicle lcmColl number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmColl],
        1
    )
}

property Vehicle lcmCollZone number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmCollZone],
        1
    )
}

property Vehicle isoPrimaryFactorColl number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorColl],
        isoWeightGroup,
        vehType,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle isoSecondaryFactorColl number.decimal {
    lookup ([IsoSecondaryFactor], [IsoSecondaryFactor.isoSecondaryFactorColl],
        vehType,
        company->commodityGroup,
        company->commodityDetail
    )
}

property Vehicle fleetSizeFactorColl number.decimal {
    lookup([FleetSizeFactor], [FleetSizeFactor.fleetSizeFactorColl],
        company->fleetPowerUnitCount,
        vehType,
        isoWeightGroup        
    )
}

property Vehicle longHaulStatedValueFactorColl number.decimal {
    lookup([LongHaulStatedValueFactorCollComp], [LongHaulStatedValueFactorCollComp.longHaulStatedValueFactorColl],
        statedValue,
        vehType
    )
}

property Vehicle longHaulOriginalCostNewFactorColl number.decimal {
    lookup([LongHaulOriginalCostNewFactorCollComp], [LongHaulOriginalCostNewFactorCollComp.longHaulOriginalCostNewFactorColl],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle statedValueFactorColl number.decimal {
    lookup([StatedValueFactorCollComp], [StatedValueFactorCollComp.statedValueFactorColl],
        statedValue,
        vehType
    )
}

property Vehicle originalCostNewFactorColl number.decimal {
    lookup([OriginalCostNewFactorCollComp], [OriginalCostNewFactorCollComp.originalCostNewFactorColl],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle naicFactorColl number.decimal {
    lookup([NaicFactor], [NaicFactor.naicFactorColl],
        company->naic,
        vehType
    )
}

property Vehicle collDeductibleFactor number.decimal {
    lookup([CollDeductibleFactor], [CollDeductibleFactor.collDeductibleFactor],
        company->quoteData->coverage->collDeductible,
        vehType
    )
}

property Vehicle longHaulCollDeductibleFactor number.decimal {
    lookup([CollDeductibleFactor], [CollDeductibleFactor.longHaulCollDeductibleFactor],
        company->quoteData->coverage->collDeductible,
        vehType
    )
}

//---- Territory Adjustment Factor - state specific factor

property Vehicle territoryAdjFactorColl number.decimal {
    lookup([TerritoryAdjFactor], [TerritoryAdjFactor.territoryAdjFactorCollComp],
        vehicleGarageIsoTerritory
    )
}

property Vehicle territoryAdjFactorLongHaulColl number.decimal {
    lookup([TerritoryAdjFactorLongHaul], [TerritoryAdjFactorLongHaul.territoryAdjFactorLongHaulCollComp],
        vehicleEndZone
    )
}

//---- expected loss ratio (elr) aka the target loss ratio, or permissible loss ratio
property Vehicle elrColl number.decimal {
    lookup([ExpModElrs], [ExpModElrs.elrColl],
        1
    )
}
