//----- Subhaul premium computation
//----- California coverage only


//----- Lookup table reference section

lookup_table SubhaulRate {
    inputs {
        dummyKey range[number.integer]
    }
    outputs {
        subhaulRate number.decimal
    }
}

//----- Derived Data

property Coverage subhaulRate number.decimal {
    lookup([SubhaulRate], [SubhaulRate.subhaulRate],
        1
        )
}

//----- Premium Calculation Section

property Coverage coverageSubhaulPremiumLiab number.decimal {
    coverageFinalModPremiumLiab
    * subhaulRate
    * ( percentSubhaul / 100.0 )
}