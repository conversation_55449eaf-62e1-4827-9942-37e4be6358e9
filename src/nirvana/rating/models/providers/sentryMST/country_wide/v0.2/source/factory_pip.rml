//----- Personal Injury Protection (aka PIP or No-Fault), Guest PIP, Property Protection Insurance (aka PPI), and PIP Excess Attendant Care (aka PIP EAC)
//----- Not offered in all states: 
//      PIP Required: DE, FL, HI, KS, MA, MI, MN, NJ, NY, ND, OR, PA, UT
//      PIP Optional: AR, KY*, MD, SD, TX, VA, WA
//                          *Rejectable only through DOI
//----- Michigan has PIP and PPI. BOTH are included here.

//----- Enum reference section

tag_enum PipLimitEnum

//----- Lookup table reference section

lookup_table PipFactor {
    inputs {
        companyGarageState GeoStateAbbrEnum
        hasPIPCoverage boolean
    }
    outputs {
        pipFactorLiab number.decimal
        pipFactorPip number.decimal
        pipFactorGuestPip number.decimal
        pipFactorMedPay number.decimal
        pipFactorPpi number.decimal
        pipFactorOther number.decimal
    }
}

lookup_table PipLimitFactor {
    inputs {
        companyGarageState GeoStateAbbrEnum
        pipLimit PipLimitEnum
    }
    outputs {
        pipLimitFactor number.decimal
    }
}

lookup_table PipExcessAttendantCare {
    inputs {
        companyGarageState GeoStateAbbrEnum
        pipLimit PipLimitEnum
    }
    outputs {
        pipExcessAttendantCareFactor number.decimal
    }
}

//----- PIP Premium Calculation Section

property Coverage coverageManualPremiumPip number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumPip ])
}

property Coverage coverageModPremiumPip number.decimal {
    coverageManualPremiumPip
    * quoteData->underwriting->scheduleModMedPay
    * quoteData->underwriting->expModMedPay
}

property Vehicle vehPremiumPip number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab *
                isoPrimaryFactorLiab *
                company->pipFactorPip *
                company->pipLimitFactor
        case False:
            baseRatePip *
                lcmLiab *
                territoryAdjFactorLiab *
                company->pipLimitFactor *
                isoPrimaryFactorMedPay          //--For non-Zone PIP, similar computation as MedPay
    } 
}

property Vehicle baseRatePip number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRatePip],
        vehicleGarageIsoTerritory
    )
}

property Company pipFactorPip number.decimal {
    lookup([PipFactor], [PipFactor.pipFactorPip],
        companyGarageState,
        quoteData->coverage->hasPIPCoverage
    )
}

property Company pipLimitFactor number.decimal {
    lookup([PipLimitFactor], [PipLimitFactor.pipLimitFactor],
        companyGarageState,
        quoteData->coverage->pipLimit
    )
}

property Company pipFactorLiab number.decimal {
    lookup([PipFactor], [PipFactor.pipFactorLiab],
        companyGarageState,
        quoteData->coverage->hasPIPCoverage
    )
}

property Company pipFactorMedPay number.decimal {
    lookup([PipFactor], [PipFactor.pipFactorMedPay],
        companyGarageState,
        quoteData->coverage->hasPIPCoverage
    )
}

//----- Guest PIP Premium Calculation Section

property Coverage coverageManualPremiumGuestPip number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumGuestPip ])
}

property Coverage coverageModPremiumGuestPip number.decimal {
    coverageManualPremiumGuestPip
    * quoteData->underwriting->scheduleModMedPay
    * quoteData->underwriting->expModMedPay
}

property Vehicle vehPremiumGuestPip number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab *
                isoPrimaryFactorLiab *
                company->pipFactorGuestPip
        case False:
            baseRateGuestPip *
                lcmLiab *
                territoryAdjFactorLiab *
                isoPrimaryFactorMedPay
    } 
}

property Vehicle baseRateGuestPip number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateGuestPip],
        vehicleGarageIsoTerritory
    )
}

property Company pipFactorGuestPip number.decimal {
    lookup([PipFactor], [PipFactor.pipFactorGuestPip],
        companyGarageState,
        quoteData->coverage->hasPIPCoverage
    )
}

//----- PPI Premium Calculation Section

property Coverage coverageManualPremiumPpi number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumPpi ])
}

property Coverage coverageModPremiumPpi number.decimal {         //------ Not multiplying by any schedule or experience
    coverageManualPremiumPpi
}

property Vehicle vehPremiumPpi number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab *
                isoPrimaryFactorLiab *
                company->pipFactorPpi
        case False:
            baseRatePpi *
                lcmLiab *
                territoryAdjFactorLiab *
                isoPrimaryFactorLiab *
                isoSecondaryFactorLiab *
                fleetSizeFactorLiab
    } 
}

property Vehicle baseRatePpi number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRatePpi],
        vehicleGarageIsoTerritory
    )
}

property Company pipFactorPpi number.decimal {
    lookup([PipFactor], [PipFactor.pipFactorPpi],
        companyGarageState,
        quoteData->coverage->hasPIPCoverage
    )
}


//----- PIP Excess Attendant Care Coverage (PIP EAC) Premium Calculation Section

property Coverage coverageManualPremiumPipEac number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumPipEac ])
}

property Coverage coverageModPremiumPipEac number.decimal {         //------ Not multiplying by any schedule or experience
    coverageManualPremiumPipEac
}

property Vehicle vehPremiumPipEac number.decimal {
    baseRatePip *
        lcmLiabZone *
        territoryAdjFactorLongHaulLiab *
        company->pipLimitFactor *
        company->pipExcessAttendantCareFactor
}

property Company pipExcessAttendantCareFactor number.decimal {
    lookup([PipExcessAttendantCare], [PipExcessAttendantCare.pipExcessAttendantCareFactor],
        companyGarageState,
        quoteData->coverage->pipLimit
    )
}
