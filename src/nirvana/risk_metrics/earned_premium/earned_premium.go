package earned_premium

import (
	"context"
	"database/sql"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/billing/bpid"
	billing_enums "nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/billing/fleet_pipeline/static_param"
	billing_policy "nirvanatech.com/nirvana/billing/legacy/policy"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	db_policy "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/risk_metrics/billed_mileage"
	"nirvanatech.com/nirvana/db-api/db_wrappers/risk_metrics/earned_premium"
	"nirvanatech.com/nirvana/policy"
	"nirvanatech.com/nirvana/policy/fleet"
)

func CalculateAndPersistMonthlyEarnedPremium(
	ctx context.Context,
	billedMileageWrapper *billed_mileage.DataWrapper,
	staticParamsClient static_param.Client,
	earnedPremiumWrapper *earned_premium.DataWrapper,
	policyClient policy.Client,
	startOfBilledMonth time.Time,
	p db_policy.Policy,
	override bool,
) error {
	ctx = log.ContextWithFields(ctx, log.String(
		"policyNumber", p.PolicyNumber.String(),
	))
	evaluatedAt := time.Now()
	endOfMonth := time_utils.EndOfMonthFor(startOfBilledMonth)
	billingInterval := time_utils.DateInterval{
		Start: time_utils.DateFromTime(startOfBilledMonth),
		End:   time_utils.DateFromTime(endOfMonth),
	}

	earnedPremiums, err := calculatePremiumsByCoverage(
		ctx,
		billedMileageWrapper,
		staticParamsClient,
		policyClient,
		p,
		billingInterval,
		endOfMonth,
		startOfBilledMonth,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to calculate earned premium for policy %s", p.PolicyNumber.String())
	}
	for coverage, earnedPremium := range earnedPremiums {
		if err := persistEarnedPremium(
			ctx,
			earnedPremiumWrapper,
			p.PolicyNumber.String(),
			coverage,
			startOfBilledMonth,
			evaluatedAt,
			earnedPremium,
			override,
		); err != nil {
			return errors.Wrapf(err, "failed to persist earned premium for policy %s", p.PolicyNumber.String())
		}
	}

	return nil
}

func calculatePremiumsByCoverage(
	ctx context.Context,
	billedMileageWrapper *billed_mileage.DataWrapper,
	staticParamsClient static_param.Client,
	policyClient policy.Client,
	p db_policy.Policy,
	billingInterval time_utils.DateInterval,
	endOfMonth, startOfMonth time.Time,
) (map[string]decimal.Decimal, error) {
	premiums := make(map[string]decimal.Decimal)

	ALPolicyNumber, err := fleet.GeneratePolicyNumber(
		app_enums.CoverageAutoLiability,
		p.EffectiveDate,
		p.PolicyNumber.GetPolicyIdentifier(),
		p.InsuranceCarrier,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate AL policy number")
	}

	// From the invoice master, we obtain the total mileage for the month. Monthly mileage is stored under the Al policy number.
	// This is how it's obtained from the invoice master.
	monthlyMileage, err := billedMileageWrapper.GetForMonthAndPolicyNumber(ctx, ALPolicyNumber.String(), startOfMonth)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrapf(err, "failed to get total mileage for policy %s", ALPolicyNumber.String())
	}
	dayRatio, err := billing_policy.GetDayRatio(ctx, policyClient, p, billingInterval)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get day ratio")
	}
	effectiveDays := billing_policy.GetEffectiveDays(p, billingInterval)

	billingPolicyId := bpid.NewBillingPolicyIdFromPolicyNumber(*p.PolicyNumber)

	coverages := p.PolicyNumber.PrimaryCoverages()
	if len(coverages) == 0 {
		return nil, errors.Newf("policy %s has no primary coverages", p.PolicyNumber.String())
	}

	for _, coverage := range coverages {
		rateType, exposure, err := getCoverageRateTypeAndExposure(
			ctx,
			coverage,
			staticParamsClient,
			billingPolicyId,
			monthlyMileage,
			*dayRatio,
			effectiveDays,
			endOfMonth,
		)
		if err != nil {
			return nil, err
		}

		rate, err := getWeightedAverageRate(
			ctx,
			staticParamsClient,
			billingPolicyId,
			rateType,
			startOfMonth,
			endOfMonth,
			p.EffectiveDate,
		)
		if err != nil {
			return nil, err
		}

		coveragePremium := exposure.Mul(rate)
		premiums[coverage.String()] = coveragePremium

		addFlatChargeIfApplicable(p, *dayRatio, coverage, premiums)
	}

	return premiums, nil
}

func addFlatChargeIfApplicable(
	p db_policy.Policy,
	dayRatio decimal.Decimal,
	coverage app_enums.Coverage,
	premiums map[string]decimal.Decimal,
) {
	// Only add flat charge for Auto Liability coverage
	if coverage == app_enums.CoverageAutoLiability {
		flatCharge := decimal.NewFromFloat(pointer_utils.Float64ValOr(p.BillingInfo.FlatCharge, 0.0))
		// Store the flat charge under the new CoverageFlatCharge in the premiums map
		premiums["CoverageFlatCharge"] = flatCharge.Mul(dayRatio)
	}
}

func getCoverageRateTypeAndExposure(
	ctx context.Context,
	coverage app_enums.Coverage,
	staticParamsClient static_param.Client,
	billingPolicyId bpid.BillingPolicyId,
	monthlyMileage *billed_mileage.BilledMileage,
	dayRatio decimal.Decimal,
	effectiveDays int,
	endOfMonth time.Time,
) (billing_enums.StaticParamType, decimal.Decimal, error) {
	//nolint:exhaustive
	switch coverage {
	case app_enums.CoverageAutoLiability:
		if monthlyMileage == nil {
			log.Info(ctx, "no monthly mileage found for policy")
			return billing_enums.StaticParamTypeALRate,
				decimal.Zero,
				nil
		}
		return billing_enums.StaticParamTypeALRate, decimal.NewFromFloat(monthlyMileage.Mileage), nil

	case app_enums.CoverageMotorTruckCargo:
		if monthlyMileage == nil {
			log.Info(ctx, "no monthly mileage found for policy")
			return billing_enums.StaticParamTypeMTCRate,
				decimal.Zero,
				nil
		}
		return billing_enums.StaticParamTypeMTCRate, decimal.NewFromFloat(monthlyMileage.Mileage), nil

	case app_enums.CoverageGeneralLiability:
		exposure := decimal.NewFromInt(int64(effectiveDays))
		return billing_enums.StaticParamTypeGLRate, exposure, nil

	case app_enums.CoverageAutoPhysicalDamage:
		tiv, err := staticParamsClient.GetValueForDate(ctx, billingPolicyId, billing_enums.StaticParamTypeTIV, time_utils.DateFromTime(endOfMonth))
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return billing_enums.StaticParamTypeAPDRate, decimal.Zero, errors.Wrapf(err, "failed to get tiv for billingPolicyId: %s", billingPolicyId)
		}
		exposure := tiv.Mul(dayRatio)
		return billing_enums.StaticParamTypeAPDRate, exposure, nil

	default:
		return billing_enums.StaticParamTypeALRate, decimal.Zero, errors.Newf("unhandled coverage type: %v", coverage)
	}
}

func getWeightedAverageRate(
	ctx context.Context,
	staticParamsClient static_param.Client,
	billingPolicyId bpid.BillingPolicyId,
	paramType billing_enums.StaticParamType,
	startOfMonth, endOfMonth time.Time,
	policyEffectiveDate time.Time,
) (decimal.Decimal, error) {
	paramsUntilDate, err := staticParamsClient.GetValuesUntilDate(
		ctx,
		billingPolicyId,
		paramType,
		time_utils.DateFromTime(endOfMonth))
	if err != nil {
		return decimal.Zero, err
	}
	if len(paramsUntilDate) == 0 {
		return decimal.Zero, nil
	}
	startDate := time_utils.DateFromTime(startOfMonth)
	// Check if policyEffectiveDate is in the same month and year as startOfMonth
	if policyEffectiveDate.Year() == startOfMonth.Year() && policyEffectiveDate.Month() == startOfMonth.Month() {
		if policyEffectiveDate.After(startOfMonth) {
			startDate = time_utils.DateFromTime(policyEffectiveDate)
		}
	}
	return static_param.WeightedAverage(
		paramsUntilDate,
		startDate,
		time_utils.DateFromTime(endOfMonth),
	)
}

func persistEarnedPremium(
	ctx context.Context,
	earnedPremiumWrapper *earned_premium.DataWrapper,
	policyNumber string,
	coverage string,
	startOfMonth, evaluatedAt time.Time,
	amount decimal.Decimal,
	override bool,
) error {
	earnedPremium := earned_premium.New(
		uuid.New(),
		policyNumber,
		coverage,
		amount,
		startOfMonth,
		evaluatedAt,
	)
	return earnedPremiumWrapper.Upsert(ctx, earnedPremium, override)
}

// CalculateAndPersistFleetEarnedPremium iterates through all policies and runs the monthly premium calculation for each month within the policy period.
// Accepts get policy filters and override flag to update existing records.
func CalculateAndPersistFleetEarnedPremium(
	ctx context.Context,
	billedMileageWrapper *billed_mileage.DataWrapper,
	staticParamsClient static_param.Client,
	earnedPremiumWrapper *earned_premium.DataWrapper,
	policyClient policy.Client,
	policyFilters policy.GetPoliciesFilter,
	override bool,
) error {
	policyFilters.SkipTestAgencies = true
	policyFilters.ProgramType = pointer_utils.ToPointer(policy_enums.ProgramTypeFleet)

	// Get all policies
	policies, err := policyClient.GetPolicies(ctx, policyFilters)
	if err != nil {
		return errors.Wrap(err, "unable to fetch active policies")
	}
	endOfPrevMonth := time_utils.EndOfMonthFor(time.Now())

	for _, p := range policies {
		startOfPolicy := p.EffectiveDate
		endOfPolicy := p.EffectiveDateTo

		// Iterate through each month within the policy period
		for policyMonth := startOfPolicy; (policyMonth.Before(endOfPolicy) || policyMonth.Equal(endOfPolicy)) &&
			(policyMonth.Before(endOfPrevMonth) || policyMonth.Equal(endOfPrevMonth)); policyMonth = policyMonth.AddDate(0, 1, 0) {
			if err := CalculateAndPersistMonthlyEarnedPremium(
				ctx,
				billedMileageWrapper,
				staticParamsClient,
				earnedPremiumWrapper,
				policyClient,
				time_utils.StartOfMonthFor(policyMonth),
				p,
				override,
			); err != nil {
				return errors.Wrapf(err, "failed to calculate and persist earned premium for policy %s in month %s", p.PolicyNumber.String(), policyMonth)
			}
		}
	}

	return nil
}
