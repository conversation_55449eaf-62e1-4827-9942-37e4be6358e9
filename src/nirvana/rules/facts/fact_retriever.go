package facts

import "context"

// FactContext is a flexible data structure for holding the facts needed by the rule engine.
type FactContext map[string]interface{}

//go:generate go run go.uber.org/mock/mockgen -destination ../evaluation_service/internal/mocks/fact_retriever_mock.go -package mocks -typed nirvanatech.com/nirvana/rules/facts FactRetriever

// FactRetriever defines the interface for any component that gathers facts for a decision model
// evaluation.
type FactRetriever interface {
	// GetFactsForObject retrieves the necessary data context for a given object ID.
	//
	// - objectID: A unique identifier for the entity being evaluated (e.g., a user ID, policy ID).
	// - ruleParams: An optional map of parameters that can guide the fact-gathering process.
	GetFactsForObject(ctx context.Context, objectID string, ruleParams map[string]string) (FactContext, error)
}
