package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/volatiletech/sqlboiler/v4/boil"

	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_models/rules"
	"nirvanatech.com/nirvana/rules/models"
)

// PostgresStore implements the EvaluationStore using SQLBoiler's generated code.
type PostgresStore struct {
	db db_api.NirvanaRW
}

var _ EvaluationStore = &PostgresStore{}

func newPostgresStore(db db_api.NirvanaRW) *PostgresStore {
	return &PostgresStore{db: db}
}

// SaveRecord converts the application model to a SQLBoiler model and inserts it.
func (s *PostgresStore) SaveRecord(ctx context.Context, record *models.EvaluationRecord) error {
	dbRecord, err := evaluationRecordToDb(record)
	if err != nil {
		return fmt.Errorf("could not convert app model to db model: %w", err)
	}

	err = dbRecord.Insert(ctx, s.db, boil.Infer())
	if err != nil {
		return fmt.Errorf("failed to insert record: %w", err)
	}
	return nil
}

// AddFeedback fetches a record, updates its feedback field, and saves it back.
// If feedback is already present, it will be overwritten.
func (s *PostgresStore) AddFeedback(ctx context.Context, evaluationID uuid.UUID, feedback models.EvaluationFeedback) error {
	dbRecord, err := rules.FindEvaluationRecord(ctx, s.db, evaluationID.String())
	if err != nil {
		return fmt.Errorf("failed to find record with ID '%s': %w", evaluationID, err)
	}

	if feedback.Timestamp.IsZero() {
		feedback.Timestamp = time.Now().UTC()
	}
	feedbackJSON, err := json.Marshal(feedback)
	if err != nil {
		return fmt.Errorf("failed to marshal feedback: %w", err)
	}
	dbRecord.Feedback.SetValid(feedbackJSON)

	_, err = dbRecord.Update(ctx, s.db, boil.Whitelist(rules.EvaluationRecordColumns.Feedback))
	if err != nil {
		return fmt.Errorf("failed to update record with feedback: %w", err)
	}
	return nil
}

// GetRecordByID fetches a record and converts it back to the application model.
func (s *PostgresStore) GetRecordByID(ctx context.Context, id uuid.UUID) (*models.EvaluationRecord, error) {
	dbRecord, err := rules.FindEvaluationRecord(ctx, s.db, id.String())
	if err != nil {
		return nil, fmt.Errorf("failed to find record with ID '%s': %w", id, err)
	}
	return evaluationRecordFromDb(dbRecord)
}
