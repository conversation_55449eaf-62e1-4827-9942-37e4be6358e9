package flags_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/infra/fx/testloader"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/safety/flags"

	"github.com/samsarahq/go/snapshotter"
)

// These DOT numbers were selected as we have downloaded CAB reports of all of these DOTs for comparison
// Additionally see also https://docs.google.com/spreadsheets/d/1FM0RWwFN65JREhChcPTjf6koG4KggSnJ2bO-CB27iFA/edit?usp=sharing
var dotNumbers = []int64{
	875382, 1339590, 652182, 1282568, 290471, 1441340, 505638, 1338868, 3478389, 300040, 126911, 506489, 58264, 184628, 1380680, 2468174,
}

func TestFlagFactory(t *testing.T) {
	var env struct {
		fx.In
		Factory *flags.Factory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	for _, dot := range dotNumbers {
		actualFlags, err := env.Factory.GetFlags(ctx, dot)
		assert.NoError(t, err)
		assert.Empty(t, actualFlags, "expecting zero flags since we do not yet have FMCSA fixture data for flags and recommendations")
	}
}

func TestMockFlags(t *testing.T) {
	var env struct {
		fx.In
		Factory *flags.Factory
		Snap    *snapshotter.Snapshotter
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	actualFlags, err := env.Factory.GetFlags(ctx, 123456)
	assert.NoError(t, err)
	env.Snap.Snapshot("Mock Flags", actualFlags)
}
