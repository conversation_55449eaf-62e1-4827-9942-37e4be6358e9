package flags

import (
	"context"
	"database/sql"
	"fmt"
	"sort"
	"strconv"

	fmcsa_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/fmcsa/models"
	"nirvanatech.com/nirvana/safety"
	"nirvanatech.com/nirvana/safety/common"
	"nirvanatech.com/nirvana/safety/enums"
)

type RelatedEntitiesFlagEngine struct {
	fmcsaWrapper fmcsa_wrapper.DataWrapper
}

func NewRelatedEntities(fmcsaWrapper fmcsa_wrapper.DataWrapper) *RelatedEntitiesFlagEngine {
	return &RelatedEntitiesFlagEngine{
		fmcsaWrapper: fmcsaWrapper,
	}
}

func (r RelatedEntitiesFlagEngine) Name() string {
	return "related_entities"
}

func (r RelatedEntitiesFlagEngine) GetFlags(ctx context.Context, dotNumber int64) ([]safety.Flag, error) {
	dotDetails, err := common.FetchDotDetails(ctx, r.fmcsaWrapper, dotNumber)
	if err != nil {
		return nil, err
	}
	contactFlags, err := getRelatedByContactFlags(ctx, r.fmcsaWrapper, dotDetails)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get contact related flags")
	}
	// Hiding relatedByVins flag for now till we ensure data consistency
	return contactFlags, nil
}

func getRelatedByContactFlags(ctx context.Context, fmcsaWrapper fmcsa_wrapper.DataWrapper, dotDetails *models.DotDetails) ([]safety.Flag, error) {
	relatedByContact := make(map[string]map[string]bool)
	fetcher := func(relatedBy, category string, value func() *string) error {
		id := value()
		if id != nil && *id != "" {
			dots, err := fmcsaWrapper.FetchRelatedEntities(ctx, relatedBy, *id)
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return nil
				}
				return errors.Wrapf(err, "Failed to fetch related by %s", relatedBy)
			}
			if len(dots) == 0 {
				return nil
			}
			for _, dot := range dots[0].DotNumbers {
				if dot == strconv.FormatInt(dotDetails.DotNumber, 10) {
					continue
				}
				m, ok := relatedByContact[dot]
				if !ok {
					relatedByContact[dot] = make(map[string]bool)
					m = relatedByContact[dot]
				}
				m[category] = true
			}
		}
		return nil
	}
	if err := fetcher("telephone_number", "contact", func() *string {
		return dotDetails.Census.Telephone
	}); err != nil {
		return nil, err
	}
	if err := fetcher("cellphone_number", "contact", func() *string {
		return dotDetails.Census.Cellphone
	}); err != nil {
		return nil, err
	}
	if err := fetcher("fax_number", "contact", func() *string {
		return dotDetails.Census.Fax
	}); err != nil {
		return nil, err
	}
	if err := fetcher("email_address", "email", func() *string {
		return dotDetails.Census.Email
	}); err != nil {
		return nil, err
	}
	if err := fetcher("physical_address", "address", func() *string {
		census := &dotDetails.Census
		if census.PhysicalAddressStreet == nil || census.PhysicalAddressCity == nil {
			return nil
		}
		return pointer_utils.String(
			fmt.Sprintf(
				"%s, %s",
				*census.PhysicalAddressStreet, *census.PhysicalAddressCity,
			),
		)
	}); err != nil {
		return nil, err
	}
	var table common.Table
	table.Records = append(table.Records, []string{"DOT #", "Name", "Shared Phone", "Shared Address", "Shared Email"})
	for dot, attribute := range relatedByContact {
		dotNo, err := strconv.ParseInt(dot, 10, 64)
		if err != nil {
			log.Error(ctx, "Failed to parse DOT number", log.Err(err), log.String("dot", dot))
			return nil, errors.Wrapf(err, "Failed to parse DOT number")
		}
		details, err := common.FetchDotDetails(ctx, fmcsaWrapper, dotNo)
		if err != nil {
			return nil, errors.Wrapf(err, "Failed to fetch DOT details")
		}
		dotName := details.Name
		table.Records = append(table.Records, []string{
			dot, dotName, yesOrNo(attribute["contact"]), yesOrNo(attribute["address"]), yesOrNo(attribute["email"]),
		})
	}
	sort.Slice(table.Records[1:], func(i, j int) bool {
		return table.Records[i+1][0] < table.Records[j+1][0]
	})
	if len(table.Records) > 1 {
		return []safety.Flag{
			{
				Id:       "related-entity-by-contact",
				Severity: enums.Moderate,
				Issue: &common.Issue{
					Sentence: fmt.Sprintf("Motor Carrier may have a relationship with %d other entities "+
						"due to shared contact details.", len(table.Records)-1),
				},
				Description: "This motor carrier may have a relationship with the following entities due to shared details: ",
				Category:    enums.RelatedEntities,
				Chart: &common.Chart{
					Title: pointer_utils.String("Related entities"),
					Table: &table,
				},
			},
		}, nil
	}
	return nil, nil
}

func yesOrNo(b bool) string {
	if b {
		return "Yes"
	}
	return "-"
}

var _ Engine = &RelatedEntitiesFlagEngine{}
