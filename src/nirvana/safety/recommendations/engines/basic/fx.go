package basic

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/infra/fx/fxregistry"
)

func init() {
	fxregistry.Register(fx.Provide(
		NewControlledSubstances,
		NewDriverFitness,
		NewHazmatCompliance,
		NewHOSCompliance,
		NewUnsafeDriving,
		NewVehicleMaintenance,
		New[*ControlledSubstances],
		New[*DriverFitness],
		New[*HazmatCompliance],
		New[*HOSCompliance],
		New[*UnsafeDriving],
		New[*VehicleMaintenance],
	))
}
