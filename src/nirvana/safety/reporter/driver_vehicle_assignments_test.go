package reporter_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/safety/reporter"
	"nirvanatech.com/nirvana/servers/telematicsv2"
)

var stat1 = &reporter.DriverVehicleAssignment{
	Vehicle: &telematicsv2.Vehicle{
		Vin:   "Vin1",
		TspId: "V1",
	},
	Driver: &telematicsv2.Driver{
		SourceId: "D1",
	},
	AssignedInterval: time_utils.NewYearAndMonth(2022, 3).Interval(),
}

var stat2 = &reporter.DriverVehicleAssignment{
	Vehicle: &telematicsv2.Vehicle{
		Vin:   "Vin2",
		TspId: "V2",
	},
	Driver: &telematicsv2.Driver{
		SourceId: "D2",
	},
	AssignedInterval: time_utils.NewYearAndMonth(2022, 1).Interval(),
}

var stat3 = &reporter.DriverVehicleAssignment{
	Vehicle: &telematicsv2.Vehicle{
		Vin:   "Vin3",
		TspId: "V3",
	},
	Driver: &telematicsv2.Driver{
		SourceId: "D3",
	},
	AssignedInterval: time_utils.NewYearAndMonth(2022, 2).Interval(),
}

var stat4 = &reporter.DriverVehicleAssignment{
	Vehicle: &telematicsv2.Vehicle{
		Vin:   "Vin1",
		TspId: "V1",
	},
	Driver: &telematicsv2.Driver{
		SourceId: "D2",
	},
	AssignedInterval: time_utils.NewYearAndMonth(2022, 4).Interval(),
}

var stat5 = &reporter.DriverVehicleAssignment{
	Vehicle: &telematicsv2.Vehicle{
		Vin:   "Vin1",
		TspId: "V5", // We expect V1 to be sorted before V5_Duplicate_Vin, so this should not be used.
	},
	Driver: &telematicsv2.Driver{
		SourceId: "D5",
	},
	AssignedInterval: time_utils.NewYearAndMonth(2024, 1).Interval(),
}
var stats = reporter.DriverVehicleAssignments{stat1, stat2, stat3, stat4, stat5}

func TestSortsVehicleDriverAssignmentStatsByStatMonth(t *testing.T) {
	expected := reporter.DriverVehicleAssignments{
		stat2,
		stat3,
		stat1,
		stat4,
		stat5,
	}
	stats.SortByTimestamp()
	assert.Equal(t, expected, stats)
}

func TestGroupsVehicleDriverAssignmentStatsByVehicleID(t *testing.T) {
	expected := map[string]reporter.DriverVehicleAssignments{
		"V1": {stat1, stat4},
		"V2": {stat2},
		"V3": {stat3},
		"V5": {stat5},
	}
	result := stats.ByVehicleID()
	assert.Equal(t, expected, result)
}

func TestGroupsVehicleDriverAssignmentStatsByDriverID(t *testing.T) {
	expected := map[string]reporter.DriverVehicleAssignments{
		"D1": {stat1},
		"D2": {stat2, stat4},
		"D3": {stat3},
		"D5": {stat5},
	}
	result := stats.ByDriverID()
	assert.Equal(t, expected, result)
}

func TestFiltersVehicleDriverAssignmentStatsByIntersectingInterval(t *testing.T) {
	testcases := []struct {
		name     string
		interval time_utils.Interval
		expected reporter.DriverVehicleAssignments
	}{
		{
			name: "no stats in interval ending on start time of first stat",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.Start.Add(-24 * time.Hour),
				End:   stat2.AssignedInterval.Start,
			},
			expected: nil,
		},
		{
			name: "first stat is returned with interval intersecting start time",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.Start.Add(-24 * time.Hour),
				End:   stat2.AssignedInterval.Start.Add(time.Nanosecond),
			},
			expected: []*reporter.DriverVehicleAssignment{stat2},
		},
		{
			name: "first stat is returned with interval exactly covering first month",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.Start,
				End:   stat2.AssignedInterval.End,
			},
			expected: []*reporter.DriverVehicleAssignment{stat2},
		},
		{
			name: "first stat is returned with interval ending on start time of second stat",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.Start,
				End:   stat3.AssignedInterval.Start,
			},
			expected: []*reporter.DriverVehicleAssignment{stat2},
		},
		{
			name: "no stats are returned for interval exactly between two months",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.End,
				End:   stat3.AssignedInterval.Start,
			},
			expected: nil,
		},
		{
			name: "first and second stats are returned with interval intersecting both time ranges",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.End.Add(-time.Nanosecond),
				End:   stat3.AssignedInterval.Start.Add(time.Nanosecond),
			},
			expected: []*reporter.DriverVehicleAssignment{stat2, stat3},
		},
		{
			name: "second stat is returned with interval spanning exact time range",
			interval: time_utils.Interval{
				Start: stat3.AssignedInterval.Start,
				End:   stat3.AssignedInterval.End,
			},
			expected: []*reporter.DriverVehicleAssignment{stat3},
		},
		{
			name: "second stat is returned with interval internal to time range",
			interval: time_utils.Interval{
				Start: stat3.AssignedInterval.Start.Add(time.Hour),
				End:   stat3.AssignedInterval.Start.Add(2 * time.Hour),
			},
			expected: []*reporter.DriverVehicleAssignment{stat3},
		},
		{
			name: "first three stats are returned with interval intersecting all 3",
			interval: time_utils.Interval{
				Start: stat3.AssignedInterval.Start.Add(-2 * time.Nanosecond),
				End:   stat3.AssignedInterval.End.Add(2 * time.Nanosecond),
			},
			expected: []*reporter.DriverVehicleAssignment{stat2, stat3, stat1},
		},
		{
			name: "no stats in interval after last stat",
			interval: time_utils.Interval{
				Start: time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2022, 5, 30, 0, 0, 0, 0, time.UTC),
			},
			expected: nil,
		},
		{
			name: "all stats returned with interval covering superset of range",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.Start.Add(-24 * time.Hour),
				End:   stat4.AssignedInterval.End.Add(24 * time.Hour),
			},
			expected: []*reporter.DriverVehicleAssignment{stat2, stat3, stat1, stat4},
		},
		{
			name: "all stats returned with interval covering exact time range",
			interval: time_utils.Interval{
				Start: stat2.AssignedInterval.Start,
				End:   stat4.AssignedInterval.End,
			},
			expected: []*reporter.DriverVehicleAssignment{stat2, stat3, stat1, stat4},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			result := stats.Intersecting(testcase.interval)
			assert.Equal(t, testcase.expected, result)
		})
	}
}

func TestSumsAssignedDurationOfVehicleDriverAssignmentStats(t *testing.T) {
	byVin := stats.ByVIN()
	assert.Equal(t, 151*24*time.Hour, stats.SumDuration())
	assert.Equal(t, 61*24*time.Hour, stats.ByVehicleID()["V1"].SumDuration())
	assert.Equal(t, 31*24*time.Hour, stats.ByVehicleID()["V2"].SumDuration())
	assert.Equal(t, time.Duration(0), stats.ByVIN()["Vin1"].SumDuration())
	assert.Equal(t, 61*24*time.Hour, byVin["VIN1"].SumDuration())
}
