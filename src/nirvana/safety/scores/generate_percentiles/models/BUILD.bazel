load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "models",
    srcs = [
        "iss_data.go",
        "percentile_data.go",
    ],
    importpath = "nirvanatech.com/nirvana/safety/scores/generate_percentiles/models",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/fmcsa/basic",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "models_test",
    srcs = ["percentiles_test.go"],
    deps = [
        ":models",
        "//nirvana/common-go/time_utils",
        "//nirvana/fmcsa/basic",
        "//nirvana/safety/scores/percentiles",
    ],
)
