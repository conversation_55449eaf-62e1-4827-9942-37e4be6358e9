load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "manager",
    srcs = [
        "doc.go",
        "fx.go",
        "interfaces.go",
        "objective_grade_manager_impl.go",
        "objective_grade_manager_mock.go",
    ],
    importpath = "nirvanatech.com/nirvana/safety/scores/objective_grade/manager",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/safety/scores/objective_grade/generator",
        "//nirvana/safety/scores/objective_grade/models",
        "//nirvana/safety/scores/weighted_basic_score",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "manager_test",
    srcs = ["objective_grade_manager_impl_test.go"],
    data = glob(["testdata/**"]),
    embedsrcs = [
        "fixture_data/fmcsa_fixture.sql",
        "fixture_data/inspections_fixture.sql",
    ],
    deps = [
        ":manager",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/safety/scores/objective_grade/models",
        "@com_github_samsarahq_go//snapshotter",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
