//go:build db_test
// +build db_test

package tests

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/samsarahq/go/snapshotter"
	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/common-go/postgres_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/feature_store/persistent"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/fmcsa/models"
	"nirvanatech.com/nirvana/safety/scores"
	"nirvanatech.com/nirvana/safety/scores/projections"
)

func Test_Filtered(t *testing.T) {
	ctx := context.Background()
	nirvanaDb, err := postgres_utils.NewNirvanaTestDB()
	assert.NoError(t, err)
	fmcsaRwDb, err := postgres_utils.NewFmcsaRwTestDB()
	assert.NoError(t, err)
	fmcsaRoDb, err := postgres_utils.NewFmcsaRoTestDB()
	assert.NoError(t, err)
	fmcsaWrapper := fmcsa.NewFmcsaDataWrapperImpl(fmcsaRwDb, fmcsaRoDb)
	featureStore := persistent.NewStore(nirvanaDb)

	dots := []int64{184668, 184628, 875382, 505638, 1380680, 2011161, 1282568, 1339590, 652182, 345951, 300440, 506489, 2071131, 2561140, 1343831, 81055, 34278, 854701, 398881, 535932, 1064753, 2468174, 2525894, 1441340, 2381294, 290471}
	result := make(map[string]map[int64]string)
	for _, dot := range dots {
		input, err := projections.FetchProjectionsInput(ctx, fmcsaWrapper, featureStore, dot, scores.Date_2021_01_29, scores.Date_2021_12_31)
		assert.NoError(t, err)
		assert.Len(t, input.DataSnapshots, 12)
		for _, category := range basic.BASICs() {
			if category == basic.CrashIndicator {
				continue
			}
			projs, err := projections.Filtered(
				projections.NewConfig(
					projections.Category(category),
					projections.RemedialActionTakenFrom(scores.Date_2021_08_27),
					projections.MultiplyViolationTransform(func(record models.ViolationRecord) *models.ViolationRecord {
						return nil
					}),
				),
				input,
				scores.Date_2021_07_30, scores.Date_2021_12_31,
			)
			assert.NoError(t, err)
			assert.Len(t, projs, 6)

			var percentiles []string
			for _, projection := range projs {
				if !projection.IsConclusivePercentile() {
					percentiles = append(percentiles, "--")
					continue
				}
				_, percentile, err := scores.PercentileForMeasure(projection)
				assert.NoError(t, err)
				percentiles = append(percentiles, fmt.Sprintf("%02.f", percentile))
			}

			if result[category.Name()] == nil {
				result[category.Name()] = make(map[int64]string)
			}
			result[category.Name()][dot] = strings.Join(percentiles, ", ")
		}
	}
	snap := snapshotter.New(t)
	defer snap.Verify()
	snap.Snapshot("filtered", result)
}
