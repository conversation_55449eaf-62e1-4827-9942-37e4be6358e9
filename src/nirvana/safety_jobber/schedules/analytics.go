package schedules

import (
	safety_jobs "nirvanatech.com/nirvana/safety/jobs"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/jobber/jtypes"
)

func init() {
	addSchedule(safety_jobs.UserAnalyticSyncPropertiesJob, SyncUserProperties)
}

func SyncUserProperties() jtypes.AddJobRunParams {
	return jtypes.NewAddJobRunParams(
		safety_jobs.UserAnalyticSyncPropertiesJob,
		&safety_jobs.UserAnalyticsSyncPropertiesMessage{},
		jtypes.NewMetadata(jtypes.Scheduled),
	).WithSchedule(jtypes.NewCronSchedule(
		"0 0 6 * * 1", // Every Monday at 6:00am UTC -> 11:00pm PT, 02:00am ET
		null.Int{},
	))
}
