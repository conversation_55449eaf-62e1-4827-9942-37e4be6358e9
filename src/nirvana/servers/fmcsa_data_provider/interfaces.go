package fmcsa_data_provider

import (
	"context"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/crashes"
	"nirvanatech.com/nirvana/fmcsa/datagov/models"
)

//go:generate go run go.uber.org/mock/mockgen -destination=wrapper_mock.go -mock_names=DatagovCrashesWrapperI=MockDatagovCrashesWrapper -package=fmcsa_data_provider nirvanatech.com/nirvana/servers/fmcsa_data_provider DatagovCrashesWrapperI
type DatagovCrashesWrapperI interface {
	GetByDOTAndInterval(
		ctx context.Context,
		dotNumber int64,
		includeDeleted bool,
		timeInterval time_utils.Interval,
	) (models.Crashes, error)
}

var _ DatagovCrashesWrapperI = (*crashes.DataWrapper)(nil)
