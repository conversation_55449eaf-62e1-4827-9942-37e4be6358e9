load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_library(
    name = "main",
    srcs = ["main.go"],
    importpath = "nirvanatech.com/nirvana/servers/quote_scraper/cmd",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/infra/config",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/appfx",
        "//nirvana/servers/quote_scraper",
        "@io_opentelemetry_go_otel_sdk//trace",
        "@org_uber_go_fx//:fx",
    ],
)

go_binary(
    name = "quote_scraper",
    embed = [":main"],
    visibility = ["//visibility:public"],
)

go_test(
    name = "cmd_test",
    srcs = ["main_test.go"],
    # keep
    embed = [":main"],
    deps = [
        "//nirvana/infra/config",
        "@com_github_stretchr_testify//require",
    ],
)
