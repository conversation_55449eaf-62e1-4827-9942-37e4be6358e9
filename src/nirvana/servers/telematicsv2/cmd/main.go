package main

import (
	"flag"
	"fmt"

	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.uber.org/fx"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/common-go/grpc/middleware"
	"nirvanatech.com/nirvana/common-go/metrics"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/appfx"
	"nirvanatech.com/nirvana/servers/telematicsv2"
)

const (
	servicePort = 9091
)

func main() {
	var port int
	flag.IntVar(
		&port, "port", servicePort, "Port on which to start grpc server",
	)
	app := NewAppBuilder(config.CurrentEnv(), fmt.Sprintf(":%d", port)).Build()
	app.Run()
}

func NewAppBuilder(env config.Env, addr string) appfx.Builder {
	metricsClient, err := metrics.NewClient(string(constants.TelematicsGRPCServer))
	if err != nil {
		panic(err)
	}
	unaryInterceptors := []grpc.UnaryServerInterceptor{
		middleware.RequestIDUnaryServerInterceptor(),
	}
	unaryInterceptors = append(unaryInterceptors, middleware.DefaultServerUnaryInterceptors(metricsClient)...)

	// Stream interceptors: default metrics and others
	streamInterceptors := middleware.DefaultServerStreamInterceptors(metricsClient)

	serverOptions := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(unaryInterceptors...),
		grpc.ChainStreamInterceptor(streamInterceptors...),
	}

	return appfx.NewGRPCAppBuilder(
		constants.TelematicsGRPCServer,
		env,
		addr,
		serverOptions,
		fxOptions()...,
	)
}

func fxOptions(extra ...fx.Option) []fx.Option {
	return append(extra,
		// Register the gRPC service to the server
		fx.Invoke(
			telematicsv2.RegisterTelematicsPipelineManagerServer,
		),
		// We use a more potent trace sampler for this service as this is a relatively new service
		fx.Decorate(func() sdktrace.Sampler {
			return sdktrace.ParentBased(sdktrace.TraceIDRatioBased(0.2))
		}),
	)
}
