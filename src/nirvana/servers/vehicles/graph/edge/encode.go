package edge

import (
	"context"
	"strings"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/servers/vehicles/graph/property"
)

func encodeEdgeNoArrows(e *Edge, allProperties bool) (string, error) {
	alias, err := e.<PERSON><PERSON>()
	if err != nil {
		return "", err
	}
	properties := e.Properties()
	if !allProperties {
		var err error
		properties, err = e.Type.PrimaryKey().Select(e.Properties())
		if err != nil {
			return "", err
		}
	}
	properties[property.Type] = e.Type.String()

	var s strings.Builder
	s.WriteString("[")
	s.WriteString(alias)
	s.WriteString(":")
	s.WriteString(e.Type.Relationship().String())
	encodedProperties, err := properties.Encode()
	if err != nil {
		return "", err
	}

	if len(properties) > 0 {
		s.WriteString(" ")
		s.WriteString(encodedProperties)
	}
	s.WriteString("]")
	return s.String(), nil
}

// encodeEdge encodes a string of the form (n1)-[:DECORATES]->(n2)
func encodeEdge(e *Edge, allProperties bool) (string, error) {
	sourceAlias, err := e.Source.<PERSON>as()
	if err != nil {
		return "", err
	}
	destAlias, err := e.Destination.Alias()
	if err != nil {
		return "", err
	}
	edgeKey, err := encodeEdgeNoArrows(e, allProperties)
	if err != nil {
		return "", err
	}

	var s strings.Builder
	s.WriteString("(" + sourceAlias + ")")
	s.WriteString("-")
	s.WriteString(edgeKey)
	s.WriteString("->")
	s.WriteString("(" + destAlias + ")")

	return s.String(), nil
}

// EncodeAllProperties encodes this edge in its OpenCypher
// string form using all properties, including those that
// are not part of our primary key.
func (e *Edge) EncodeAllProperties() (string, error) {
	return encodeEdge(e, true)
}

// EncodePrimaryKey encodes this edge in its OpenCypher
// string form using only primary key properties.
func (e *Edge) EncodePrimaryKey() (string, error) {
	return encodeEdge(e, false)
}

// EncodeNoArrows encodes this edge in its OpenCypher
// string form without the attached arrows for the source
// and destination nodes. For example, we encode "[edge]"
// rather than "(source)-[edge]-(destination)".
func (e *Edge) EncodeNoArrows() (string, error) {
	return encodeEdgeNoArrows(e, false)
}

// String prints out the primary key representation of this edge.
func (e *Edge) String() string {
	retval, err := e.EncodePrimaryKey()
	if err != nil {
		log.DPanic(context.Background(), "Failed to encode edge",
			log.Err(err), log.Any("edge", e))
	}
	return retval
}
