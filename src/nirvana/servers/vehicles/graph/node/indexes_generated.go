// Code generated by genschema. DO NOT EDIT!

package node

import "nirvanatech.com/nirvana/infra/config"

// primaryKeyIndexes returns composite indexes for all of our primary keys. This is in
// contrast to primaryKeyConstraints(), which returns unique constraints and disallows
// duplicated nodes from being inserted. We are forced to use composite indexes for
// unit test environments, due to the way we are namespacing our nodes with multiple labels.
func primaryKeyIndexes() []string {
	return []string {
		`CREATE INDEX index_node_agency IF NOT EXISTS
			FOR (n:agency) ON (n.agency_id)`,
		`CREATE INDEX index_node_fleet IF NOT EXISTS
			FOR (n:fleet) ON (n.fleet_id)`,
		`CREATE INDEX index_node_application IF NOT EXISTS
			FOR (n:application) ON (n.application_id)`,
		`CREATE INDEX index_node_equipment_list IF NOT EXISTS
			FOR (n:equipment_list) ON (n.equipment_list_id)`,
		`CREATE INDEX index_node_equipment_list_timeseries IF NOT EXISTS
			FOR (n:equipment_list_timeseries) ON (n.equipment_list_id, n.time)`,
		`CREATE INDEX index_node_equipment_vehicle IF NOT EXISTS
			FOR (n:equipment_vehicle) ON (n.equipment_list_id, n.vin)`,
		`CREATE INDEX index_node_equipment_vehicle_timeseries IF NOT EXISTS
			FOR (n:equipment_vehicle_timeseries) ON (n.equipment_list_id, n.vin, n.time)`,
		`CREATE INDEX index_node_vin_vehicle IF NOT EXISTS
			FOR (n:vin_vehicle) ON (n.vin)`,
		`CREATE INDEX index_node_vin_decoder_problem IF NOT EXISTS
			FOR (n:vin_decoder_problem) ON (n.vin)`,
		`CREATE INDEX index_node_vin_decoder_solution IF NOT EXISTS
			FOR (n:vin_decoder_solution) ON (n.vin)`,
		`CREATE INDEX index_node_telematics_connection IF NOT EXISTS
			FOR (n:telematics_connection) ON (n.handle_id)`,
		`CREATE INDEX index_node_telematics_connection_snapshot IF NOT EXISTS
			FOR (n:telematics_connection_snapshot) ON (n.handle_id, n.time)`,
		`CREATE INDEX index_node_telematics_tag IF NOT EXISTS
			FOR (n:telematics_tag) ON (n.handle_id, n.tag_id)`,
		`CREATE INDEX index_node_telematics_tag_snapshot IF NOT EXISTS
			FOR (n:telematics_tag_snapshot) ON (n.handle_id, n.tag_id, n.time)`,
		`CREATE INDEX index_node_telematics_vehicle IF NOT EXISTS
			FOR (n:telematics_vehicle) ON (n.handle_id, n.vehicle_id)`,
		`CREATE INDEX index_node_telematics_vehicle_snapshot IF NOT EXISTS
			FOR (n:telematics_vehicle_snapshot) ON (n.handle_id, n.vehicle_id, n.time)`,
    }
}


// primaryKeyConstraints returns primary key unique constraints for all of our primary keys. This is
// intended for dev and production environments, where we don't have multiple 'env' labels for nodes.
func primaryKeyConstraints() []string {
	return []string {
		`CREATE CONSTRAINT constraint_unique_node_agency IF NOT EXISTS
			FOR (n:agency) REQUIRE (n.agency_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_fleet IF NOT EXISTS
			FOR (n:fleet) REQUIRE (n.fleet_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_application IF NOT EXISTS
			FOR (n:application) REQUIRE (n.application_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_equipment_list IF NOT EXISTS
			FOR (n:equipment_list) REQUIRE (n.equipment_list_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_equipment_list_timeseries IF NOT EXISTS
			FOR (n:equipment_list_timeseries) REQUIRE (n.equipment_list_id, n.time) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_equipment_vehicle IF NOT EXISTS
			FOR (n:equipment_vehicle) REQUIRE (n.equipment_list_id, n.vin) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_equipment_vehicle_timeseries IF NOT EXISTS
			FOR (n:equipment_vehicle_timeseries) REQUIRE (n.equipment_list_id, n.vin, n.time) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_vin_vehicle IF NOT EXISTS
			FOR (n:vin_vehicle) REQUIRE (n.vin) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_vin_decoder_problem IF NOT EXISTS
			FOR (n:vin_decoder_problem) REQUIRE (n.vin) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_vin_decoder_solution IF NOT EXISTS
			FOR (n:vin_decoder_solution) REQUIRE (n.vin) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_telematics_connection IF NOT EXISTS
			FOR (n:telematics_connection) REQUIRE (n.handle_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_telematics_connection_snapshot IF NOT EXISTS
			FOR (n:telematics_connection_snapshot) REQUIRE (n.handle_id, n.time) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_telematics_tag IF NOT EXISTS
			FOR (n:telematics_tag) REQUIRE (n.handle_id, n.tag_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_telematics_tag_snapshot IF NOT EXISTS
			FOR (n:telematics_tag_snapshot) REQUIRE (n.handle_id, n.tag_id, n.time) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_telematics_vehicle IF NOT EXISTS
			FOR (n:telematics_vehicle) REQUIRE (n.handle_id, n.vehicle_id) IS UNIQUE`,
		`CREATE CONSTRAINT constraint_unique_node_telematics_vehicle_snapshot IF NOT EXISTS
			FOR (n:telematics_vehicle_snapshot) REQUIRE (n.handle_id, n.vehicle_id, n.time) IS UNIQUE`,
    }
}

// Indexes returns unique primary key constraints for our nodes in production environments,
// or standard composite indexes for unit test environments. We use constraints only for dev
// and prod, since the constraints are violated in unit test environments where we have a
// separate 'env' label to namespace our nodes. It is unfortunately not possible to create
// a constraint against multiple labels, so we are forced to create composite indexes for
// unit test environments.
func Indexes(env config.Env) []string {
    if env != config.Env_PROD {
        return primaryKeyIndexes()
    }
    return primaryKeyConstraints()
}

