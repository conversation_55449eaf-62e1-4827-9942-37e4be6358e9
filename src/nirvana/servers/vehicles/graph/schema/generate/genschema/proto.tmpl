// Code generated by genschema. DO NOT EDIT!

syntax = "proto3";

import "google/protobuf/timestamp.proto";

package vehicles_service;

/*
 Graph Nodes
 */
{{- range $node := .Nodes }}
message {{ $node.Name }}Node {
  message PrimaryKey {
    {{- range $i, $field := $node.Fields.PrimaryKeyFields }}
      {{ $field.ProtoType }} {{ $field.Name }} = {{ add $i 1 }};
    {{- end }}
  }
  PrimaryKey Key = 1;
{{- range $i, $field := $node.Fields.PropertyFields }}
  {{ $field.ProtoType }} {{ $field.Name }} = {{ add $i 2 }};
{{- end }}
}
{{ end }}
message Node {
  oneof Selected {
  {{- range $i, $node := .Nodes }}
    {{ $node.Name }}Node {{ $node.Name }} = {{ add $i 1 }};
  {{- end }}
  }
}

/*
 Graph Edges
 */
{{- range $edge := .Edges }}
message {{ $edge.Name }}Edge {
  message PrimaryKey {
    {{ $edge.Source.Name }}Node source = 1;
    {{ $edge.Destination.Name }}Node destination = 2;
  {{- range $i, $field := $edge.Fields.PrimaryKeyFields }}
    {{ $field.ProtoType }} {{ $field.Name }} = {{ add $i 3 }};
  {{- end }}
  }
  PrimaryKey Key = 1;
{{- range $i, $field := $edge.Fields.PropertyFields }}
  {{ $field.ProtoType }} {{ $field.Name }} = {{ add $i 2 }};
{{- end }}
}
{{ end }}
message Edge {
  oneof Selected {
  {{- range $i, $edge := .Edges }}
    {{ $edge.Name }}Edge {{ $edge.Name }} = {{ add $i 1 }};
  {{- end }}
  }
}
