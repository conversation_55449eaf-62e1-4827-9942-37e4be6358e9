package sharing

import (
	"fmt"
	"net/url"
	"path"
	"time"

	"nirvanatech.com/nirvana/common-go/url_util"

	"github.com/google/uuid"

	sharing_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/infra/authz"
)

func NewSafetyReportLink(shareID, serviceAccountUserID, safetyReportID, createdBy uuid.UUID, now time.Time, expiresAt *time.Time,
) sharing_wrapper.ShareableLink {
	return sharing_wrapper.ShareableLink{
		ID:                   shareID,
		ServiceAccountUserID: serviceAccountUserID,
		ResourceID:           safetyReportID,
		LinkType:             sharing_wrapper.SafetyReportSharedLink,
		CreatedAt:            now,
		CreatedBy:            createdBy.String(),
		ExpiresAt:            expiresAt,
	}
}

func NewSafetyReportSharingRole(safetyReportID, userID uuid.UUID) authz.SharingRole {
	return authz.SharingRole{
		ID:            uuid.New(),
		ResourceID:    safetyReportID,
		ResourceType:  authz.SafetyReport,
		PrincipalID:   userID,
		PrincipalType: authz.UserPrincipal,
		Role:          authz.SharedSafetyReaderRole,
		Domain:        fmt.Sprintf("/safety/reports/%s", safetyReportID),
		CreatedAt:     time.Now(),
		UpdatedAt:     nil,
	}
}

func SafetyReportSharedURL(safetyReportID, shareID uuid.UUID, medium, source string) *url.URL {
	q := url.Values{}
	q.Set("shareid", shareID.String())
	return url_util.URLWithUTM(&url.URL{
		Scheme:   "https",
		Host:     "safety.nirvanatech.com",
		Path:     fmt.Sprintf("/%s/overview", safetyReportID),
		RawQuery: q.Encode(),
	}, medium, source)
}

func formatSignUpURI[T uint64 | int64 | string](
	pathPrefix string,
	shareID uuid.UUID,
	dotNumber T,
	fleetName string,
	medium, source string,
) *url.URL {
	q := url.Values{}
	q.Set("fleetName", fleetName)
	q.Set("dotNumber", fmt.Sprintf("%v", dotNumber))
	return url_util.URLWithUTM(&url.URL{
		Scheme:   "https",
		Host:     "safety.nirvanatech.com",
		Path:     path.Join(pathPrefix, shareID.String()),
		RawQuery: q.Encode(),
	}, medium, source)
}

func UserSignUpFromTelematicsURI[T uint64 | int64 | string](
	shareID uuid.UUID,
	dotNumber T,
	fleetName string,
	medium, source string,
) *url.URL {
	return formatSignUpURI("/telematics-sign-up", shareID, dotNumber, fleetName, medium, source)
}

func UserSignUpURI[T uint64 | int64 | string](
	shareID uuid.UUID,
	dotNumber T,
	fleetName string,
	medium, source string,
) *url.URL {
	return formatSignUpURI("/sign-up", shareID, dotNumber, fleetName, medium, source)
}
