const { batchParse, terminateParserPool } = require('./transform/batch_parse_interface');
const batchRequest = require('./extract/batch_request_interface');
const db = require('./db/db');
const { maxConsecutiveFailures } = require('./config');

(async () => {

    console.log(`Node version is ${process.version}`)

    const pool = db.getPool;

    let consecutiveFailures = 0;
    let failedConsecutiveDotnosArray = [];

    let pendingDotnos = await db.getPendingDotnoBatch(pool);

    while (pendingDotnos.length) {
        let timeStartReq = Date.now();
        let htmlResultsArr = await batchRequest(pendingDotnos);
        let timeEndReq = Date.now();
        console.log(`Requests finished in ~${timeEndReq - timeStartReq}ms.`);

        if (htmlResultsArr[htmlResultsArr.length - 1].history.status === 'Failed' && htmlResultsArr[htmlResultsArr.length - 1].unsafeDriving.status === 'Failed') {
            for (let htmlResult of htmlResultsArr) {
                if (htmlResult.history.status === 'Failed' && htmlResult.unsafeDriving.status === 'Failed') {
                    consecutiveFailures++;
                    failedConsecutiveDotnosArray.push(htmlResult.dotno);
                } else {
                    consecutiveFailures = 0;
                    failedConsecutiveDotnosArray = [];
                }
            }
        } else {
            consecutiveFailures = 0;
            failedConsecutiveDotnosArray = [];
        }

        let timeStartParse = Date.now();
        let parsedResArr = await batchParse(htmlResultsArr);
        let timeEndParse = Date.now();

        console.log(`Batch Parsing finished in ~${timeEndParse - timeStartParse}ms.`);

        for (let parsedResObj of parsedResArr) {
            let res = await db.updateRowInQueue(pool, parsedResObj);
            console.log(`${res.rowCount} queue row(s) updated for ${parsedResObj.dotno}`);
        }

        if (consecutiveFailures >= maxConsecutiveFailures) {
            let rolledBackRows = await db.queueRollbackFailedDotnos(pool, failedConsecutiveDotnosArray);
            console.error(`Max consecutive failures reached (${maxConsecutiveFailures}), source is likely down or has some other serious issue. Process stopped.`);
            console.warn(`${rolledBackRows} rows set back to 'Pending' in queue.`);
            await pool.end();
            await terminateParserPool();
            return null;
        }

        pendingDotnos = await db.getPendingDotnoBatch(pool);
    }

    await pool.end();
    await terminateParserPool();
    console.log('Scraping process finished, no pending dotnos in queue.');
    return;
})();
