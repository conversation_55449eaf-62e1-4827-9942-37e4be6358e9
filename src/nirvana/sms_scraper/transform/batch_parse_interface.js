const workerpool = require('workerpool');
const pool = workerpool.pool(__dirname + '/parse.js')

module.exports = {
    batchParse: async (unparsedObjArr) => {
        let promisesArr = [];
        let unparseableArr = [];

        for (let i = 0; i < unparsedObjArr.length; i++) {
            if (unparsedObjArr[i].history.status === "Success" ||
                unparsedObjArr[i].unsafeDriving.status === "Success") {
                promisesArr.push(pool.exec('parse', [unparsedObjArr[i]]));
            } else {
                unparseableArr.push(unparsedObjArr[i]);
            }
        }

        let parsedObjArr = await Promise.all(promisesArr);

        return [...unparseableArr, ...parsedObjArr];
    },

    terminateParserPool: async () => {
        await pool.terminate();
    }
}