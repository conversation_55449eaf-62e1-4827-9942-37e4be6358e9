load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "commision_rates_new_business_lib",
    srcs = [
        "agency_commision_rates.go",
        "generate_agency_commision_rates.go",
        "template.go",
    ],
    embedsrcs = ["agency_commision_rates.tmpl"],
    importpath = "nirvanatech.com/nirvana/support/pullers/commision_rates_new_business",
    visibility = ["//visibility:private"],
    deps = [
        "//nirvana/common-go/aws_utils",
        "//nirvana/common-go/gworkspace_utils/gsheets_utils",
        "//nirvana/common-go/log",
        "//nirvana/support",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_binary(
    name = "commision_rates_new_business",
    embed = [":commision_rates_new_business_lib"],
    visibility = ["//visibility:public"],
)
