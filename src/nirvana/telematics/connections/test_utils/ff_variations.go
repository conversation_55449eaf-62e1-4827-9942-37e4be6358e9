package test_utils

import (
	_ "embed"
	"encoding/json"

	"nirvanatech.com/nirvana/telematics/connections"
)

// These exported variables define the expected values of the feature flag
// variations configured on LaunchDarkly for the `telematics-consent-flow`
// feature flag. We hand-write these values in the test_utils/data directory,
// as JSON files, and embed them into the binary. We then unmarshal them into
// FeatureFlagVariation structs, and use them in tests.
//
// TODO: Use those JSON files in terraform (using launch-darkly provider)
var (
	FFVariation1 connections.FeatureFlagVariation
	FFVariation2 connections.FeatureFlagVariation
	FFVariation3 connections.FeatureFlagVariation
)

// Doing this within init ensures that the raw files in test_utils/data are
// valid JSON and can be unmarshalled into FeatureFlagVariation structs. In case
// of any error, the program will panic - test failure(s).
func init() {
	if err := json.Unmarshal(ffVariation1Raw, &FFVariation1); err != nil {
		panic(err)
	}
	if err := json.Unmarshal(ffVariation2Raw, &FFVariation2); err != nil {
		panic(err)
	}
	if err := json.Unmarshal(ffVariation3Raw, &FFVariation3); err != nil {
		panic(err)
	}
}

var (
	//go:embed data/featureFlagVariation1.json
	ffVariation1Raw []byte
	//go:embed data/featureFlagVariation2.json
	ffVariation2Raw []byte
	//go:embed data/featureFlagVariation3.json
	ffVariation3Raw []byte
)
