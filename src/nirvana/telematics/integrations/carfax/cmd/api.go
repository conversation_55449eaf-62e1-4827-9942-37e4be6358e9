package cmd

import (
	"github.com/cockroachdb/errors"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	"nirvanatech.com/nirvana/telematics/integrations/carfax"
)

var GetVinInfoCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   "debug",
		Short: "Make direct API calls to carfax sandbox, use only for debugging",
		Long:  "Make sure credentials/config is properly set up on your local",
		Args:  cobra.ExactArgs(1), // expecting exactly one argument (the VIN)
	},
	func(cmd *cobra.Command, args []string, env struct {
		fx.In
		ApiWrapper *carfax.Client
	},
	) error {
		resp, err := env.ApiWrapper.GetVinInfo(cmd.Context(), args[0])
		if err != nil {
			return errors.Wrap(err, "failed to get carfax vin info")
		}
		log.Sugared.Infof("Carfax response for vin %s: %s", args[0], string(resp))
		return nil
	},
)
