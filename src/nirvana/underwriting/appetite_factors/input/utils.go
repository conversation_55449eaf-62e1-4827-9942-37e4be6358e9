package input

import (
	"nirvanatech.com/nirvana/fmcsa/basic"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func GetBasicAlertsCount(basicScoreThresholds externalRef0.BasicScoreThresholds) *BasicAlertCount {
	basicAlertCount, scorePresent := int32(0), false
	for _, threshold := range basicScoreThresholds {
		// Crash Indicator is a category weighted on other categories and looks at no unique data
		if threshold.Category != basic.CrashIndicator.Name() && threshold.Score != nil {
			scorePresent = true
			if *threshold.Score > threshold.Threshold {
				basicAlertCount += 1
			}
		}
	}
	if scorePresent {
		return ToBasicAlertCount(basicAlertCount)
	}
	return ToInconclusiveBasicAlertCount()
}
