package dot_rating

import (
	"context"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"

	"github.com/volatiletech/null/v8"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"github.com/cockroachdb/errors"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"
)

type V1 struct {
	manager                  uw_app_review.ReviewManager
	applicationReviewWrapper uw.ApplicationReviewWrapper
}

func NewV1(manager uw_app_review.ReviewManager, wrapper uw.ApplicationReviewWrapper) *V1 {
	return &V1{
		manager:                  manager,
		applicationReviewWrapper: wrapper,
	}
}

func (v *V1) Name() types.GeneratorVersion {
	return types.GeneratorVersion{
		Name:    DotRatingName,
		Version: DotRatingV1,
	}
}

type V1Result struct {
	Rating               *string
	IsDateWithinTwoYears *bool
}

func (v *V1) Generate(ctx context.Context, args any, data *types.CachedData) (any, error) {
	concreteArgs, ok := args.(*types.GeneratorArgs)
	if !ok {
		return nil, errors.Newf("invalid args: %+v; expected GeneratorArgs type", args)
	}

	reviewId := concreteArgs.AppReviewId
	review, err := v.applicationReviewWrapper.GetReview(ctx, reviewId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get application review for app review ID: %s", reviewId)
	}

	dotRating, err := v.manager.Safety.DotRating.Get(ctx, concreteArgs.AppReviewId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get DOT rating")
	}

	var rating *string
	if dotRating.SafetyRating != nil {
		strVal := string(*dotRating.SafetyRating)
		rating = &strVal
	}

	var isDateWithinTwoYears *bool

	if dotRating.EffectiveDate != nil && !dotRating.EffectiveDate.IsZero() {
		// Check if the dot rating's effective date is within two years of the review's effective date
		if review.EffectiveDate.AddDate(-2, 0, 0).Before(dotRating.EffectiveDate.Time) {
			isDateWithinTwoYears = pointer_utils.ToPointer(true)
		} else {
			isDateWithinTwoYears = pointer_utils.ToPointer(false)
		}
	}

	return V1Result{
		Rating:               rating,
		IsDateWithinTwoYears: isDateWithinTwoYears,
	}, nil
}

func (v *V1) Apply(f models.Fact, result any) error {
	fact, ok := f.(*models.FrontingInputFact)
	if !ok {
		return errors.Newf("invalid fact type: %T; expected FrontingInputFact", f)
	}

	concreteResult, ok := result.(V1Result)
	if !ok {
		return errors.Newf("unexpected result type: %T; expected V1Result", result)
	}

	fact.DotRating = models.DotRating{
		Rating:               null.StringFromPtr(concreteResult.Rating),
		IsDateWithinTwoYears: null.BoolFromPtr(concreteResult.IsDateWithinTwoYears),
	}
	return nil
}
