package common_utils

import (
	"reflect"
	"sort"
	"testing"
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

func Test_buildLossYearMap(t *testing.T) {
	tests := []struct {
		name    string
		arg     *oapi_uw.ApplicationReviewLossSummary
		want    lossYearMap
		wantErr bool
	}{
		{
			name: "Test Loss Year Map with five year data",
			arg: &oapi_uw.ApplicationReviewLossSummary{
				Value: []struct {
					CoverageType string                                           `json:"coverageType"`
					Summary      []oapi_uw.ApplicationReviewLossSummaryItemRecord `json:"summary"`
				}{
					{
						CoverageType: "Auto Liability",
						Summary: []oapi_uw.ApplicationReviewLossSummaryItemRecord{
							{
								PolicyPeriodStartDate: openapi_types.Date{
									Time: time.Date(2016, 1, 1, 0, 0, 0, 0, time.UTC),
								},
								LossIncurred: 1000,
								PolicyPeriodEndDate: openapi_types.Date{
									Time: time.Date(2016, 12, 31, 0, 0, 0, 0, time.UTC),
								},
							},
						},
					},
					{
						CoverageType: "Auto Physical Damage",
						Summary: []oapi_uw.ApplicationReviewLossSummaryItemRecord{
							{
								PolicyPeriodStartDate: openapi_types.Date{
									Time: time.Date(2016, 1, 1, 0, 0, 0, 0, time.UTC),
								},
								LossIncurred: 1000,
								PolicyPeriodEndDate: openapi_types.Date{
									Time: time.Date(2016, 12, 31, 0, 0, 0, 0, time.UTC),
								},
							},
						},
					},
					{
						CoverageType: "Motor Truck Cargo",
						Summary: []oapi_uw.ApplicationReviewLossSummaryItemRecord{
							{
								PolicyPeriodStartDate: openapi_types.Date{
									Time: time.Date(2016, 1, 1, 0, 0, 0, 0, time.UTC),
								},
								LossIncurred: 1000,
								PolicyPeriodEndDate: openapi_types.Date{
									Time: time.Date(2016, 12, 31, 0, 0, 0, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := buildLossYearMapFromLossSummary(tt.arg, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("buildLossYearMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("buildLossYearMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_buildLossYearMap_isRenewalSkips6thLoss(t *testing.T) {
	// Create 6 years of loss data
	var losses []oapi_uw.ApplicationReviewLossSummaryItemRecord
	for i := 0; i < 6; i++ {
		losses = append(losses, oapi_uw.ApplicationReviewLossSummaryItemRecord{
			PolicyPeriodStartDate: openapi_types.Date{
				Time: time.Date(2015+i, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			LossIncurred: int32((i + 1) * 1000),
			PolicyPeriodEndDate: openapi_types.Date{
				Time: time.Date(2015+i, 12, 31, 0, 0, 0, 0, time.UTC),
			},
		})
	}

	arg := &oapi_uw.ApplicationReviewLossSummary{
		Value: []struct {
			CoverageType string                                           `json:"coverageType"`
			Summary      []oapi_uw.ApplicationReviewLossSummaryItemRecord `json:"summary"`
		}{
			{
				CoverageType: "Auto Liability",
				Summary:      losses,
			},
		},
	}

	want := map[int]int32{
		2015: 1000,
		2016: 2000,
		2017: 3000,
		2018: 4000,
		2019: 5000,
	}

	got, err := buildLossYearMapFromLossSummary(arg, true)
	if err != nil {
		t.Errorf("buildLossYearMapFromLossSummary() error = %v, wantErr false", err)
		return
	}

	type lossRecord struct {
		Year   int
		Amount int32
	}

	toSlice := func(m lossYearMap) []lossRecord {
		var out []lossRecord
		for k, v := range m {
			out = append(out, lossRecord{k, v})
		}
		sort.Slice(out, func(i, j int) bool {
			return out[i].Year < (out[j].Year)
		})
		return out
	}

	gotSlice := toSlice(got)
	wantSlice := toSlice(want)

	if !reflect.DeepEqual(gotSlice, wantSlice) {
		t.Errorf("buildLossYearMapFromLossSummary() mismatch:\n got  = %#v\n want = %#v", gotSlice, wantSlice)
	}
}

func Test_getSortedYears(t *testing.T) {
	tests := []struct {
		name string
		args lossYearMap
		want []int
	}{
		{
			name: "Test Sorted Years",
			args: lossYearMap{
				2018: 1000,
				2016: 1000,
				2019: 1000,
				2017: 1000,
			},
			want: []int{
				2016,
				2017,
				2018,
				2019,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.args.getSortedYears(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSortedYears() = %v, want %v", got, tt.want)
			}
		})
	}
}
