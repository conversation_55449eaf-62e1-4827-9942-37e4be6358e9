package common_utils

import (
	"strings"

	"github.com/cockroachdb/errors"
	mstReferralDb "nirvanatech.com/nirvana/db-api/db_wrappers/uw/mst_referral"
)

// DetermineExistingReferralStatus determines the status based on the rules and UW overrides,
// it does not update (or transition) the status.
func DetermineExistingReferralStatus(
	ruleReview *mstReferralDb.ReferralAssessment,
	overrideRules []mstReferralDb.ReferralRuleReview,
) mstReferralDb.ReferralReviewStatus {
	isReferralRequired, isReferralRequiredOverrideByUWToInactive := IsReferralRequired(ruleReview.Rules, overrideRules)
	switch {
	//nolint:exhaustive
	case isReferralRequired:
		switch ruleReview.ReviewStatus {
		case mstReferralDb.ReferralReviewStatusUnspecified, mstReferralDb.ReferralReviewStatusReviewNotEligibleBySystem,
			mstReferralDb.ReferralReviewStatusReviewNotEligibleByUW:
			return mstReferralDb.ReferralReviewStatusReviewPending
		default:
			// retain the non-terminal status for any other status
			return ruleReview.ReviewStatus
		}
	// if referral is not required, let's determine whether this was determined by the system or UW override
	case isReferralRequiredOverrideByUWToInactive:
		return mstReferralDb.ReferralReviewStatusReviewNotEligibleByUW
	default:
		return mstReferralDb.ReferralReviewStatusReviewNotEligibleBySystem
	}
}

// IsReferralRequired determines whether referral is required based on the rules and UW overrides.
// It returns two boolean values:
//  1. isReferralRequired: whether referral is required based on the system and override rules
//  2. isReferralRequiredOverrideByUWToInactive: whether referral is required based on the rules
//     but UW has overridden at-least one of them to 'Inactive'
func IsReferralRequired(systemRules []mstReferralDb.ReferralRuleReview,
	overrideRules []mstReferralDb.ReferralRuleReview,
) (isReferralRequired, isReferralRequiredOverrideByUWToInactive bool) {
	overrideRuleMap := make(map[mstReferralDb.ReferralRuleId]mstReferralDb.ReferralRuleStatus)

	for _, rule := range overrideRules {
		overrideRuleMap[rule.Id] = rule.RuleStatus
	}

	for _, rule := range systemRules {
		overrideRuleStatus, ok := overrideRuleMap[rule.Id]
		if rule.RuleStatus != mstReferralDb.ReferralRuleStatusActive {
			// check if UW has overridden the 'inactive' rule to 'Active'
			if ok && overrideRuleStatus == mstReferralDb.ReferralRuleStatusActive {
				isReferralRequired = true
			}
			continue
		}

		switch rule.Type {
		case mstReferralDb.ReferralRuleTypeAutomatic:
			// if any automatic rule is active and not UW does not override it to 'Inactive', referral is required
			if ok && overrideRuleStatus == mstReferralDb.ReferralRuleStatusInactive {
				isReferralRequiredOverrideByUWToInactive = true
				continue
			}
			isReferralRequired = true
		case mstReferralDb.ReferralRuleTypeSemiAutomatic:
			if ok {
				// if any semi-automatic rule is active and override is present,
				// it does not necessarily mean referral is required
				// it depends on whether UW has overridden it to 'Active'/'Inactive'
				if overrideRuleStatus == mstReferralDb.ReferralRuleStatusActive {
					isReferralRequired = true
				} else if overrideRuleStatus == mstReferralDb.ReferralRuleStatusInactive {
					isReferralRequiredOverrideByUWToInactive = true
				}
			} else {
				// if any semi-automatic rule is active and UW has not overridden it, referral is required
				isReferralRequired = true
			}
		}
	}

	return isReferralRequired, isReferralRequiredOverrideByUWToInactive
}

func GetNirvanaCategoryForRuleId(id mstReferralDb.ReferralRuleId) (string, error) {
	switch id {
	case mstReferralDb.ReferralRuleIdAnySymbol1Or61:
		return "Symbols", nil
	case mstReferralDb.ReferralRuleIdAnyPolicyWithPolicyTermLongerThan12Months:
		return "Extended Policies", nil
	case mstReferralDb.ReferralRuleIdAnythingThatRequiresBackdatingAndDoesNotMeetTheRequirements:
		return "Backdating Policies", nil
	case mstReferralDb.ReferralRuleIdAnyClassOrExposureListedAsExcluded:
		return "Excluded Classes", nil
	case mstReferralDb.ReferralRuleIdAllRisksRequestingCSL:
		return "Auto Liability CSL", nil
	case mstReferralDb.ReferralRuleIdAccountsWithTwoOrMoreLosses:
		return "Large Losses", nil
	case mstReferralDb.ReferralRuleIdAnySingleClaimWithFatality:
		return "Fatalities", nil
	case mstReferralDb.ReferralRuleIdAnyRiskThatIsAlreadyOnTransversePaper:
		return "Incumbent MST", nil
	case mstReferralDb.ReferralRuleIdAllNewVentures:
		return "New Ventures", nil
	case mstReferralDb.ReferralRuleIdAnyRiskGeneratingAnUnmodifiedPremium:
		return "Premium Ceiling", nil
	case mstReferralDb.ReferralRuleIdAnyAccountWith3YearLossRatio:
		return "Loss Ratio", nil
	case mstReferralDb.ReferralRuleIdAnySubHaulExposure:
		return "Sub-Haul Exposure", nil
	case mstReferralDb.ReferralRuleIdLongHaulPriceForALCoverage:
		return "Long Haul AL Price", nil
	case mstReferralDb.ReferralRuleIdShortToMediumHaulPriceForALCoverage:
		return "Short/Medium Haul AL Price", nil
	case mstReferralDb.ReferralRuleIdAPDCoverageRateWithTIV:
		return "APD Rate", nil
	case mstReferralDb.ReferralRuleIdAPDCoverageDeductible:
		return "APD Deductible", nil
	default:
		return "", errors.Errorf("unknown referral rule id: %d", id)
	}
}

func GetMstReportCategoryForRuleId(id mstReferralDb.ReferralRuleId) (string, error) {
	switch id {
	case mstReferralDb.ReferralRuleIdAnySymbol1Or61:
		return "Coverage", nil
	case mstReferralDb.ReferralRuleIdAnyPolicyWithPolicyTermLongerThan12Months:
		return "Short/Extended Policy Period", nil
	case mstReferralDb.ReferralRuleIdAnythingThatRequiresBackdatingAndDoesNotMeetTheRequirements:
		return "Back Date (w/NKLL)", nil
	case mstReferralDb.ReferralRuleIdAnyClassOrExposureListedAsExcluded:
		return "Class of Business", nil
	case mstReferralDb.ReferralRuleIdAllRisksRequestingCSL:
		return "Limits", nil
	case mstReferralDb.ReferralRuleIdAccountsWithTwoOrMoreLosses:
		return "Loss Ratio/Claims", nil
	case mstReferralDb.ReferralRuleIdAnySingleClaimWithFatality:
		return "Loss Ratio/Claims", nil
	case mstReferralDb.ReferralRuleIdAnyRiskThatIsAlreadyOnTransversePaper:
		return "Other", nil
	case mstReferralDb.ReferralRuleIdAllNewVentures:
		return "Exposures", nil
	case mstReferralDb.ReferralRuleIdAnyRiskGeneratingAnUnmodifiedPremium:
		return "Premium Threshold", nil
	case mstReferralDb.ReferralRuleIdAnyAccountWith3YearLossRatio:
		return "Loss Ratio/Claims", nil
	case mstReferralDb.ReferralRuleIdAnySubHaulExposure:
		return "Exposures", nil
	case mstReferralDb.ReferralRuleIdLongHaulPriceForALCoverage:
		return "Rate/Price", nil
	case mstReferralDb.ReferralRuleIdShortToMediumHaulPriceForALCoverage:
		return "Rate/Price", nil
	case mstReferralDb.ReferralRuleIdAPDCoverageRateWithTIV:
		return "TIV Threshold", nil
	case mstReferralDb.ReferralRuleIdAPDCoverageDeductible:
		return "Deductible/SIR", nil
	default:
		return "", errors.Errorf("unknown referral rule id: %d", id)
	}
}

func GetCategoriesForActiveRules(
	systemRules, overrideRules []mstReferralDb.ReferralRuleReview,
	isMstReferralReportingCase bool,
) ([]string, error) {
	overrideRulesMap := make(map[mstReferralDb.ReferralRuleId]mstReferralDb.ReferralRuleStatus)
	for _, r := range overrideRules {
		overrideRulesMap[r.Id] = r.RuleStatus
	}

	getCategoryFunc := GetNirvanaCategoryForRuleId
	if isMstReferralReportingCase {
		getCategoryFunc = GetMstReportCategoryForRuleId
	}

	var categories []string

	for _, systemRule := range systemRules {
		overrideStatus, hasOverride := overrideRulesMap[systemRule.Id]

		isActive := false
		switch systemRule.Type {
		case mstReferralDb.ReferralRuleTypeAutomatic:
			ruleStatus := systemRule.RuleStatus
			if hasOverride {
				ruleStatus = overrideStatus
			}
			isActive = ruleStatus == mstReferralDb.ReferralRuleStatusActive
		case mstReferralDb.ReferralRuleTypeSemiAutomatic:
			isActive = hasOverride && overrideStatus == mstReferralDb.ReferralRuleStatusActive
		default:
			return nil, errors.Errorf("unknown rule type: %v", systemRule.Type)
		}

		if !isActive {
			continue
		}

		category, err := getCategoryFunc(systemRule.Id)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get category for rule id: %d", systemRule.Id)
		}
		categories = append(categories, category)
	}

	return categories, nil
}

// FormatListWithAnd formats a slice of strings into a human-readable list,
// joining them with commas and "and" before the last item. For example:
// ["a", "b", "c"] -> "a, b and c"
func FormatListWithAnd(items []string) string {
	switch len(items) {
	case 0:
		return ""
	case 1:
		return items[0]
	case 2:
		return items[0] + " and " + items[1]
	default:
		var b strings.Builder
		b.WriteString(items[0])

		for i := 1; i < len(items)-1; i++ {
			b.WriteString(", ")
			b.WriteString(items[i])
		}

		b.WriteString(" and ")
		b.WriteString(items[len(items)-1])

		return b.String()
	}
}
