package common_utils

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

var (
	terminalLocation1 = application.TerminalLocation{
		AddressLineOne: "original address 1",
		AddressLineTwo: pointer_utils.String("100"),
		IsGated:        false,
		ZipCode:        35004,
		ZipCodeString:  "35004",
		IsDeletedByUW:  false,
		IsCreatedByUW:  false,
		UsState:        us_states.AK,
		TypeOfTerminal: enums.TypeOfTerminalOffice,
		IsGuarded:      false,
	}
	terminalLocation2 = application.TerminalLocation{
		AddressLineOne: "original address 2",
		AddressLineTwo: pointer_utils.String("200"),
		IsGated:        false,
		ZipCode:        35004,
		ZipCodeString:  "35004",
		IsDeletedByUW:  true,
		IsCreatedByUW:  false,
		UsState:        us_states.AK,
		TypeOfTerminal: enums.TypeOfTerminalOffice,
		IsGuarded:      false,
	}
	overrideTerminalLocation1 = application.TerminalLocation{
		AddressLineOne: "original address 1",
		AddressLineTwo: pointer_utils.String("300"),
		IsGated:        false,
		ZipCode:        35004,
		ZipCodeString:  "35004",
		IsDeletedByUW:  false,
		IsCreatedByUW:  false,
		UsState:        us_states.AK,
		TypeOfTerminal: enums.TypeOfTerminalOffice,
		IsGuarded:      false,
	}
	overrideTerminalLocation2 = application.TerminalLocation{
		AddressLineOne: "original address 2",
		AddressLineTwo: pointer_utils.String("400"),
		IsGated:        false,
		ZipCode:        35004,
		ZipCodeString:  "35004",
		IsDeletedByUW:  true,
		IsCreatedByUW:  false,
		UsState:        us_states.AK,
		TypeOfTerminal: enums.TypeOfTerminalOffice,
		IsGuarded:      false,
	}

	addressWithAddressLineTwoNull = application.TerminalLocation{
		AddressLineOne: "original address 2",
		AddressLineTwo: nil,
		IsGated:        false,
		ZipCode:        35004,
		ZipCodeString:  "35004",
		IsDeletedByUW:  true,
		IsCreatedByUW:  false,
		UsState:        us_states.AK,
		TypeOfTerminal: enums.TypeOfTerminalOffice,
		IsGuarded:      false,
	}
)

func convertTerminalLocationToPatchRequest(terminalLocation application.TerminalLocation) oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItem {
	return oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItem{
		AddressLineOne: terminalLocation.AddressLineOne,
		AddressLineTwo: terminalLocation.AddressLineTwo,
		IsGated:        terminalLocation.IsGated,
		ZipCode:        terminalLocation.ZipCodeString,
		IsDeletedByUW:  &terminalLocation.IsDeletedByUW,
		IsCreatedByUW:  &terminalLocation.IsCreatedByUW,
		UsState:        terminalLocation.UsState.String(),
		IsGuarded:      terminalLocation.IsGuarded,
		Type:           oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItemType(str_utils.PrettyEnumString(terminalLocation.TypeOfTerminal.String(), "TypeOfTerminal")),
	}
}

func getSubmission(terminalLocations []application.TerminalLocation) application.SubmissionObject {
	submission := application.SubmissionObject{}
	companyInfo := application.CompanyInfo{
		TerminalLocations: &terminalLocations,
	}

	submission.CompanyInfo = &companyInfo
	return submission
}

func TestValidateLocations(t *testing.T) {
	tests := []struct {
		name        string
		args        []oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItem
		expectError bool
	}{
		{
			name:        "Test Validate Locations with one valid location",
			args:        []oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItem{convertTerminalLocationToPatchRequest(terminalLocation1)},
			expectError: false,
		},
		{
			name: "Test Validate Locations with two valid location",
			args: []oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItem{
				convertTerminalLocationToPatchRequest(terminalLocation1),
				convertTerminalLocationToPatchRequest(terminalLocation2),
			},
			expectError: false,
		},
		{
			name: "Test Validate Locations with two invalid location",
			args: []oapi_uw.UpdateApplicationReviewOperationsTerminalLocationItem{
				convertTerminalLocationToPatchRequest(terminalLocation1),
				convertTerminalLocationToPatchRequest(terminalLocation1),
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateLocations(tt.args)
			if tt.expectError && err == nil {
				t.Errorf("ValidateLocations() expected error, got nil")
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidateLocations() expected nil, got %v", err)
			}
		})
	}
}

func TestFilterDeletedTerminalLocations(t *testing.T) {
	tests := []struct {
		name string
		args *[]application.TerminalLocation
		want []application.TerminalLocation
	}{
		{
			name: "Test Filter Deleted Terminal Locations with one non deleted location",
			args: &[]application.TerminalLocation{terminalLocation1},
			want: []application.TerminalLocation{terminalLocation1},
		},
		{
			name: "Test Filter Deleted Terminal Locations with two non deleted location",
			args: &[]application.TerminalLocation{terminalLocation1, overrideTerminalLocation1},
			want: []application.TerminalLocation{terminalLocation1, overrideTerminalLocation1},
		},
		{
			name: "Test Filter Deleted Terminal Locations with one deleted location amd one non deleted location",
			args: &[]application.TerminalLocation{terminalLocation1, overrideTerminalLocation2},
			want: []application.TerminalLocation{terminalLocation1},
		},
		{
			name: "Test Filter Deleted Terminal Locations with both deleted location",
			args: &[]application.TerminalLocation{terminalLocation2, overrideTerminalLocation2},
			want: []application.TerminalLocation{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FilterDeletedTerminalLocations(tt.args)
			require.Equal(t, len(tt.want), len(result))
		})
	}
}

func TestGetEffectiveTerminalLocations(t *testing.T) {
	tests := []struct {
		name string
		args *uw.ApplicationReview
		want *[]application.TerminalLocation
	}{
		{
			name: "Test Get Effective Terminal Locations with two override terminal locations",
			args: &uw.ApplicationReview{
				Overrides: uw.Overrides{
					TerminalLocations: []application.TerminalLocation{overrideTerminalLocation1, overrideTerminalLocation2},
				},
				Submission: getSubmission([]application.TerminalLocation{terminalLocation1}),
			},
			want: &[]application.TerminalLocation{overrideTerminalLocation1, overrideTerminalLocation2},
		},
		{
			name: "Test Get Effective Terminal Locations with no override terminal locations (nil check)",
			args: &uw.ApplicationReview{
				Overrides: uw.Overrides{
					TerminalLocations: nil,
				},
				Submission: getSubmission([]application.TerminalLocation{terminalLocation1}),
			},
			want: &[]application.TerminalLocation{terminalLocation1},
		},
		{
			name: "Test Get Effective Terminal Locations with no override terminal locations",
			args: &uw.ApplicationReview{
				Overrides: uw.Overrides{
					TerminalLocations: []application.TerminalLocation{},
				},
				Submission: getSubmission([]application.TerminalLocation{terminalLocation1}),
			},
			want: &[]application.TerminalLocation{terminalLocation1},
		},
		{
			name: "Test Get Effective Terminal Locations with override terminal locations when original is empty",
			args: &uw.ApplicationReview{
				Overrides: uw.Overrides{
					TerminalLocations: []application.TerminalLocation{overrideTerminalLocation1},
				},
				Submission: getSubmission(nil),
			},
			want: &[]application.TerminalLocation{overrideTerminalLocation1},
		},
		{
			name: "Test Get Effective Terminal Locations with override terminal locations when original is nil",
			args: &uw.ApplicationReview{
				Overrides: uw.Overrides{
					TerminalLocations: []application.TerminalLocation{overrideTerminalLocation1},
				},
				Submission: getSubmission([]application.TerminalLocation{}),
			},
			want: &[]application.TerminalLocation{overrideTerminalLocation1},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetEffectiveTerminalLocations(tt.args)
			if result == nil {
				require.Equal(t, len(*tt.want), len(*result))
			}
		})
	}
}

func TestIsTerminalLocationValidForRating(t *testing.T) {
	type args struct {
		location    application.TerminalLocation
		companyInfo application.CompanyInfo
	}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test Is Terminal Location Valid For Rating when UsState is Same",
			args: args{
				location:    terminalLocation1,
				companyInfo: application.CompanyInfo{USState: us_states.AK},
			},
			want: true,
		},
		{
			name: "Test Is Terminal Location Valid For Rating when UsState is Different",
			args: args{
				location:    terminalLocation1,
				companyInfo: application.CompanyInfo{USState: us_states.AL},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsTerminalLocationValidForRating(tt.args.location, tt.args.companyInfo)
			require.Equal(t, tt.want, result)
		})
	}
}

func TestIsTerminalLocationSelected(t *testing.T) {
	type args struct {
		location  application.TerminalLocation
		overrides uw.Overrides
	}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test Is Terminal Location Selected with same address",
			args: args{
				location: terminalLocation1,
				overrides: uw.Overrides{
					RatingAddress: &terminalLocation1,
				},
			},
			want: true,
		},
		{
			name: "Test Is Terminal Location Selected with different address",
			args: args{
				location: terminalLocation1,
				overrides: uw.Overrides{
					RatingAddress: &overrideTerminalLocation2,
				},
			},
			want: false,
		},
		{
			name: "Test Is Terminal Location Selected when rating address is nil",
			args: args{
				location: terminalLocation1,
				overrides: uw.Overrides{
					RatingAddress: nil,
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsTerminalLocationSelected(tt.args.location, tt.args.overrides)
			require.Equal(t, tt.want, result)
		})
	}
}

func TestCompileTerminalAddress(t *testing.T) {
	type args struct {
		location application.TerminalLocation
	}

	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test CompileTerminalAddress when address line two is nil",
			args: args{
				location: addressWithAddressLineTwoNull,
			},
			want: "original address 2",
		},
		{
			name: "Test CompileTerminalAddress when address line two is not nil",
			args: args{
				location: terminalLocation1,
			},
			want: "original address 1 100",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompileTerminalAddress(tt.args.location)
			require.Equal(t, tt.want, result)
		})
	}
}

func TestGetRatingAddressFromOverridesUpdate(t *testing.T) {
	type args struct {
		reviewObj *uw.ApplicationReview
		finalLocs []application.TerminalLocation
	}

	type want struct {
		shouldUpdateRatingAddress bool
	}

	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "Test Get Rating Address From Overrides Update when rating address is being deleted (Address Line 1)",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						RatingAddress: &terminalLocation1,
					},
				},
				finalLocs: []application.TerminalLocation{terminalLocation2},
			},
			want: want{
				shouldUpdateRatingAddress: true,
			},
		},
		{
			name: "Test Get Rating Address From Overrides Update when rating is nil",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						RatingAddress: nil,
					},
				},
				finalLocs: []application.TerminalLocation{terminalLocation2},
			},
			want: want{
				shouldUpdateRatingAddress: false,
			},
		},
		{
			name: "Test Get Rating Address From Overrides Update when rating address is being edited (Addesss Line 2)",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						RatingAddress: &terminalLocation1,
					},
				},
				finalLocs: []application.TerminalLocation{overrideTerminalLocation1},
			},
			want: want{
				shouldUpdateRatingAddress: true,
			},
		},
		{
			name: "Test Get Rating Address From Overrides Update when rating address is neither being deleted or edited",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						RatingAddress: &terminalLocation1,
					},
				},
				finalLocs: []application.TerminalLocation{terminalLocation2, terminalLocation1},
			},
			want: want{
				shouldUpdateRatingAddress: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			shouldSetRatingAddressAsNil := ShouldSetRatingAddressAsNil(tt.args.reviewObj, tt.args.finalLocs)
			require.Equal(t, tt.want.shouldUpdateRatingAddress, shouldSetRatingAddressAsNil)
		})
	}
}

func TestShouldPreventTerminalLocationAddition(t *testing.T) {
	cargoTerminalSchedule := &application.CargoTerminalSchedule{
		ID: uuid.New(),
	}

	type args struct {
		reviewObj *uw.ApplicationReview
		finalLocs []application.TerminalLocation
	}

	type want struct {
		shouldPrevent bool
	}

	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "Should prevent when Cargo at Terminals coverage is enabled and terminal location lacks CargoTerminalSchedule",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						AncillaryCoverages: map[enums.Coverage]uw.AncillaryCoverage{
							enums.CoverageCargoAtScheduledTerminals: {
								CoverageType: enums.CoverageCargoAtScheduledTerminals,
								IsEnabled:    true,
							},
						},
					},
				},
				finalLocs: []application.TerminalLocation{
					{AddressLineOne: "Address 1", CargoTerminalSchedule: nil},
				},
			},
			want: want{
				shouldPrevent: true,
			},
		},
		{
			name: "Should allow when Cargo at Terminals coverage is enabled and all terminal locations have CargoTerminalSchedule",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						AncillaryCoverages: map[enums.Coverage]uw.AncillaryCoverage{
							enums.CoverageCargoAtScheduledTerminals: {
								CoverageType: enums.CoverageCargoAtScheduledTerminals,
								IsEnabled:    true,
							},
						},
					},
				},
				finalLocs: []application.TerminalLocation{
					{AddressLineOne: "Address 1", CargoTerminalSchedule: cargoTerminalSchedule},
					{AddressLineOne: "Address 2", CargoTerminalSchedule: cargoTerminalSchedule},
				},
			},
			want: want{
				shouldPrevent: false,
			},
		},
		{
			name: "Should allow when Cargo at Terminals coverage is not enabled even if CargoTerminalSchedule is missing",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						AncillaryCoverages: map[enums.Coverage]uw.AncillaryCoverage{},
					},
				},
				finalLocs: []application.TerminalLocation{
					{AddressLineOne: "Address 1", CargoTerminalSchedule: nil},
				},
			},
			want: want{
				shouldPrevent: false,
			},
		},
		{
			name: "Should allow when coverage exists but not enabled",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						AncillaryCoverages: map[enums.Coverage]uw.AncillaryCoverage{
							enums.CoverageCargoAtScheduledTerminals: {
								CoverageType: enums.CoverageCargoAtScheduledTerminals,
								IsEnabled:    false,
							},
						},
					},
				},
				finalLocs: []application.TerminalLocation{
					{AddressLineOne: "Address 1", CargoTerminalSchedule: nil},
				},
			},
			want: want{
				shouldPrevent: false,
			},
		},
		{
			name: "Should prevent when coverage is enabled and at least one location lacks CargoTerminalSchedule",
			args: args{
				reviewObj: &uw.ApplicationReview{
					Overrides: uw.Overrides{
						AncillaryCoverages: map[enums.Coverage]uw.AncillaryCoverage{
							enums.CoverageCargoAtScheduledTerminals: {
								CoverageType: enums.CoverageCargoAtScheduledTerminals,
								IsEnabled:    true,
							},
						},
					},
				},
				finalLocs: []application.TerminalLocation{
					{AddressLineOne: "Address 1", CargoTerminalSchedule: cargoTerminalSchedule},
					{AddressLineOne: "Address 2", CargoTerminalSchedule: nil},
				},
			},
			want: want{
				shouldPrevent: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ShouldPreventTerminalLocationAddition(tt.args.reviewObj, tt.args.finalLocs)
			require.Equal(t, tt.want.shouldPrevent, result)
		})
	}
}

func TestFindRatingAddressIndexInTerminalLocations(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name              string
		terminalLocations *[]application.TerminalLocation
		ratingAddress     *application.TerminalLocation
		expectedIndex     int
	}{
		{
			name:              "Nil terminal locations should return -1",
			terminalLocations: nil,
			ratingAddress:     &terminalLocation1,
			expectedIndex:     -1,
		},
		{
			name:              "Nil rating address should return -1",
			terminalLocations: &[]application.TerminalLocation{terminalLocation1, terminalLocation2},
			ratingAddress:     nil,
			expectedIndex:     -1,
		},
		{
			name:              "Rating address found at index 0",
			terminalLocations: &[]application.TerminalLocation{terminalLocation1, terminalLocation2},
			ratingAddress:     &terminalLocation1,
			expectedIndex:     0,
		},
		{
			name:              "Rating address found at index 1",
			terminalLocations: &[]application.TerminalLocation{terminalLocation1, terminalLocation2},
			ratingAddress:     &terminalLocation2,
			expectedIndex:     1,
		},
		{
			name:              "Rating address not found should return -1",
			terminalLocations: &[]application.TerminalLocation{terminalLocation1, terminalLocation2},
			ratingAddress: &application.TerminalLocation{
				AddressLineOne: "non-existent address",
				AddressLineTwo: pointer_utils.String("999"),
				IsGated:        false,
				ZipCode:        12345,
				ZipCodeString:  "12345",
				IsDeletedByUW:  false,
				IsCreatedByUW:  false,
				UsState:        us_states.CA,
				TypeOfTerminal: enums.TypeOfTerminalOffice,
				IsGuarded:      false,
			},
			expectedIndex: -1,
		},
		{
			name:              "Empty terminal locations slice should return -1",
			terminalLocations: &[]application.TerminalLocation{},
			ratingAddress:     &terminalLocation1,
			expectedIndex:     -1,
		},
		{
			name:              "Rating address with same AddressLineOne but different other fields should be found",
			terminalLocations: &[]application.TerminalLocation{terminalLocation1, terminalLocation2},
			ratingAddress: &application.TerminalLocation{
				AddressLineOne: "original address 1",                     // Same as terminalLocation1
				AddressLineTwo: pointer_utils.String("different line 2"), // Different from terminalLocation1
				IsGated:        true,                                     // Different from terminalLocation1
				ZipCode:        99999,                                    // Different from terminalLocation1
				ZipCodeString:  "99999",                                  // Different from terminalLocation1
				IsDeletedByUW:  true,                                     // Different from terminalLocation1
				IsCreatedByUW:  true,                                     // Different from terminalLocation1
				UsState:        us_states.CA,                             // Different from terminalLocation1
				TypeOfTerminal: enums.TypeOfTerminalDock,                 // Different from terminalLocation1
				IsGuarded:      true,                                     // Different from terminalLocation1
			},
			expectedIndex: 0, // Should still find it based on AddressLineOne match
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actualIndex := FindRatingAddressIndexInTerminalLocations(tt.terminalLocations, tt.ratingAddress)
			require.Equal(t, tt.expectedIndex, actualIndex)
		})
	}
}
