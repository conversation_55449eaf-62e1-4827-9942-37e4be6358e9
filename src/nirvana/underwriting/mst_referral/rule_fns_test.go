package mst_referral

import (
	"context"
	"reflect"
	"testing"
	"time"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"

	"go.uber.org/fx"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_evaluateRuleYIB(t *testing.T) {
	var env struct {
		fx.In

		*application_review_fixture.ApplicationReviewsFixture
		*Executor
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()

	type args struct {
		reviewObj uw.ApplicationReview
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "Test evaluateRuleYIB with 10 years",
			args: args{
				reviewObj: *env.ApplicationReviewsFixture.ApplicationReview,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := evaluateRuleYIB(ctx, env.Executor, &tt.args.reviewObj)
			if (err != nil) != tt.wantErr {
				t.Errorf("evaluateRuleYIB() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("evaluateRuleYIB() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_evaluateRuleLossSummary(t *testing.T) {
	var env struct {
		fx.In

		*application_review_fixture.ApplicationReviewsFixture
		AppWrapper application.DataWrapper
		*Executor
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()

	tests := []struct {
		name    string
		args    *application.LossInfo
		want    bool
		wantErr bool
	}{
		{
			name: "Test with two losses greater than 500k",
			args: &application.LossInfo{
				LossRunSummary: []application.LossRunSummaryPerCoverage{
					{
						CoverageType: app_enums.CoverageAutoLiability,
						Summary: []application.LossRunSummaryRecord{
							{
								LossIncurred:          11388,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-4, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-5, 0, 0),
							},
							{
								LossIncurred:          10667,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-3, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-4, 0, 0),
							},
							{
								LossIncurred:          300000,
								NumberOfClaims:        15,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-2, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-3, 0, 0),
							},
							{
								LossIncurred:          300000,
								NumberOfClaims:        3,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-1, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-2, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    0,
								PolicyPeriodEndDate:   time.Now(),
								PolicyPeriodStartDate: time.Now().AddDate(-1, 0, 0),
							},
						},
					},
					{
						CoverageType: app_enums.CoverageAutoPhysicalDamage,
						Summary: []application.LossRunSummaryRecord{
							{
								LossIncurred:          11388,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-4, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-5, 0, 0),
							},
							{
								LossIncurred:          10667,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-3, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-4, 0, 0),
							},
							{
								LossIncurred:          300000,
								NumberOfClaims:        15,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-2, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-3, 0, 0),
							},
							{
								LossIncurred:          300000,
								NumberOfClaims:        3,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-1, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-2, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    0,
								PolicyPeriodEndDate:   time.Now(),
								PolicyPeriodStartDate: time.Now().AddDate(-1, 0, 0),
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "Test with no losses greater than 500k",
			args: &application.LossInfo{
				LossRunSummary: []application.LossRunSummaryPerCoverage{
					{
						CoverageType: app_enums.CoverageAutoLiability,
						Summary: []application.LossRunSummaryRecord{
							{
								LossIncurred:          11388,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-4, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-5, 0, 0),
							},
							{
								LossIncurred:          10667,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-3, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-4, 0, 0),
							},
							{
								LossIncurred:          300000,
								NumberOfClaims:        15,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-2, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-3, 0, 0),
							},
							{
								LossIncurred:          300000,
								NumberOfClaims:        3,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-1, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-2, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    0,
								PolicyPeriodEndDate:   time.Now(),
								PolicyPeriodStartDate: time.Now().AddDate(-1, 0, 0),
							},
						},
					},
					{
						CoverageType: app_enums.CoverageAutoPhysicalDamage,
						Summary: []application.LossRunSummaryRecord{
							{
								LossIncurred:          11388,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-4, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-5, 0, 0),
							},
							{
								LossIncurred:          10667,
								NumberOfClaims:        2,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-3, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-4, 0, 0),
							},
							{
								LossIncurred:          100000,
								NumberOfClaims:        15,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-2, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-3, 0, 0),
							},
							{
								LossIncurred:          100000,
								NumberOfClaims:        3,
								NumberOfPowerUnits:    15,
								PolicyPeriodEndDate:   time.Now().AddDate(-1, 0, 0),
								PolicyPeriodStartDate: time.Now().AddDate(-2, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    0,
								PolicyPeriodEndDate:   time.Now(),
								PolicyPeriodStartDate: time.Now().AddDate(-1, 0, 0),
							},
						},
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := env.AppWrapper.UpdateSub(ctx,
				env.ApplicationReviewsFixture.ApplicationReview.Submission.ID,
				func(object application.SubmissionObject) (application.SubmissionObject, error) {
					object.LossInfo = tt.args
					return object, nil
				})
			if err != nil {
				t.Errorf("Failed to update submission with mock loss info")
			}
			got, err := evaluateRuleLossSummary(ctx, env.Executor, env.ApplicationReviewsFixture.ApplicationReview)
			if (err != nil) != tt.wantErr {
				t.Errorf("evaluateRuleLossSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("evaluateRuleLossSummary() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_evaluateRuleCrashRecord(t *testing.T) {
	var env struct {
		fx.In

		*fmcsa_fixture.FmcsaFixture // includes crash record data
		*application_review_fixture.ApplicationReviewsFixture
		*Executor
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()

	type args struct {
		reviewObj uw.ApplicationReview
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "Test with crash records",
			args: args{
				reviewObj: *env.ApplicationReviewsFixture.ApplicationReview,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := evaluateRuleCrashRecord(ctx, env.Executor, &tt.args.reviewObj)
			if (err != nil) != tt.wantErr {
				t.Errorf("evaluateRuleCrashRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("evaluateRuleCrashRecord() got = %v, want %v", got, tt.want)
			}
		})
	}
}
