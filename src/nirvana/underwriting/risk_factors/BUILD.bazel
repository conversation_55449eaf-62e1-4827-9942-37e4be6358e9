load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "risk_factors_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/underwriting/risk_factors",
    proto = "//proto/risk_factors:risk_factors_proto",
    visibility = ["//visibility:public"],
)

# keep
go_proto_link(
    name = "risk_factors_go_proto_link",
    dep = ":risk_factors_go_proto",
    version = "v1",
)

go_library(
    name = "risk_factors",
    srcs = [
        "fx.go",
        "impl.go",
        "manager.go",
        "manager_v2.go",
        "pricing_manager.go",
        "utils.go",
    ],
    embed = [":risk_factors_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/underwriting/risk_factors",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/grpc/libgrpc",
        "//nirvana/common-go/grpc/middleware",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/risk_factors",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "risk_factors_test",
    srcs = [
        "fx_test.go",
        "impl_test.go",
    ],
    embed = [":risk_factors"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/risk_factors",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/external_data_management/data_processing/vin/testing/testfixtures",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testfixtures/basic_fixture",
        "//nirvana/infra/fx/testfixtures/emailer_fixture",
        "//nirvana/infra/fx/testfixtures/feature_store_fixture",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testfixtures/lni_fixture",
        "//nirvana/infra/fx/testloader",
        "@com_github_google_uuid//:uuid",
        "@com_github_launchdarkly_go_sdk_common_v3//ldvalue",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
