package risk_factors

import (
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestRiskFactorsServiceClientProvision(t *testing.T) {
	var deps struct {
		fx.In
		RiskFactorsServiceClient RiskFactorsServiceClient
		FeatureFlag              feature_flag_lib.Client
		AuthWrapper              auth.DataWrapper
		ApplicationReviewWrapper uw.ApplicationReviewWrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	require.NotNil(t, deps.RiskFactorsServiceClient)
	require.NotNil(t, deps.FeatureFlag)
	require.NotNil(t, deps.AuthWrapper)
	require.NotNil(t, deps.ApplicationReviewWrapper)
}
