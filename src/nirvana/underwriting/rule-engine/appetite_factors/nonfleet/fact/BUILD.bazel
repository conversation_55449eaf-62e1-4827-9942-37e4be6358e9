load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fact",
    srcs = [
        "appetite_factor_constructor.go",
        "fact.go",
        "fact_constructor.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/nonfleet/fact",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/rule_engine",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/app_review_widget",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/fmcsa/basic",
        "//nirvana/infra/config",
        "//nirvana/nonfleet/underwriting_panels",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
