package review_readiness_state_machine

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"

	"nirvanatech.com/nirvana/underwriting/task/taskmanager"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
)

type ReviewReadinessStateMachineDeps struct {
	fx.In
	ApplicationReviewWrapper     uw.ApplicationReviewWrapper
	ApplicationWrapper           application.DataWrapper
	TspConnectionManager         *tsp_connections.TSPConnManager
	TelematicsDataPlatformClient telematicsv2.TelematicsPipelineManager
	Jobber                       quoting_jobber.Client
	ReviewReadinessTaskManager   taskmanager.TaskManager
	AuthWrapper                  auth.DataWrapper
	FeatureFlag                  feature_flag_lib.Client
}
