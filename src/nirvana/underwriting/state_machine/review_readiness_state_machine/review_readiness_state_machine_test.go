package review_readiness_state_machine

import (
	"context"
	"fmt"
	"testing"

	"nirvanatech.com/nirvana/infra/fx/testloader"
	mock_taskmanager "nirvanatech.com/nirvana/underwriting/task/taskmanager/mocks"

	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"

	"nirvanatech.com/nirvana/common-go/slice_utils"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"

	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"github.com/stretchr/testify/suite"
)

func TestAppReviewReadinessStateMachineTestSuite(t *testing.T) {
	suite.Run(t, new(testSuite))
}

type testSuite struct {
	suite.Suite
	AppReviewFixtures *application_review_fixture.ApplicationReviewsFixture
	fxapp             *fxtest.App
	deps              ReviewReadinessStateMachineDeps
	AppReviewWrapper  uw.ApplicationReviewWrapper
	AppWrapper        app.DataWrapper
	mockTaskManager   *mock_taskmanager.MockTaskManager
}

func (s *testSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *testSuite) SetupTest() {
	var env struct {
		fx.In
		Deps             ReviewReadinessStateMachineDeps
		AppReviewFixture *application_review_fixture.ApplicationReviewsFixture
		AppReviewWrapper uw.ApplicationReviewWrapper
		AppWrapper       app.DataWrapper
	}
	// inject mock for task manager so that we can easily control whether task workflow is enabled or not.

	mockTaskManagerCreator, mockTaskManager := mock_taskmanager.GetMockTaskManagerFxDecorator()

	s.fxapp = testloader.RequireStart(
		s.T(), &env,
		testloader.Use(fx.Decorate(mockTaskManagerCreator)),
	)
	s.Require().NotNil(mockTaskManager)
	s.mockTaskManager = mockTaskManager
	s.AppReviewFixtures = env.AppReviewFixture
	s.deps = env.Deps
	s.AppReviewWrapper = env.AppReviewWrapper
	s.AppWrapper = env.AppWrapper
}

func (s *testSuite) TestStateMachineTransitions() {
	ctx := context.Background()
	reviewId := s.AppReviewFixtures.ApplicationReview.Id

	validTransitions := []struct {
		initialState uw.AppReviewReadinessState
		finalState   uw.AppReviewReadinessState
		event        Event
	}{
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateReady,
			event:        EventTelematicsDataAvailable,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateReady,
			event:        EventNoHistoricalTelematicsData,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateError,
			event:        EventTelematicsConnectionFailed,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateError,
			event:        EventNotEnoughTelematicsData,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateError,
			event:        EventTelematicsReconnectionRequired,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateError,
			event:        EventConsentEmailReminderExhausted,
		},
		{
			initialState: uw.AppReviewReadinessStateReady,
			finalState:   uw.AppReviewReadinessStateNotReady,
			event:        EventTelematicsConsentOrReconsent,
		},
		{
			initialState: uw.AppReviewReadinessStateError,
			finalState:   uw.AppReviewReadinessStateNotReady,
			event:        EventTelematicsConsentOrReconsent,
		},
		{
			initialState: uw.AppReviewReadinessStateError,
			finalState:   uw.AppReviewReadinessStateReady,
			event:        EventTelematicsDataAvailable,
		},
		{
			initialState: uw.AppReviewReadinessStateError,
			finalState:   uw.AppReviewReadinessStateReady,
			event:        EventNoHistoricalTelematicsData,
		},

		// manual transitions
		{
			initialState: uw.AppReviewReadinessStateReady,
			finalState:   uw.AppReviewReadinessStateNotReady,
			event:        EventManualTransitionToNotReady,
		},
		{
			initialState: uw.AppReviewReadinessStateError,
			finalState:   uw.AppReviewReadinessStateNotReady,
			event:        EventManualTransitionToNotReady,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateReady,
			event:        EventManualTransitionToReady,
		},
		{
			initialState: uw.AppReviewReadinessStateError,
			finalState:   uw.AppReviewReadinessStateReady,
			event:        EventManualTransitionToReady,
		},
		{
			initialState: uw.AppReviewReadinessStateReady,
			finalState:   uw.AppReviewReadinessStateError,
			event:        EventManualTransitionToError,
		},
		{
			initialState: uw.AppReviewReadinessStateNotReady,
			finalState:   uw.AppReviewReadinessStateError,
			event:        EventManualTransitionToError,
		},
	}

	allEvents := EventValues()
	allStates := uw.AppReviewReadinessStateValues()

	// try all possible combinations of states and events
	for _, initialState := range allStates {
		for _, finalState := range allStates {
			for _, event := range allEvents {
				// check if the transition is valid
				isValid := false
				for _, validTransition := range validTransitions {
					if initialState == validTransition.initialState && finalState == validTransition.finalState && event == validTransition.event {
						isValid = true
						break
					}
				}

				if initialState == finalState {
					continue
				}

				isAllowedStr := "allowed"
				if !isValid {
					isAllowedStr = "not allowed"
				}

				testName := fmt.Sprintf("transition from %s to %s on event %s is %s", initialState.String(), finalState.String(), event.String(), isAllowedStr)
				s.T().Run(testName, func(t *testing.T) {
					// update app review readiness to initial state
					err := s.AppReviewWrapper.UpdateAppReview(ctx, reviewId, func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
						review.ReviewReadinessState = initialState
						return review, nil
					})
					s.Require().NoError(err)

					stateMachine, err := NewAppReviewReadinessStateMachine(ctx, s.deps, reviewId, initialState)
					s.Require().NoError(err)
					err = stateMachine.Event(ctx, event)
					s.Require().NoError(err)

					review, err := s.AppReviewWrapper.GetReview(ctx, reviewId)
					s.Require().NoError(err)

					if isValid {
						// verify state in db
						s.Require().Equal(finalState.String(), review.ReviewReadinessState.String())

						// verify transition log in DB
						transitions, err := s.AppReviewWrapper.GetReviewReadinessStateTransitions(ctx, reviewId)
						s.Require().NoError(err)

						filteredTransitions := slice_utils.Filter(transitions, func(t uw.AppReviewReadinessStateTransition) bool {
							return t.OldState == initialState.String() && t.NewState == finalState.String() && t.Event == event.String()
						})
						s.Require().GreaterOrEqual(len(filteredTransitions), 1)
					} else {
						// state should not change
						s.Require().NotEqual(finalState.String(), review.ReviewReadinessState.String())
					}
				})
			}
		}
	}
}

func (s *testSuite) TestStateMachineTransitionsShouldNotBeAllowedIfPolicyIsCreated() {
	ctx := context.Background()
	reviewId := s.AppReviewFixtures.ApplicationReview.Id
	review, err := s.AppReviewWrapper.GetReview(ctx, reviewId)
	s.Require().NoError(err)

	initialState := uw.AppReviewReadinessStateReady

	// update app review state
	err = s.AppReviewWrapper.UpdateAppReview(ctx, reviewId, func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
		review.ReviewReadinessState = initialState
		return review, nil
	})
	s.Require().NoError(err)

	// update app state to PolicyCreated
	err = s.AppWrapper.UpdateApp(ctx, review.ApplicationID, func(app app.Application) (app.Application, error) {
		app.State = app_state_enums.AppStatePolicyCreated
		return app, nil
	})
	s.Require().NoError(err)

	// try transition from Ready to Not-Ready state
	sm, err := NewAppReviewReadinessStateMachine(ctx, s.deps, reviewId, initialState)
	s.Require().NoError(err)
	err = sm.stateMachine.Event(ctx, EventTelematicsConsentOrReconsent.String(), []interface{}{reviewId}...)
	s.Require().Error(err)
	s.Require().ErrorContains(err, "cannot transition review readiness state machine when app is in policy created state")
}

func (s *testSuite) TestStateMachineTransitionsShouldNotBeAllowedIfTaskWorkflowIsEnabled() {
	s.mockTaskManager.EnableTaskWorkflow()
	ctx := context.Background()
	reviewId := s.AppReviewFixtures.ApplicationReview.Id

	// try transition
	sm, err := NewAppReviewReadinessStateMachine(ctx, s.deps, reviewId, uw.AppReviewReadinessStateNotReady)
	s.Require().NoError(err)
	err = sm.stateMachine.Event(ctx, EventTelematicsDataAvailable.String(), []interface{}{reviewId}...)
	s.Require().Error(err)
	s.Require().ErrorContains(err, "cannot transition review readiness state machine when task workflow is enabled")
}
