package state_machine

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
)

var InitialAppReviewState = uw.ApplicationReviewStatePending

// fetchAppReviewStateFromDb is a temporary function which fetches the state of the
// application review from the database. This function will be removed once the
// new states are persisted in the database.
func fetchAppReviewStateFromDb(ctx context.Context, deps UnderwritingWrapperDeps, id uuid.UUID) (*uw.ApplicationReviewState, error) {
	appReview, err := deps.ApplicationReviewWrapper.GetReview(ctx, id.String())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get app review %s", id)
	}
	return &appReview.State, nil
}
