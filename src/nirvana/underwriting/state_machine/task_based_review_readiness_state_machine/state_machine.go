package review_readiness_state_machine

import (
	"context"

	"nirvanatech.com/nirvana/common-go/log"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"

	"github.com/cockroachdb/errors"

	"github.com/looplab/fsm"
)

type AppReviewReadinessStateMachine struct {
	reviewID     string
	stateMachine *fsm.FSM
}

func NewAppReviewReadinessStateMachine(
	ctx context.Context,
	deps ReviewReadinessStateMachineDeps,
	reviewID string,
	currentState uw.AppReviewReadinessState,
) (*AppReviewReadinessStateMachine, error) {
	stateMachine, err := newFSM(ctx, deps, currentState)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create state machine for task based app review readiness")
	}
	return &AppReviewReadinessStateMachine{
		reviewID:     reviewID,
		stateMachine: stateMachine,
	}, nil
}

func (a *AppReviewReadinessStateMachine) Event(ctx context.Context, event Event, args ...interface{}) error {
	// always append review ID to the args by default
	newArgs := append([]interface{}{a.reviewID}, args...)

	err := a.stateMachine.Event(ctx, event.String(), newArgs...)
	if err != nil {
		var invalidEventError fsm.InvalidEventError
		var canceledError fsm.CanceledError
		if errors.As(err, &invalidEventError) {
			// InvalidEventError is returned when the event is not defined in the current state
			// we don't want to treat that as an error because if transition is not defined then state machine should remain in same state
			log.Info(ctx, "no transition defined for event on state, state machine unchanged",
				log.String("event", event.String()),
				log.String("state", a.stateMachine.Current()),
				log.Err(err),
			)
			return nil
		}
		if errors.As(err, &canceledError) {
			// CanceledError is returned when a callback have canceled a transition
			// we don't want to treat that as an error because if transition is canceled then state machine should remain in same state
			log.Warn(ctx, "transition canceled by callback, state machine unchanged",
				log.String("event", event.String()),
				log.String("state", a.stateMachine.Current()),
				log.Err(err),
			)
			return nil
		}
		return errors.Wrapf(err, "failed to trigger event for app review readiness, review ID %s", a.reviewID)
	}
	return nil
}

func newFSM(
	ctx context.Context,
	deps ReviewReadinessStateMachineDeps,
	currentState uw.AppReviewReadinessState,
) (stateMachine *fsm.FSM, err error) {
	transitionsMap, err := generateEventMap()
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate transition map for task based app review state machine")
	}
	callBacks, err := generateCallbackMap(deps)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate callback map for task based app review state machine")
	}
	stateMachine = fsm.NewFSM(currentState.String(), transitionsMap, callBacks)
	return stateMachine, nil
}
