load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "clients_management_proto",
    srcs = ["configs.proto"],
    strip_import_prefix = "/proto",
    visibility = ["//visibility:public"],
    deps = [
        "//proto/interceptors_management/method_specific_read_from_store_interceptor:method_specific_read_from_store_interceptor_proto",
        "//proto/interceptors_management/method_specific_write_to_store_interceptor:method_specific_write_to_store_interceptor_proto",
        "//proto/interceptors_management/read_from_store_interceptor:read_from_store_interceptor_proto",
        "//proto/interceptors_management/write_to_store_interceptor:write_to_store_interceptor_proto",
    ],
)
