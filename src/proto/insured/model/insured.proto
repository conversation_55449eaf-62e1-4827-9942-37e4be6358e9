syntax = "proto3";
package insured;

import "common/location.proto";
import "insurance_core/insured.proto";

option go_package = "nirvanatech.com/nirvana/insured/model";

message ContactInfo {
    string email = 1;
    string phone = 2;
}

message Insured {
    string id = 1;

    insurance_core.InsuredName name = 2;

    common.Address address = 3;

    ContactInfo contactInfo = 4;

    insurance_core.InsuredIdentifier externalIdentifier = 5;
}

message CreateInsuredRequest {
    // The insured entity to be created. The id field should be empty.
    // If an insured with the same externalIdentifier already exists, the API will return the id of the existing insured.
    Insured insured = 1;
}

message CreateInsuredResponse {
    // The id of the insured entity that was created/already existed.
    string id = 1;
    // If the insured entity already existed, this field will be set to true.
    bool insuredAlreadyExisted = 2;
}