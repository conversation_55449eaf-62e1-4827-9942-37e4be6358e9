syntax = "proto3";
package jobber;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

message ClusterIdResponse {
  string id = 1;
}

message JobRunId {
  string jobId = 1;
  int64 runId = 2;
}

message JobRun {
  JobRunId id = 1;
  bytes metadata = 2;
  bytes state = 3;
  string creatorUUID = 4;
}

message ManyJobRuns {
  repeated JobRun jobRuns = 1;
}

message AddJobRunRequest {
  string registryKey = 1;
  string jobIdSuffix = 2;
  bytes serMsg = 3;
  bytes metadata = 4;
  optional bytes schedule = 5;
  optional string CreatorUuid = 6;
}

message GetJobRunByCreatorUUIDRequest {
  string creatorUUID = 1;
}

message GetJobRunsByOwnerRequest {
  string owner = 1;
}

service Jobber {
  // This RPC would probably evolve into ClusterInfo RPC.
  rpc ClusterId(google.protobuf.Empty) returns (ClusterIdResponse) {}
  rpc AddJobRun(AddJobRunRequest) returns (JobRunId) {}
  rpc GetJobRun(JobRunId) returns (JobRun) {}
  rpc GetJobRunByCreatorUUID(GetJobRunByCreatorUUIDRequest) returns (JobRun) {}
  rpc GetJobRunsByOwner(GetJobRunsByOwnerRequest) returns (ManyJobRuns) {}
  rpc CancelJobRun(JobRunId) returns (google.protobuf.Empty) {}
  rpc KillJobRunUnsafe(JobRunId) returns (google.protobuf.Empty) {}
  rpc PickupJobRunIfPossible(JobRunId) returns (google.protobuf.Empty) {}
}

message AddProcessorRequest {
  // also contains first heartbeat
  ProcessorInfo processorInfo = 1;
}

message ResourceStatus {
  string type = 1;
  int64 total = 2;
  int64 freeApprox = 3;
  bool canDivergeInSystem = 4;
  int64 freeInSystemApprox = 5;
  int64 averageJobConsumption = 6;
}

message ResourcesStatus {
  repeated ResourceStatus resources = 1;
}

message AddProcessorResponse {
  ProcessorHeartbeatResponse heartbeatResponse = 1;
}

message ProcessorHeartbeatRequest {
  string id = 1;
  int64 seqNo = 2;
  google.protobuf.Timestamp sendTime = 3; 
  ResourcesStatus currentResourceStatus = 4;
  string clusterId = 6;
}

message ProcessorHeartbeatResponse {}

message AliveProcessorsRequest {
  string clusterId = 1;
}

message ProcessorInfo {
  string id = 1;
  string clusterId = 2;
  // deadOnNoHeartbeatInterval is the interval after which monitor considers 
  // a processor dead if no heartbeat is received.
  // This should be sligthly longer than the internal processor
  // interval use for this purpose to account for network delays and
  // clock drift etc.
  google.protobuf.Duration deadOnNoHeartbeatInterval = 3;
  ResourcesStatus currentResourceStatus = 4;
  ProcessorStatus processorStatus = 5;
  string address = 6;
  ProcessorHeartbeatRequest heartbeat = 7;
}

message AliveProcessorsResponse {
  repeated ProcessorInfo infos = 1;
}

message UpdateProcessorStatusRequest {
  string clusterId = 1;
  string id = 2;
  ProcessorStatus newStatus = 3;
}

message UpdateProcessorStatusResponse {
}

service Monitor {
  rpc AddProcessor(AddProcessorRequest) returns (AddProcessorResponse) {}
  rpc HeartbeatProcessor(ProcessorHeartbeatRequest) returns (ProcessorHeartbeatResponse) {}
  // AliveProcessors returns the list of running processors. This
  // running does not mean processors in running status, just that
  // they are alive and sending heartbeats.
  rpc AliveProcessors(AliveProcessorsRequest) returns (AliveProcessorsResponse) {}
  // UpdateProcessorStatus updates the status of the processor. If the
  // status is updated to STOPPED, we remove the processor from the
  // list of alive processors.
  rpc UpdateProcessorStatus(UpdateProcessorStatusRequest) returns (UpdateProcessorStatusResponse) {}
}

// CAUTION: Do not change the order of the enum values.
enum ProcessorStatus {
  // Initial state. Only transitions to RUNNING.
  NOT_STARTED = 0;
  // Processor is running normally. Only transitions to DRAINING.
  RUNNING = 1;
  // Processor is draining. Only transitions to STOPPED.
  // In the draining state, the processor is not running new jobs or recovery,
  // only finishing the ones that are already being processed.
  DRAINING = 2;
  // STOPPED is the final state.
  // In the stopped state, the processor:
  // - stops all background goroutines
  // - fails all Jobber RPCs with stopped processor error
  // - tells the monitor that it is dead
  STOPPED = 4;
}

message CurrentStatusRequest {
}

message DrainAndStopProcessorRequest {
  bool NonBlocking = 1;
}

message DrainAndStopProcessorResponse {
}

message UpdateJobRunPickupTimeRequest {
  JobRunId jobRunId = 1;
  google.protobuf.Timestamp newPickupTime = 2;
}

message UpdateJobRunPickupTimeResponse {
}

service ProcessorInternal {
  // GetCurrentStatus returns the current status of the processor along with some other details.
  rpc GetCurrentStatus(CurrentStatusRequest) returns (ProcessorInfo) {}
  // DrainAndStopProcessor transitions the processor to the stopped state after draining it.
  // If NonBlocking is false, the call blocks until the processor is stoppe else it returns immediately
  // and draining and transition to stopped happens in background.
  // This call is indempotent and thus can be also used to wait until processor is stopped.
  rpc DrainAndStopProcessor(DrainAndStopProcessorRequest) returns (DrainAndStopProcessorResponse) {}
  // UpdateJobRunPickupTime changes the pickup time of a job run.
  rpc UpdateJobRunPickupTime(UpdateJobRunPickupTimeRequest) returns (UpdateJobRunPickupTimeResponse) {}
}
