syntax = "proto3";
package pdfgen;

import "google/protobuf/duration.proto";

message GeneratePDFRequest {
  string template = 1;
  bytes data = 2;
  google.protobuf.Duration linkExpiry = 3;
  string userPassword = 4;
  string ownerPassword = 5;
  string fileName = 6;
  bool useReactPdf = 7;
}

message GeneratePDFResponse {
  string downloadLink = 1;
  string handleUuid = 2;
}

service PDFGen {
  rpc Generate(GeneratePDFRequest) returns (GeneratePDFResponse) {}
}
