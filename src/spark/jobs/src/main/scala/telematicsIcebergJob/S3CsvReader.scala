package telematicsIcebergJob

import org.apache.hadoop.fs.{FileStatus, Path}
import org.apache.spark.sql.execution.datasources.{
  HadoopFsRelation,
  InMemoryFileIndex
}
import org.apache.spark.sql.execution.datasources.csv.CSVFileFormat
import org.apache.spark.sql.types.StructType
import org.apache.spark.sql.{DataFrame, SparkSession}

import scala.collection.mutable

case class S3InventoryView(s3_uri: String, size: Long)

trait S3CsvReader {
  def readCsvs(
      spark: SparkSession,
      inventory: List[S3InventoryView],
      dataSchema: StructType
  ): DataFrame
}

/** DefaultS3CsvReader simply uses spark's in-built csv reader
  */
object DefaultS3CsvReader extends S3CsvReader {
  override def readCsvs(
      spark: SparkSession,
      inventory: List[S3InventoryView],
      dataSchema: StructType
  ): DataFrame = {
    spark.read
      .option("header", "true")
      .schema(dataSchema)
      .csv(inventory.map(_.s3_uri): _*)
  }
}

/** OptimisedS3CsvReader hooks into spark internals to make reading a very large
  * number of csvs from s3 more efficient. Internally, what spark does on
  * `spark.read.csv(...)` is that for every s3 path provided it performs a HEAD
  * API call to S3 to fetch stats on the path such as its size, whether its a
  * directory or not. This leads to a large number of unwanted API calls to S3.
  * Since our source of files comes from S3 inventory directly, these details
  * are actually known upfront and we would to hint spark to not make
  * unnecessary API calls. Unfortunately spark does not provide a public config
  * option to do that. We are thus forced to hook into internals of spark to
  * make it work as proposed. This is a fragile approach and may break with
  * spark version upgrades.
  */
object OptimisedS3CsvReader extends S3CsvReader {

  /** A custom FileIndex that bypasses filesystem listing by using a
    * pre-existing list of FileStatus objects.
    */
  private class PreloadedFileIndex(
      sparkSession: SparkSession,
      preloadedFileStatuses: Seq[FileStatus]
  ) extends InMemoryFileIndex(
        sparkSession,
        rootPathsSpecified = preloadedFileStatuses.map(_.getPath),
        parameters = Map.empty,
        userSpecifiedSchema = None
      ) {

    // Override the method that performs the file listing
    override def listLeafFiles(
        paths: Seq[Path]
    ): mutable.LinkedHashSet[FileStatus] = {
      // Ignore the input paths and return our preloaded list instead
      preloadedFileStatuses.to
    }
  }

  override def readCsvs(
      spark: SparkSession,
      inventory: List[S3InventoryView],
      dataSchema: StructType
  ): DataFrame = {

    val hadoopFileStatuses = inventory.map(path => {
      new FileStatus(
        /* length */ path.size,
        /* isdir */ false,
        /* block_replication */ 1,
        /* blocksize */ 128 * 1024 * 1024, // some reasonable default
        /* modification_time */ 0L,
        new Path(path.s3_uri)
      )
    })

    val preloadedFileIndex = new PreloadedFileIndex(spark, hadoopFileStatuses)
    val hadoopFsRelation = HadoopFsRelation(
      location = preloadedFileIndex,
      partitionSchema = new StructType(),
      dataSchema = dataSchema,
      bucketSpec = None,
      fileFormat = new CSVFileFormat(),
      options = Map(
        "header" -> "true"
      )
    )(spark)

    spark.sqlContext.baseRelationToDataFrame(hadoopFsRelation)
  }
}
